[1/10] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x5220 bytes. 0x2de0 bytes (36%) free.


[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/uart_handler.c.obj
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/uart_handler.c: In function 'uart_read_state_machine':
D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/main/Src/uart_handler.c:166:42: warning: unused variable 'payload_len' [-Wunused-variable]
  166 |                                 uint32_t payload_len = byte_array_to_u32_little_endian(&data[2]);
      |                                          ^~~~~~~~~~~
[5/10] Linking C static library esp-idf\main\libmain.a
[6/10] Generating ld/sections.ld
[7/10] Linking CXX executable Vantage_nxESP32_Int_Rel_1_2_0_0.elf
[8/10] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32s3 image...

Merged 2 ELF sections

Successfully created esp32s3 image.

Generated D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_0_0.bin
[9/10] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/partition_table/partition-table.bin D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_0_0.bin"
Vantage_nxESP32_Int_Rel_1_2_0_0.bin binary size 0x94dd0 bytes. Smallest app partition is 0x100000 bytes. 0x6b230 bytes (42%) free.

[9/10] C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.4.1\esp-idf\components\esptool_py && C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.4.1/esp-idf -D SERIAL_TOOL=C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build -P C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32s3 -p COM33 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size detect 0x0 bootloader/bootloader.bin 0x10000 Vantage_nxESP32_Int_Rel_1_2_0_0.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port COM33
Connecting....
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Unknown Embedded PSRAM (AP_1v8)
Crystal is 40MHz
MAC: b4:3a:45:f7:39:08
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Auto-detected Flash size: 32MB
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x000a4fff...
Flash will be erased from 0x00008000 to 0x00008fff...
Flash params set to 0x025f
SHA digest in image updated
Compressed 21024 bytes to 13384...
Writing at 0x00000000... (100 %)
Wrote 21024 bytes (13384 compressed) at 0x00000000 in 0.6 seconds (effective 286.7 kbit/s)...
Hash of data verified.
Compressed 609744 bytes to 358800...
Writing at 0x00010000... (4 %)
Writing at 0x0001c983... (9 %)
Writing at 0x00026684... (13 %)
Writing at 0x0002f6af... (18 %)
Writing at 0x0003551c... (22 %)
Writing at 0x0003bd73... (27 %)
Writing at 0x00041f69... (31 %)
Writing at 0x00047c87... (36 %)
Writing at 0x0004dcf8... (40 %)
Writing at 0x00053d57... (45 %)
Writing at 0x00059ba4... (50 %)
Writing at 0x0005f62a... (54 %)
Writing at 0x00064f05... (59 %)
Writing at 0x0006aee9... (63 %)
Writing at 0x00070cee... (68 %)
Writing at 0x000771dc... (72 %)
Writing at 0x0007c952... (77 %)
Writing at 0x00085020... (81 %)
Writing at 0x0008d759... (86 %)
Writing at 0x000930cf... (90 %)
Writing at 0x00098cb5... (95 %)
Writing at 0x0009ee06... (100 %)
Wrote 609744 bytes (358800 compressed) at 0x00010000 in 9.9 seconds (effective 494.0 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 118...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (118 compressed) at 0x00008000 in 0.1 seconds (effective 467.9 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
