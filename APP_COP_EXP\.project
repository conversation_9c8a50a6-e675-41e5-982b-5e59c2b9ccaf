<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>APP_COP_EXP</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.st.stm32cube.ide.mcu.MCUProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeProjectNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeIdeServicesRevAev2ProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUAdvancedStructureProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUSingleCpuProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCURootProjectNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Lib_Network</name>
			<type>2</type>
			<locationURI>WORKSPACE_LOC/Lib_Network</locationURI>
		</link>
		<link>
			<name>Lib_System</name>
			<type>2</type>
			<locationURI>WORKSPACE_LOC/Lib_System</locationURI>
		</link>
		<link>
			<name>ThirdParty</name>
			<type>2</type>
			<locationURI>WORKSPACE_LOC/ThirdParty</locationURI>
		</link>
	</linkedResources>
</projectDescription>
