/*******************************************************************************
* @Copyright (C) 2022 by Vantage Elevation
* @file           : app.h
* @version 		  : 1.0.0
* @brief          : Main program body
* @details		  : Main program body declarations
********************************************************************************/
#ifndef _APP_H_
#define _APP_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include "FreeRTOS.h"
#include "task.h"
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/


/*******************************************************************************
* Macros
********************************************************************************/
#define FAULT_ALARM_TASK_PRIORITY   (tskIDLE_PRIORITY)
#define NTS_TASK_PRIORITY			      (tskIDLE_PRIORITY)
#define PARAMETERS_TASK_PRIORITY	  (tskIDLE_PRIORITY)
#define POSITION_TASK_PRIORITY		  (tskIDLE_PRIORITY)
#define CAN1_TASK_PRIORITY			    (tskIDLE_PRIORITY)
#define CAN2_TASK_PRIORITY		      (tskIDLE_PRIORITY)
#define CARNET_TASK_PRIORITY	      (tskIDLE_PRIORITY)
#define SPIAB_TASK_PRIORITY		      (tskIDLE_PRIORITY)
#define FPGA_TASK_PRIORITY		      (tskIDLE_PRIORITY)
#define UART_FIXTURE_TASK_PRIORITY	(tskIDLE_PRIORITY)
#define FIXTURE_NET_PRIORITY		    (tskIDLE_PRIORITY)
#define LEARN_TASK_PRIORITY         (tskIDLE_PRIORITY)
#define SYSTEM_CHECK_TASK_PRIORITY  (tskIDLE_PRIORITY)
#define LW_INTERFACE_PRIORITY         (tskIDLE_PRIORITY)

#define FAULT_ALARM_TASK_STACK_SIZE   (128)
#define LED_TASK_STACK_SIZE			      (128)
#define NTS_TASK_STACK_SIZE			      (256)
#define PARAMETERS_TASK_STACK_SIZE    (128)
#define POSITION_TASK_STACK_SIZE	    (128)
#define CAN1_TASK_STACK_SIZE		      (128)
#define CAN2_TASK_STACK_SIZE		      (128)
#define CARNET_TASK_STACK_SIZE		    (256)
#define SPIAB_TASK_STACK_SIZE		      (128)
#define FPGA_TASK_STACK_SIZE		      (128)
#define UART_FIXTURE_TASK_STACK_SIZE  (128)
#define FIXTURE_NET_TASK_STACK_SIZE	  (128)
#define LEARN_TASK_STACK_SIZE         (256)
#define SYSTEM_CHECK_TASK_STACK_SIZE  (128)
#define LW_INTERFACE_TASK_STACK_SIZE  (256)

/* Task delays */
#define TASK_IDLE_DELAY					100
#define FAULT_TASK_DELAY				50
#define NTS_TASK_DELAY					100
#define POSITION_TASK_DELAY			2
#define CAR_NET_TASK_DELAY			5
#define CAN1_TASK_DELAY					3
#define CAN2_TASK_DELAY					30
#define SPIAB_TASK_DELAY				5
#define PARAMETERS_TASK_DELAY	  10
#define FPGA_TASK_DELAY					20
#define UART_FIXTURE_TASK_DELAY 5
#define FIXTURE_NET_TASK_DELAY  10
#define LEARN_TASK_DELAY        100
#define SYSTEM_CHECK_TASK_DELAY 900
#define LW_INTERFACE_TASK_DELAY        100

/* Idle task period tasks*/
#define LED_TICKS_PERIOD				    100
#define LOW_PRIORITY_TICKS_PERIOD		3000
#define RTC_TICKS_PERIOD				    1000
#define WATCHDOG_TICKS_PERIOD			  500

/* Watchdog task flag positions */
#define FAULT_TASK_WATCHDOG_BIT				0
#define NTS_TASK_WATCHDOG_BIT				  1
#define POSITION_TASK_WATCHDOG_BIT		2
#define CAR_NET_TASK_WATCHDOG_BIT			3
#define CAN1_TASK_WATCHDOG_BIT				4
#define CAN2_TASK_WATCHDOG_BIT				5
#define SPIAB_TASK_WATCHDOG_BIT				6
#define PARAMETERS_TASK_WATCHDOG_BIT	7


/*******************************************************************************
* Typedefs
********************************************************************************/
typedef struct
{
	TaskFunction_t function;
	const char * const name;
	const size_t stack_size;
	void * const parameters;
	UBaseType_t priority;
    StackType_t * const stack_uffer;
    StaticTask_t * const task_buffer;
}TaskParams_t;

/*******************************************************************************
* Global Variables
********************************************************************************/


/*******************************************************************************
* Function Prototypes
********************************************************************************/
void vIdleTaskModulesInit(void);
#endif /* _APP_H_ */
