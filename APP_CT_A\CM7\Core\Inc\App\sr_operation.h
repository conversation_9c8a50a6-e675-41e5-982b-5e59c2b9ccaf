/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_operation.h
* @version 		  : 1.0.0
* @brief This subroutine updates the car's floor
* @details This subroutine updates the car's floor
*****************************************************************************/
#ifndef _SR_OPERATION_H_
#define _SR_OPERATION_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include <stdint.h>
#include "system.h"
#include "position_def.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void vOperation_Task(void *pvParameters);
#endif /* _SR_OPERATION_H_ */
