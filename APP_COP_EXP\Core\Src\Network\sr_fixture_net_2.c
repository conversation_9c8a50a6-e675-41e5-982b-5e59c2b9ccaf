/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file sr_fixture_net_2.c
* @version 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "sr_fixture_net_2.h"
#include "motion_def.h"
#include "fixture_def.h"
#include "car_input_def.h"
#include "car_output_def.h"
#include "param_def.h"
#include "hall_call_def.h"
#include "app_can.h"
#include "floor_def.h"
#include "operation_def.h"
#include "dg_car_ct_a7.h"
#include "dg_car_mr_a7.h"
#include "main.h"

/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

#define MAD_RUN_INTERVAL_1MS							  		  (100)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef struct
{
	uint16_t uwRunDelay_1ms;
	en_fixture_net_2_state eState;
} st_fixture_net_2_control;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
uint16_t gFixtureNet2_Task_Delay = 0;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_fixture_net_2_control stControl;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @brief returns the state of the fixture_net subroutine */
en_fixture_net_2_state Fixture_Net_2_GetState(void)
{
	return stControl.eState;
}

/* @fn void FixtureNet2_Task_Init(void)
 * @brief Initializes the FixtureNet2 task delay based on the local board type.
 *        This function sets the task delay for FixtureNet2 depending on the board type:
 *        - For `BOARD_COP`, it sets the delay to a predefined periodic delay constant.
 *        - For other board types, it sets the delay to an inactive task interval.
 * @param None
 * @return None
 */
void FixtureNet2_Task_Init(void)
{
	switch(Main_GetLocalBoardType())
	{
		case BOARD_COP:
			gFixtureNet2_Task_Delay = FIXTURE_NET2_TASK_PERIODIC_DELAY_MS;
			break;
		default:
			gFixtureNet2_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;
			break;
	}
}

/* @fn void vFixtureNet2_Task(void *pvParams)
 * @brief FixtureNet2 task that handles communication and state transitions for FixtureNet2.
 *        This task operates in two states: INIT and RUNNING_MAD.
 *        In INIT state, it checks the configuration and initializes CAN3 for communication if needed.
 *        In RUNNING_MAD state, it sends MAD messages at a specified interval.
 *        The task continues running as long as the parameter state is not in STARTUP.
 * @param pvParams: Pointer to task parameters (not used in this function).
 * @return None
 */
void vFixtureNet2_Task(void *pvParams)
{
   // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
  /*
    * do
    * {
    *  vTaskDelay(10);
    * }while(Param_GetState() == PARAM_STATE__STARTUP);
    */

    while(1){

       // Only proceed if the parameter state is not in STARTUP
       if( Param_GetState() != PARAM_STATE__STARTUP )
       {
          switch(stControl.eState)
          {
             case FIXTURE_NET_2_STATE__INIT:
                // Check if the configuration matches and initialize CAN3 for FixtureNet2 if needed
                if( Param_ReadValue_8Bit(CPARAM8__Fixture_COP_Aux_CAN3) == CAN_AUX__MAD )
                {
                   CAN3_Init_FIXTURE_125K();
                   stControl.eState = FIXTURE_NET_2_STATE__RUNNING_MAD;
                }
                break;

             case FIXTURE_NET_2_STATE__RUNNING_MAD:
                 // If the running delay has reached the threshold, send MAD messages
                 if(stControl.uwRunDelay_1ms >= MAD_RUN_INTERVAL_1MS)
                 {
                     st_CAN_msg_8B stTxMsg;
                     stTxMsg.uiID = 0x594;  // Message ID
                     stTxMsg.ucDLC = 8;     // Data Length Code
                     stTxMsg.bExtendedID = 0;  // Standard ID
                     stTxMsg.bCAN_FD = 0;      // Normal CAN (not CAN FD)

                     // Build and send the MAD CAN message
                     Fixture_Build_Message_MAD_CAN(&stTxMsg);
                     CAN3_LoadToRB_FIXTURE_125K(&stTxMsg);
                     stControl.uwRunDelay_1ms = 0;  // Reset the delay counter
                 }
                 else
                 {
                     // Increment the delay counter if the message isn't due yet
                     stControl.uwRunDelay_1ms += gFixtureNet2_Task_Delay;
                 }
                break;
             default:
                 break;
          }
       }
       vTaskDelay(gFixtureNet2_Task_Delay);
    }
}

