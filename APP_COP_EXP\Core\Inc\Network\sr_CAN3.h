/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file           : sr_CAN3.h
* @version        : 1.0.0
* @brief          : CAN1 subroutine
* @details        : CAN1 subroutine
********************************************************************************/
#ifndef _SR_CAN3_H_
#define _SR_CAN3_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "network.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_pass_fail CAN3_TransmitDatagram(st_CAN_msg *pstMsg);
en_active_inactive CAN3_CheckDestination(uint32_t uiDestinationBitmap);
uint8_t CAN3_CheckIfCommLoss(void);
en_active_inactive CAN3_HallBoardFuncOnBus(uint8_t ucFunc);
void CAN3_Task_Init(void);
void vCAN3_Task(void *pvParams);
#endif /* _SR_CAN3_H_ */
