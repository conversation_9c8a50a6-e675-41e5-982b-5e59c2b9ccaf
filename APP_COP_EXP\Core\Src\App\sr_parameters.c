/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_parameters.c
* @version 		  : 1.0.0
* @brief Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
* @details Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_parameters.h"
#include "main.h"
#include "car_faults_def.h"
#include "motion_def.h"
#include "param_def.h"
#include "position_def.h"
#include "floor_def.h"
#include "dg_car_mr_a7.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
uint16_t gParameters_Task_Delay = 0;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/

/* @fn void Parameters_Task_Init(void)
 * @brief Initializes the Parameters task by setting the task delay based on the local board type
 * @param None
 * @return None
 */
void Parameters_Task_Init(void)
{
	switch(Main_GetLocalBoardType())
	{
		case BOARD_COP:
			gParameters_Task_Delay = PARAMETERS_TASK_PERIODIC_DELAY_MS;
			break;
		case BOARD_EXP_MASTER:
		case BOARD_EXP_SLAVE:
		case BOARD_RISER:
		case BOARD_INVALID:
		default:
			gParameters_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;
		break;
	}
}

/* @fn void vParameters_Task(void *pvParams)
 * @brief Handles parameter tasks, including running parameters, updating floor positions,
 *        checking motion status, and setting faults based on conditions
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vParameters_Task(void *pvParams)
{
    // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    /*
     * do
     * {
     *  vTaskDelay(10);
     * }while(Param_GetState() == PARAM_STATE__STARTUP);
     */

    while(1) {
        // Run parameter processing
        Param_Run();

        // If the parameter state is not startup, update floor positions and set floors

        // TODO: We could change the logic if this to wait at the beginning of the task
        if(Param_GetState() != PARAM_STATE__STARTUP)
        {
            Floor_UpdateFloorPositions();
            if(Position_GetState() == POSITION_STATE__KNOWN)
            {
                // Set the current and destination floors
                Operation_SetCurrentFloor(Floor_GetFloorIndex(Position_GetPosition_05mm()));
                Operation_SetDestinationFloor(Floor_GetFloorIndex(Motion_GetDestination_05mm_MRA()));
            }
        }
        // If motion is not running and either startup or not all parameters are updated, set fault
	   if( ( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) )
	    && ( ( Param_GetState() == PARAM_STATE__STARTUP )
	      || ( Param_GetBlockToUpdate() != NUM_PARAM_BLOCKS ) ) )
        {
            Faults_SetFault(CFLT__PARAMETER_SYNC_MR_A4 + System_GetNodeID());
        }
            vTaskDelay(gParameters_Task_Delay); // Delay using the configured Parameters task period
    }
}

