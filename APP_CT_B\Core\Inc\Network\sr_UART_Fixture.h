/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file sr_UART_Fixture.h
* @version 1.0.0
* @brief This subroutine loads/unloads data for transmission on UART1 which connects the MCUB to fixtures.
* @details This subroutine loads/unloads data for transmission on UART1 which connects the MCUB to fixtures.
*****************************************************************************/
#ifndef _SR_UART_FIXTURE_H_
#define _SR_UART_FIXTURE_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "app.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/
typedef enum
{
   UART_FIXTURE_STATE__INIT,      /* Waiting for parameter initialization, before initializing the UART peripheral at the appropriate rate */
   UART_FIXTURE_STATE__RUNNING_CE_MICROCOMM, /* Initialized and sending messages */
   UART_FIXTURE_STATE__RUNNING_DL20,   		 /* Initialized and sending messages */
   UART_FIXTURE_STATE__RUNNING_EX51,   		 /* Initialized and sending messages */

   NUM_UART_FIXTURE_STATES
}en_uart_fixture_state;
/******************************************************************************
* Global Variables
*******************************************************************************/
//extern st_subroutine gstSR_UART_Fixture;
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_uart_fixture_state UART_Fixture_GetState(void);

void vUART_Fixture_Task(void *pvParameters);

#endif /* _SR_UART_FIXTURE_H_ */
