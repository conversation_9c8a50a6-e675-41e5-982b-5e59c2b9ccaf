/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_LED.c
* @version 		  : 1.0.0
* @brief This subroutine updates on board LEDs
* @details This subroutine updates on board LEDs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_LED.h"
#include "sr_RTC.h"
#include "sr_Watchdog.h"
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

typedef void(*IdleTaskFunction_t)(void);
typedef struct
{
	IdleTaskFunction_t function;
	TickType_t ticks;
	const TickType_t ticks_period;
}IdleTaskModule_t;

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

IdleTaskModule_t IdleTaskModules[] =
{
		{vLED_Update,		     0, LED_TICKS_PERIOD},
		{vRTC_Update,            0, RTC_TICKS_PERIOD},
		{vWatchdog_update,		 0, WATCHDOG_TICKS_PERIOD}
};
const uint32_t IdleTaskModuleCount = sizeof(IdleTaskModules)/sizeof(IdleTaskModule_t);

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn void vIdleTaskModulesInit(void)
 * @brief Initializes the idle task modules by setting the initial tick count for each task.
 * @param None
 * @return None
 */

void vIdleTaskModulesInit(void)
{
	for(uint32_t i = 0; i < IdleTaskModuleCount; i++)
	{
		IdleTaskModule_t *module = &IdleTaskModules[i];
		module->ticks = xTaskGetTickCount();
	}
}
/* @fn void vApplicationIdleHook(void)
 * @brief Application idle hook function executes from idle task. This function is called
 *  during the idle time of the RTOS scheduler. It checks the elapsed  time for
 *  each idle task and executes the corresponding function when the task’s time period has elapsed.
 * @param None
 * @return None
 */

void vApplicationIdleHook(void)
{
	for(uint32_t i = 0; i < IdleTaskModuleCount; i++)
	{
		IdleTaskModule_t *module = &IdleTaskModules[i];
		if((xTaskGetTickCount() - module->ticks) > module->ticks_period)
		{
			module->ticks = xTaskGetTickCount();
			module->function();
		}
	}
}

