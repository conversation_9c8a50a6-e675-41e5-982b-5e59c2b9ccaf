/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_swv.c
* @version 		  : 1.0.0
* @brief Functions for initializing and accessing serial wire viewer
* @details Functions for initializing and accessing serial wire viewer
*****************************************************************************/
#ifndef _SWV_H_
#define _SWV_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "stm32h7xx_hal.h"

/******************************************************************************
* Preprocessor Constants
*******************************************************************************/
/* When set to 1, enables debugging messages through serial wire viewer */
#define ENABLE_DEBUGGING_SWV           (0)
#if ENABLE_DEBUGGING_SWV
#include <stdio.h>
#endif
/******************************************************************************
* Configuration Constants
*******************************************************************************/
#if ENABLE_DEBUGGING_SWV
#define ENABLE_GROUP_TX_DEBUG          (0)
#define ENABLE_HALL_TX_DEBUG           (0)
#define ENABLE_CAR_TX_DEBUG            (0)
#define ENABLE_HB_STATUS_DEBUG         (0)
#define ENABLE_DISPATCH_DEBUG          (0)
#define ENABLE_FRAM_DEBUG              (0)
#define ENABLE_UART_DEBUG              (0)
#endif

/******************************************************************************
* Macros
*******************************************************************************/

/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/

#endif /* _SWV_H_ */
