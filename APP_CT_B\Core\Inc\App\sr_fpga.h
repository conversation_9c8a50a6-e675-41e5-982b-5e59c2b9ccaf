/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_fpga.h
* @version 		  : 1.0.0
* @brief Subroutine updating FPGA related features
* @details Subroutine updating FPGA related features
*****************************************************************************/
#ifndef _SR_FPGA_H_
#define _SR_FPGA_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "network.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
//extern st_subroutine gstSR_FPGA;
/******************************************************************************
* Function Prototypes
*******************************************************************************/
void FPGA_OLD_LoadDatagram_FPGA_Status_CTB(un_datagram *punDatagram);
void FPGA_OLD_Run(void);

void FPGA_LoadDatagram_FPGA_Status_CTB(un_datagram *punDatagram);
void FPGA_Init(void);
void vFPGA_Task(void *pvParameters);
#endif /* _SR_FPGA_H_ */
