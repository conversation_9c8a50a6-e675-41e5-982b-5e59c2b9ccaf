#include "app.h"
#include "ets.h"
#include "operation_def.h"

#include "car_faults_def.h"

#define ETS_CHECK_TIMEOUT 3000

ets_t ETS;

void vETS_Task(void *pvParameters)
{
  while(System_GetControlMode() == CONTROL_MODE__UNKNOWN)
  {
    vTaskDelay(100);
  }

  if(System_GetControlMode() == CONTROL_MODE__HYDRO)
  {
    vTaskDelay(portMAX_DELAY);
  }

  ETS_Init(&ETS);
  while(1)
  {
    ETS_Run(&ETS);
    vTaskDelay(ETS_TASK_DELAY);
  }
}


