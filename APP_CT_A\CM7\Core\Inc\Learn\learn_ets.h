#ifndef INC_LEARN_LEARN_ETS_H_
#define INC_LEARN_LEARN_ETS_H_

#include "learn_v2_def.h"
#include "LearnDef/learn_usr_evt.h"

#define DEFAULT_ETS_POS_XCHECK_VALUE      10
#define DEFAULT_ETS_SPEED_XCHECK_VALUE    10

learn_status_t Learn_ETS_Init(void);
learn_status_t Learn_ETS_Record(void);
learn_status_t Learn_ETS_SaveParams(learn_usr_t type);
learn_status_t Learn_ETS_ParamSync(learn_usr_t type);
learn_status_t Learn_ETS_SaveXCheck(uint8_t *msg_data);
learn_status_t Learn_ETS_XCheck(void);

#endif /* INC_LEARN_LEARN_ETS_H_ */
