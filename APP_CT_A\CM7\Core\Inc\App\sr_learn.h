/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file sr_learn.h
* @version 1.0.0
* @brief This subroutine handles learn mode
* @details This subroutine handles learn mode
*****************************************************************************/
#ifndef _SR_LEARN_H_
#define _SR_LEARN_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
#include "position_def.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void vLearning_Task(void *pvParameters);
#endif /* _SR_LEARN_H_ */
