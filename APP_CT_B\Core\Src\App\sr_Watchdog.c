/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_Watchdog.c
 * @version 		  : 1.0.0
 * @brief This subroutine updates the watchdog peripheral to prevent reset
 * @details This subroutine updates the watchdog peripheral to prevent reset
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "sr_Watchdog.h"
#include "app_watchdog.h"
#include "bootloader_def.h"
#include "car_input_def.h"
#include "motion_def.h"
#include "car_faults_def.h"
#include "stm32g4xx_hal.h"
#include "bootloader_cfg.h"
#include "no_init.h"
#include "system.h"

#include "dg_car_mr_a7.h"
#include "dg_car_ct_a7.h"
#include "dg_car_cop_a4.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                (500)

#define NEED_TO_RESET_DEBOUNCE_LIMIT__MS           (3000)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/

/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/

/******************************************************************************
 * Function Definitions
 *******************************************************************************/

/* @fn void Watchdog_Update(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
void vWatchdog_update(void)
{
  if((Bootloader_GetBootloaderHoldSignal_MR_A7()  == BOOTLOADER_HOLD_MAGIC) ||
     (Bootloader_GetBootloaderHoldSignal_CT_A7() == BOOTLOADER_HOLD_MAGIC))
  {
    // Set bootloader magic
    boot_set_magic(BOOTLOADER_HOLD_MAGIC);
    // Set new watchdog timer
    Watchdog_SetTimeout(2000);

    // Starve watchdog timer
    for(;;);
  }

  Watchdog_Feed();
}

