/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : app_gpio.h
 * @version 		  : 1.0.0
 * @brief          : GPIO accessing and initialization
 * @details		  : GPIO accessing and initialization
 ********************************************************************************/
#ifndef _APP_GPIO_H_
#define _APP_GPIO_H_
/*******************************************************************************
 * Includes
 ********************************************************************************/
#include <stdint.h>
#include "stm32g4xx_hal.h"

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/
/* When DIP switches are all ON at startup, device will startup with watchdog disabled */

/*******************************************************************************
 * Configuration Constants
 ********************************************************************************/
/* Note: falling edge interrupt setting is actually rising edge due to input inversion */
//				(NAME,					PORT,	PIN,			MODE,			PULL,		SPEED, 			INVERT)
#define LOCAL_INPUT_TABLE \
	LOCAL_GP_INPUT(DIP_SW_01		, GPIOF, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_02		, GPIOA, GPIO_PIN_1 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_03		, GPIOA, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_04		, GPIOA, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_05		, GPIOA, GPIO_PIN_4 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_06		, GPIOA, GPIO_PIN_5 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_07		, GPIOA, GPIO_PIN_6 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_08		, GPIOA, GPIO_PIN_7 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DC_IN_01			, GPIOD, GPIO_PIN_0 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_02			, GPIOD, GPIO_PIN_1 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_03			, GPIOD, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_04			, GPIOD, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_05			, GPIOD, GPIO_PIN_4 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_06			, GPIOD, GPIO_PIN_5 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_07			, GPIOD, GPIO_PIN_6 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_08			, GPIOD, GPIO_PIN_7 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_09			, GPIOD, GPIO_PIN_8 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_10			, GPIOD, GPIO_PIN_9 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_11			, GPIOD, GPIO_PIN_10 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_12			, GPIOD, GPIO_PIN_11 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_13			, GPIOG, GPIO_PIN_0 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_14			, GPIOD, GPIO_PIN_13 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_15			, GPIOD, GPIO_PIN_14 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_16			, GPIOD, GPIO_PIN_15 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0)

//				(NAME,					PORT,	PIN,			MODE,			PULL,			SPEED, 			INVERT)
#define LOCAL_OUTPUT_TABLE \
	LOCAL_GP_OUTPUT(SF_TOGGLE		, GPIOB, GPIO_PIN_10 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_01		, GPIOC, GPIO_PIN_0 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_02		, GPIOC, GPIO_PIN_1 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_03		, GPIOC, GPIO_PIN_2 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_04		, GPIOC, GPIO_PIN_3 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_05		, GPIOC, GPIO_PIN_4 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_06		, GPIOC, GPIO_PIN_5 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_07		, GPIOC, GPIO_PIN_6 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_08		, GPIOC, GPIO_PIN_7 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_09		, GPIOE, GPIO_PIN_0 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_10		, GPIOE, GPIO_PIN_1 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_11		, GPIOE, GPIO_PIN_2 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_12		, GPIOE, GPIO_PIN_3 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_13		, GPIOE, GPIO_PIN_4 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_14		, GPIOE, GPIO_PIN_5 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_15		, GPIOE, GPIO_PIN_6 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_16		, GPIOE, GPIO_PIN_7 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(LED_HB			, GPIOE, GPIO_PIN_13    , GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_FLT			, GPIOE, GPIO_PIN_14    , GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_ALM			, GPIOE, GPIO_PIN_15    , GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1)
/*******************************************************************************
 * Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/
typedef struct {
   GPIO_TypeDef *pGPIO_Port;
   uint32_t uiMode;
   uint16_t uwPin;
   uint8_t ucPull;
   uint8_t ucSpeed;
   uint8_t bInvert;
} st_gpio_def;

typedef enum {
#define LOCAL_GP_INPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) enLOCAL_IN__##NAME,
   LOCAL_INPUT_TABLE
#undef LOCAL_GP_INPUT
   NUM_LOCAL_INPUTS
} en_local_inputs;

typedef enum {
#define LOCAL_GP_OUTPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) enLOCAL_OUT__##NAME,
   LOCAL_OUTPUT_TABLE
#undef LOCAL_GP_OUTPUT
   NUM_LOCAL_OUTPUTS
} en_local_outputs;

/*******************************************************************************
 * Global Variables
 ********************************************************************************/

/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/
void GPIO_Init(void);
uint8_t GPIO_ReadInput(en_local_inputs eInput);
uint8_t GPIO_ReadOutput(en_local_outputs eOutput);//
uint8_t GPIO_ReadOutputCommand(en_local_outputs eOutput);
uint8_t* GPIO_GetOutputCommandPointer(void);
void GPIO_WriteOutput(en_local_outputs eOutput, uint8_t bActive);
uint8_t GPIO_ReadDIPBank(void);

#endif /* _APP_GPIO_H_ */
