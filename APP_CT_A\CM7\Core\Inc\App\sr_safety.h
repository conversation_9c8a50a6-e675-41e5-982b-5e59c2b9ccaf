/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_safety.h
* @version 		  : 1.0.0
* @brief This subroutine updates safety control outputs and monitors safety inputs
* @details This subroutine updates safety control outputs and monitors safety inputs
*****************************************************************************/
#ifndef _SR_SAFETY_H_
#define _SR_SAFETY_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "version.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void vSafety_Task(void *pvParameters);
#endif /* _SR_SAFETY_H_ */
