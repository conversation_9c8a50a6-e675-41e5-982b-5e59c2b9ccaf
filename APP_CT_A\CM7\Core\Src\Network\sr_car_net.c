/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_car_net.c
* @version 		  : 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "app.h"
#include "sr_car_net.h"
#include "sr_CAN1.h"
#include "sr_local_inputs.h"
#include "sr_local_outputs.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
#include "car_output_def.h"
#include "learn_def.h"
#include "bootloader.h"
#include "app_can.h"
#include "app_spi.h"
#include "dg_car_ct_a7.h"
#include "learn_v2_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (5)

#define MAX_PACKETS_TRANSMIT_PER_CYCLE                            (5)

//#define LOCAL_INPUT_FIXED_RANGE_BITSHIFT_CTA                      (0)
//#define LOCAL_INPUT_FIXED_RANGE_BITMASK_CTA                       (0x0001FFFF) // First 16 bits. ETS_DOWN is skipped for hydro

#define PARAM_REQUEST_STARTUP_RESEND_RATE_1MS                     (100)
#define PARAM_REQUEST_BLOCK_RESEND_RATE_1MS                       (1000)
#define PARAM_REQUEST_OTHER_RESEND_RATE_1MS                       (1000)
#define PARAM_CRC_RESEND_RATE_1MS                                 (10000)
#define PARAM_BLOCK_RESEND_RATE_1MS                               (3000)

#define POSITION_MIN_RESEND_RATE_1MS                              (3000)//(1000)

#define ALARM_LOG_RESEND_INTERVAL_50MS                            (250)
#define FAULT_LOG_RESEND_INTERVAL_50MS                            (250)
#define PARAM_LOG_RESEND_INTERVAL_50MS                            (250)

#define DEBUG_INFO_RESEND_INTERVAL_50MS							  (250)

#define LEARN_STATE_RESEND_ACTIVE_INTERVAL_100MS                  (3)

#define NETWORK_INFO_RESEND_INTERVAL_1S						      (5)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_datagram_control *pstControl;
static const en_sys_network eLocalNetwork = SYS_NETWORK__CAR;

static en_active_inactive bDebugHBLedBlink = INACTIVE;
/******************************************************************************
* Function Definitions
*******************************************************************************/
en_active_inactive GetTransmitFaultDatagramCheck(void)
{
	return bDebugHBLedBlink;
}

void SetTransmitFaultDatagramCheck(en_active_inactive bActive)
{
	bDebugHBLedBlink = bActive;
}
/* Load functions */
void DG_CAR_CT_A7_LoadData_Fault(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Fault];
   st_fault_data *pstData = Faults_GetFaultData_ByNode(System_GetNodeID());
   if( pstData != NULL )
   {
	   pstDatagram->uwMinResendInterval_1ms = ( pstData->eFault == CFLT__NONE ) ? 3000 : 500;
	   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Fault, (uint8_t *) pstData);
   }
}
void DG_CAR_CT_A7_LoadData_Alarm(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Alarm];
   st_alarm_data *pstData = Alarms_GetAlarmData_ByNode(System_GetNodeID());
   if( pstData != NULL )
   {
	   pstDatagram->uwMinResendInterval_1ms = ( pstData->eAlarm == CALM__NONE ) ? 3000 : 500;
	   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Alarm, (uint8_t *) pstData);
   }
}
void DG_CAR_CT_A7_LoadData_Param_Req(void)
{
   static en_param_blocks eLastBlock;
   static uint16_t uwResendCountdown_1ms;
   static st_param_req stReq;
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Param_Req];
   if( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
   {
      en_param_blocks eBlock = Param_GetBlockToUpdate();
      if( pstDatagram->bDataChanged == INACTIVE )
      {
         if( Param_GetState() == PARAM_STATE__STARTUP )
         {
            if( uwResendCountdown_1ms >= PARAM_REQUEST_STARTUP_RESEND_RATE_1MS )
            {
               uwResendCountdown_1ms = 0;
               stReq.eType = 0;
               stReq.uiValue = 0;
               stReq.uwParamIndex = 0;
               stReq.eCommand = PARAM_COMMAND__STARTUP;
               DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Param_Req, (uint8_t *) &stReq);
               pstDatagram->bDataChanged = ACTIVE;
               stReq.eCommand = PARAM_COMMAND__NONE; /* Mark that a new packet can be unloaded */
            }
            else uwResendCountdown_1ms += pstDatagram->uwLoadInterval_1ms;
         }
         else if( eBlock < NUM_PARAM_BLOCKS )
         {
            if( ( eLastBlock != eBlock )
             || ( uwResendCountdown_1ms >= PARAM_REQUEST_BLOCK_RESEND_RATE_1MS ) )
            {
               uwResendCountdown_1ms = 0;
               stReq.eType = 0;
               stReq.uiValue = 0;
               stReq.uwParamIndex = 0;
               stReq.eCommand = PARAM_COMMAND__REQ_BLOCK_START + eBlock;
               DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Param_Req, (uint8_t *) &stReq);
               pstDatagram->bDataChanged = ACTIVE;
               stReq.eCommand = PARAM_COMMAND__NONE; /* Mark that a new packet can be unloaded */
            }
            else uwResendCountdown_1ms += pstDatagram->uwLoadInterval_1ms;
         }
         else if( ( stReq.eCommand != PARAM_COMMAND__NONE )
               || ( Param_GetRequest(&stReq) ) )
         {
            if( uwResendCountdown_1ms >= PARAM_REQUEST_OTHER_RESEND_RATE_1MS )
            {
               uwResendCountdown_1ms = 0;
               DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Param_Req, (uint8_t *) &stReq);
               pstDatagram->bDataChanged = ACTIVE;
               stReq.eCommand = PARAM_COMMAND__NONE; /* Mark that a new packet can be unloaded */
            }
            else uwResendCountdown_1ms += pstDatagram->uwLoadInterval_1ms;
         }
         else uwResendCountdown_1ms = 0xFFFF;
      }
      eLastBlock = eBlock;
   }
   else pstDatagram->bDataChanged = INACTIVE;
}
void DG_CAR_CT_A7_LoadData_CAN_Status(void)
{
	static uint8_t ucDelay_1s;
   un_datagram unData;
   if( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) && 0 ) //AS TODO enable with dip or parameter
   {
	   if( ucDelay_1s < NETWORK_INFO_RESEND_INTERVAL_1S )
	   {
		   ucDelay_1s++;
	   }
	   if( pstControl->pastDatagrams[DG_CAR_CT_A7__CAN_Status].bDataChanged == INACTIVE )
	   {
		   if( ucDelay_1s >= NETWORK_INFO_RESEND_INTERVAL_1S )
		   {
			   unData.auc8[0] = CAN1_GetStatusCounter(CAN_COUNTER__RX_PACKET);
			   unData.auc8[1] = CAN1_GetStatusCounter(CAN_COUNTER__TX_PACKET);
			   unData.auc8[2] = CAN1_GetStatusCounter(CAN_COUNTER__RX_OVERFLOW);
			   unData.auc8[3] = CAN1_GetStatusCounter(CAN_COUNTER__TX_OVERFLOW);
			   unData.auc8[4] = CAN1_GetStatusCounter(CAN_COUNTER__BUS_ERROR);
			   unData.auc8[5] = CAN1_GetStatusCounter(CAN_COUNTER__BUS_RESET);

			   unData.auc8[6] = CAN2_GetStatusCounter(CAN_COUNTER__RX_PACKET);
			   unData.auc8[7] = CAN2_GetStatusCounter(CAN_COUNTER__TX_PACKET);
			   unData.auc8[8] = CAN2_GetStatusCounter(CAN_COUNTER__RX_OVERFLOW);
			   unData.auc8[9] = CAN2_GetStatusCounter(CAN_COUNTER__TX_OVERFLOW);
			   unData.auc8[10] = CAN2_GetStatusCounter(CAN_COUNTER__BUS_ERROR);
			   unData.auc8[11] = CAN2_GetStatusCounter(CAN_COUNTER__BUS_RESET);

			   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__CAN_Status, &unData.auc8[0]);
			   pstControl->pastDatagrams[DG_CAR_CT_A7__CAN_Status].bDataChanged = ACTIVE;
			   ucDelay_1s = 0;
		   }
	   }
   }
}
void DG_CAR_CT_A7_LoadData_SPI_Status(void)
{
	static uint8_t ucDelay_1s;
   un_datagram unData;
   if( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) && 0 ) //AS TODO enable with dip or parameter
   {
	   if( ucDelay_1s < NETWORK_INFO_RESEND_INTERVAL_1S )
	   {
		   ucDelay_1s++;
	   }
	   if( pstControl->pastDatagrams[DG_CAR_CT_A7__SPI_Status].bDataChanged == INACTIVE )
	   {
		   if( ucDelay_1s >= NETWORK_INFO_RESEND_INTERVAL_1S )
		   {
			   unData.auc8[0] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__TX);
			   unData.auc8[1] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__RX);
			   unData.auc8[2] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__UNDERRUN);
			   unData.auc8[3] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__MODE);
			   unData.auc8[4] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__FRAME);
			   unData.auc8[5] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__CRC);
			   unData.auc8[6] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__TX_OVERFLOW);
			   unData.auc8[7] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__RX_OVERFLOW);
			   unData.auc8[8] = SPI_TX_GetCounter(SPI_AB, SPI_COUNTER__UNKNOWN);

			   unData.auc8[9] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__TX);
			   unData.auc8[10] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__RX);
			   unData.auc8[11] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__UNDERRUN);
			   unData.auc8[12] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__MODE);
			   unData.auc8[13] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__FRAME);
			   unData.auc8[14] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__CRC);
			   unData.auc8[15] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__TX_OVERFLOW);
			   unData.auc8[16] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__RX_OVERFLOW);
			   unData.auc8[17] = SPI_RX_GetCounter(SPI_AB, SPI_COUNTER__UNKNOWN);

			   unData.auc8[18] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__TX);
			   unData.auc8[19] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__RX);
			   unData.auc8[20] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__UNDERRUN);
			   unData.auc8[21] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__MODE);
			   unData.auc8[22] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__FRAME);
			   unData.auc8[23] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__CRC);
			   unData.auc8[24] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__TX_OVERFLOW);
			   unData.auc8[25] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__RX_OVERFLOW);
			   unData.auc8[26] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__UNKNOWN);

			   unData.auc8[27] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__TX);
			   unData.auc8[28] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__RX);
			   unData.auc8[29] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__UNDERRUN);
			   unData.auc8[30] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__MODE);
			   unData.auc8[31] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__FRAME);
			   unData.auc8[32] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__CRC);
			   unData.auc8[33] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__TX_OVERFLOW);
			   unData.auc8[34] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__RX_OVERFLOW);
			   unData.auc8[35] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__UNKNOWN);

			   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__SPI_Status, &unData.auc8[0]);
			   pstControl->pastDatagrams[DG_CAR_CT_A7__SPI_Status].bDataChanged = ACTIVE;
			   ucDelay_1s = 0;
		   }
	   }
   }
}
void DG_CAR_CT_A7_LoadData_Version(void)
{
  // CT A7 application header address = 0x08040000
  const uint32_t ulAppHeaderAddress = 0x08040000;
  const app_header_t *xAppHeader = (app_header_t*)ulAppHeaderAddress;

   un_datagram unData;
   unData.auw16[0] = xAppHeader->version.auwVers[0];
   unData.auw16[1] = xAppHeader->version.auwVers[1];
   unData.auw16[2] = xAppHeader->version.auwVers[2];
   unData.auw16[3] = xAppHeader->version.auwVers[3];
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Version, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Local_IO(void)
{
	un_datagram unData;
//	uint32_t input_bitmap_1 = LocalInputs_GetInputBitmap(0);
//	uint32_t input_bitmap_2 = LocalInputs_GetInputBitmap(1);
	uint32_t output_bitmap = LocalOutputs_GetOutputBitmap(0);

	unData.auc8[0] = (uint8_t)LocalInputs_GetInputBitmap(0);
	unData.auc8[1] = (uint8_t)LocalInputs_GetInputBitmap(1);
	unData.auc8[2] = (uint8_t)LocalInputs_GetInputBitmap(2);
	unData.auc8[3] = (uint8_t)LocalInputs_GetInputBitmap(3);
	unData.auc8[4] = (uint8_t)LocalInputs_GetInputBitmap(4);
	unData.auc8[5] = output_bitmap & 0xFF;
	unData.auc8[6] = (output_bitmap >> 8) & 0xFF;
	unData.auc8[7] = (output_bitmap >> 16) & 0xFF;

	DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Local_IO, &unData.auc8[0]);
//	DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Local_IO, LocalInputs_GetInputBitmapPointer());
}
void DG_CAR_CT_A7_LoadData_Position(void)
{
   static uint16_t uwResendDelay_1ms;
   un_datagram unData;
   unData.aui32[0] = Position_GetPosition_05mm();
   unData.auc8[3] = Position_GetState();
   unData.auw16[2] = Position_GetSpeed_fpm();
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Position, &unData.auc8[0]);
   if( pstControl->pastDatagrams[DG_CAR_CT_A7__Position].bDataChanged == INACTIVE )
   {
      if( uwResendDelay_1ms >= POSITION_MIN_RESEND_RATE_1MS )
      {
         uwResendDelay_1ms = 0;
         pstControl->pastDatagrams[DG_CAR_CT_A7__Position].bDataChanged = ACTIVE;
      }
      else uwResendDelay_1ms += pstControl->pastDatagrams[DG_CAR_CT_A7__Position].uwLoadInterval_1ms;
   }
   else uwResendDelay_1ms = 0;
}

void DG_CAR_CT_A7_LoadData_Lrn_Hstwy_Misc_Dat(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_Hstwy_Misc_Dat];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;
   un_datagram unData;
   unData.auw16[0] = (uint16_t)Learn_GetState();
   // Only send errors once when they're found set, then clear them
   static uint16_t iLastErr = LRN_RET__NO_ERROR;
   uint16_t iErr = Learn_GetActiveError();
   if( iErr == LRN_RET__NO_ERROR || iLastErr != LRN_RET__NO_ERROR )
   {
      iErr = LRN_RET__NO_ERROR;
      Learn_SetActiveError(iErr);
   }
   iLastErr = iErr;
   unData.auw16[1] = iErr;
   unData.auw16[2] = (uint16_t)Learn_GetActiveUINode();
   unData.auc8[6] = Learn_GetCurrentFloor();
   unData.auc8[7] = Learn_GetBottomTerminalLearned();
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_Hstwy_Misc_Dat, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Lrn_Hstwy_Flr_Pos(void)
{
   static uint8_t ucFloorIndex;
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_Hstwy_Flr_Pos];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;

   uint8_t ucNumLearnFloors = Learn_GetNumberOfCarFloors();
   if( ucFloorIndex >= ucNumLearnFloors )
   {
      ucFloorIndex = 0;
   }
   uint8_t ucFloorsInPkt = ucNumLearnFloors - ucFloorIndex;
   if( ucFloorsInPkt >= 15 )
   {
      ucFloorsInPkt = 15; // confine num to fit max packet size
   }
   un_datagram unData;
   unData.auc8[0] = ucFloorIndex;
   unData.auc8[1] = ucFloorsInPkt;
   uint8_t ucDGDatIndex = 1;
   uint8_t ucStopCount = ucFloorsInPkt+ucFloorIndex;
   for(; ucFloorIndex < ucStopCount; ucFloorIndex++)
   {
      Learn_GetFloorPosition_05mm(ucFloorIndex, &unData.aui32[ucDGDatIndex++]);
   }
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_Hstwy_Flr_Pos, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Lrn_CMD_DBG_0_ACK(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_CMD_DBG_0_ACK];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;
   un_datagram unData;
   uint16_t uwRetVal = Learn_GetCmdRet(LRN_CMD_ID__DBG_0);
   unData.auw16[0] = uwRetVal;
   uint8_t ucFlagVal = Learn_GetCmdFlagForTx(LRN_CMD_ID__DBG_0_ACK);
   unData.auc8[2] = ucFlagVal;
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_CMD_DBG_0_ACK, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Lrn_CMD_SETPOS_C_ACK(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_CMD_SETPOS_C_ACK];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;
   un_datagram unData;
   uint16_t uwRetVal = Learn_GetCmdRet(LRN_CMD_ID__SETPOS_C);
   unData.auw16[0] = uwRetVal;
   uint8_t ucFlagVal = Learn_GetCmdFlagForTx(LRN_CMD_ID__SETPOS_C_ACK);
   unData.auc8[2] = ucFlagVal;
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_CMD_SETPOS_C_ACK, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Lrn_CMD_SETDIS_0_ACK(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_CMD_SETDIS_0_ACK];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;
   un_datagram unData;
   uint16_t uwRetVal = Learn_GetCmdRet(LRN_CMD_ID__SETDIS_0);
   unData.auw16[0] = uwRetVal;
   uint8_t ucFlagVal = Learn_GetCmdFlagForTx(LRN_CMD_ID__SETDIS_0_ACK);
   unData.auc8[2] = ucFlagVal;
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_CMD_SETDIS_0_ACK, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Lrn_CMD_SETPOS_M_ACK(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_CMD_SETPOS_M_ACK];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;
   un_datagram unData;
   uint16_t uwRetVal = Learn_GetCmdRet(LRN_CMD_ID__SETPOS_M);
   unData.auw16[0] = uwRetVal;
   uint8_t ucFlagVal = Learn_GetCmdFlagForTx(LRN_CMD_ID__SETPOS_M_ACK);
   unData.auc8[2] = ucFlagVal;
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_CMD_SETPOS_M_ACK, &unData.auc8[0]);
}
void DG_CAR_CT_A7_LoadData_Lrn_CMD_DONE(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_CMD_DONE];
   if( Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 60000;
   un_datagram unData;
   uint8_t ucFlagVal = Learn_GetCmdFlag(LRN_CMD_ID__DONE);
   //printf("ucFlagVal=%d %s fileLn%d\n", ucFlagVal, __FUNCTION__, __LINE__);
   unData.auc8[0] = ucFlagVal;
   DGS_WriteDatagram(pstControl, DG_CAR_CT_A7__Lrn_CMD_DONE, &unData.auc8[0]);
}

void DG_CAR_CT_A7_LoadData_Lrn_Cmd(void)
{
  // Is there anything on the learn out queue?
  extern learn_t LearnModule;
  if(LearnModule.out.queue != NULL)
  {
    if(uxQueueMessagesWaiting(LearnModule.out.queue) > 0)
    {
      learn_out_msg_t msg;
      xQueueReceive(LearnModule.out.queue, &msg, 0);

      st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Lrn_Cmd];
      pstDatagram->paucData[0] = msg.evt;
      memcpy(&pstDatagram->paucData[1], &msg.data[0], LEARN_OUT_DATA_LENGTH);

      pstDatagram->bDataChanged = ACTIVE;
    }
  }
}

/* @fn static void TransmitHandler(void)
 * @brief Checks for datagrams to transmit
 * @param None
 * @return None
 */
static void TransmitHandler(void) {
   st_CAN_msg stTxMsg;
   en_pass_fail eError;
   uint16_t uwDatagramIndex = DG_CAR_CT_A7__Position;
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_CT_A7__Position];
   /* Prioritize position packet */
   if(pstDatagram->bDataChanged) {
      eError = PASS;
      stTxMsg.bCAN_FD = 1;
      stTxMsg.bExtendedID = 1;
      stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
      memcpy(&stTxMsg.unData.auc8[0], pstDatagram->paucData, pstDatagram->ucSize_Bytes);
      if(Network_CheckDestination_Car(NET_FWD_PATH__CAR, pstDatagram->uiDestinationBitmap) == PASS) {
         stTxMsg.uiID = UNIQUE_POSITION_EXTENDED_CAN_ID;
         eError = CAN1_TransmitDatagram(&stTxMsg);
// Suppress resend on transmission failure if the bus is down to avoid starving other pathways
         eError = (CAN1_CheckIfCommLoss()) ? PASS : eError;
      }
      if(Network_CheckDestination_Car(NET_FWD_PATH__AB, pstDatagram->uiDestinationBitmap) == PASS && (eError != FAIL)) {
         stTxMsg.uiID = Network_GetPacketID(pstDatagram->ucSize_Bytes, uwDatagramIndex, eLocalNetwork, System_GetNodeID());
         eError = SPI_AB_TransmitDatagram(&stTxMsg);
      }
      if(Network_CheckDestination_Car(NET_FWD_PATH__UI, pstDatagram->uiDestinationBitmap) == PASS) {
         SPI_AU_TransmitDatagram(&stTxMsg);
      }
      if(eError == PASS) {
         pstDatagram->bDataChanged = INACTIVE;
      }
   }
   for(uint8_t i = 0; i < MAX_PACKETS_TRANSMIT_PER_CYCLE; i++) {
      if(DGS_GetNextDatagram(pstControl, &uwDatagramIndex) == PASS) {
         eError = PASS;
         pstDatagram = &pstControl->pastDatagrams[uwDatagramIndex];
         stTxMsg.bCAN_FD = 1;
         stTxMsg.bExtendedID = 1;
         stTxMsg.uiID = Network_GetPacketID(pstDatagram->ucSize_Bytes, uwDatagramIndex, eLocalNetwork, System_GetNodeID());
         stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
         memcpy(&stTxMsg.unData.auc8[0], pstDatagram->paucData, pstDatagram->ucSize_Bytes);
         if(Network_CheckDestination_Car(NET_FWD_PATH__CAR, pstDatagram->uiDestinationBitmap) == PASS) {
            eError = CAN1_TransmitDatagram(&stTxMsg);
// Suppress resend on transmission failure if the bus is down to avoid starving other pathways
            eError = (CAN1_CheckIfCommLoss()) ? PASS : eError;
         }
         if(Network_CheckDestination_Car(NET_FWD_PATH__AB, pstDatagram->uiDestinationBitmap) == PASS && (eError != FAIL)) {
            eError = SPI_AB_TransmitDatagram(&stTxMsg);
         }
         if(Network_CheckDestination_Car(NET_FWD_PATH__UI, pstDatagram->uiDestinationBitmap) == PASS) {
            SPI_AU_TransmitDatagram(&stTxMsg);
         }
         /* If message has failed to send, remark it as the next packet due to send */
         if(eError == FAIL) {
            if(pstControl->uwLastSentDG)
               pstControl->uwLastSentDG--;
            else
               pstControl->uwLastSentDG = pstControl->uwNumberOfDG - 1;
            pstDatagram->bDataChanged = ACTIVE;
            break;
         } else {
#if ENABLE_CAR_TX_DEBUG
	         printf("SC: %d\n", uwDatagramIndex);
#endif
            pstDatagram->ucPacketCounter++;
            if( uwDatagramIndex == DG_CAR_CT_A7__Fault )
            {
            	bDebugHBLedBlink = ACTIVE;
            }
         }
      } else {
         break;
      }
   }
}
/* @fn void CarNet_Task_Init(void)
 * @brief Initializes the CarNet task, selects the appropriate datagram control structure,
 *        and configures CAN interfaces based on the local board type
 * @param None
 * @return None
 */
void CarNet_Init(void)
{
	pstControl = DG_CAR_CT_A7_GetDatagramControlStruct();
	DGS_Init(pstControl);
}

/* @fn void vCarNet_Task(void *pvParams)
 * @brief Handles CarNet tasks, including stack usage monitoring and datagram scheduling for transmission
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCarNet_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		/* Load Datagram scheduler */
		   DGS_LoadHandler(pstControl);

		   /* Transmit Datagram scheduler */
		   TransmitHandler();
		vTaskDelay(CAR_NET_TASK_DELAY);
	}
}
