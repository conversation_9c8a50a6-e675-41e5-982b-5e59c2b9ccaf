/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_Fault.c
* @version 		  : 1.0.0
* @brief Subroutine updating processor's fault status
* @details Subroutine updating processor's fault status
*****************************************************************************/
#ifndef _SR_FAULT_
#define _SR_FAULT_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void vFaultAlarm_Task(void *pvParameters);
void FaultAlarm_Init(void);
#endif /* _SR_FAULT_ */
