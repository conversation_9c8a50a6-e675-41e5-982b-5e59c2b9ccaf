/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_hall_net.c
* @version        : 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "sr_hall_net.h"
#include "sr_CAN1.h"
#include "car_output_def.h"
#include "main.h"
#include "car_alarms_def.h"
#include "hall_call_def.h"
#include "ui_request_def.h"
#include "dg_group_riser.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

#define MAX_PACKETS_TRANSMIT_PER_CYCLE                            (5)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
uint16_t gHallNet_Task_Delay = 0;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static uint16_t uwHBCheckConnectionsTimer_1ms;

/******************************************************************************
* Function Definitions
*******************************************************************************/

/* @fn void HallNet_Task_Init(void)
 * @brief Initializes the Hall Network task
 * @param None
 * @return None
 */
void HallNet_Task_Init(void)
{
   if( Main_GetLocalBoardType() == BOARD_RISER )
   {
      gHallNet_Task_Delay = HALL_NET_TASK_PERIODIC_DELAY_MS;
   }
   else
   {
	   gHallNet_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;
   }
}

/* @fn void vHallNet_Task(void *pvParams)
 * @brief Handles Hall Network tasks: checking for offline hall boards.
 * Note: We have no plans to run hall-net on this board, so the checking of hall board conn status could be moved to another task, and this task removed.
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vHallNet_Task(void *pvParams)
{
   // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1) {
        // Check for offline hall boards if the specified period has elapsed
        // Needs to be called at min 50ms intervals to hold alarms without timout jitter.
        Hall_Board_CheckConnections(&uwHBCheckConnectionsTimer_1ms);
        // Update the connection check timer
        uwHBCheckConnectionsTimer_1ms += gHallNet_Task_Delay;

		vTaskDelay(gHallNet_Task_Delay); // Delay using the configured HallNet task period
    }
}

