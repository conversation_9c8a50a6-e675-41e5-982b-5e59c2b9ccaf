/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_parameters.h
* @version 		  : 1.0.0
* @brief Subroutine updating processor's fault status
* @details Subroutine updating processor's fault status
*****************************************************************************/
#ifndef _SR_PARAMETERS_
#define _SR_PARAMETERS_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
void Parameters_Task_Init(void);
void vParameters_Task(void *pvParams);

#endif /* _SR_PARAMETERS_ */
