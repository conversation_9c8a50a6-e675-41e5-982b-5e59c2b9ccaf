/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_outputs.h
* @version 		  : 1.0.0
* @brief          : local outputs subroutine
* @details		  : local outputs subroutine
********************************************************************************/
#ifndef _SR_LOCAL_OUTPUTS_H_
#define _SR_LOCAL_OUTPUTS_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/


/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variables
********************************************************************************/

uint8_t LocalOutputs_GetOutputBitmap(uint8_t ucArrayIndex);

/*******************************************************************************
* Function Prototypes
********************************************************************************/
void vLocalOutputs_Task(void *pvParams);

#endif /* _SR_LOCAL_OUTPUTS_H_ */
