/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file    spi.c
 * @brief   This file provides code for the configuration
 *          of the SPI instances.
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "app_spi.h"
#include <string.h>
#include "network.h"
#include "main.h"
/* USER CODE BEGIN 0 */
/* Variable Definitions ------------------------------------------------------------------*/

/* Structures for accessing spi ring buffers */
static volatile RINGBUFF_T stSPI_AU_txring; // TX A to UI
static volatile RINGBUFF_T stSPI_AU_rxring; // RX UI to A

/* Underlying arrays for SPI ring buffers */
static volatile uint16_t auwSPI_AU_rxbuff[SPI_RX_RING_BUFFER_SIZE_WORD];
static volatile uint16_t auwSPI_AU_txbuff[SPI_TX_RING_BUFFER_SIZE_WORD];

/* Temporary buffers for active transfer */
static volatile uint16_t auwSPI_AU_TempTxbuff[SPI_MAX_PACKET_SIZE__WORD];

static volatile uint16_t auwStatusCounter_SPI_AU_RX[NUM_SPI_COUNTERS];// RX A to UI
static volatile uint16_t auwStatusCounter_SPI_AU_TX[NUM_SPI_COUNTERS];// TX UI to A

/* Flag indicating the TX ring buffer is being loaded and should not be pulled from */
static volatile en_active_inactive bLoadingTxBufferUI;
/* USER CODE END 0 */

SPI_HandleTypeDef hspi1;
SPI_HandleTypeDef hspi2;

/* SPI1 init function */
void MX_SPI1_Init(void)
{

  /* USER CODE BEGIN SPI1_Init 0 */
	RingBuffer_Init((RINGBUFF_T *)&stSPI_AU_rxring, (uint16_t *)auwSPI_AU_rxbuff, sizeof(uint16_t), SPI_RX_RING_BUFFER_SIZE_WORD);
  /* USER CODE END SPI1_Init 0 */

  /* USER CODE BEGIN SPI1_Init 1 */

  /* USER CODE END SPI1_Init 1 */
  hspi1.Instance = SPI1;
  hspi1.Init.Mode = SPI_MODE_SLAVE;
  hspi1.Init.Direction = SPI_DIRECTION_2LINES_RXONLY;
  hspi1.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi1.Init.NSS = SPI_NSS_HARD_INPUT;
  hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi1.Init.CRCPolynomial = 7;
  hspi1.Init.CRCLength = SPI_CRC_LENGTH_DATASIZE;
  hspi1.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  if (HAL_SPI_Init(&hspi1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI1_Init 2 */
	/* Initiate Receive only spi that will perpetually unload bytes to a ring buffer. Adapted from HAL_SPI_Receive_IT */
	/* Enable RXP, OVR, FRE, MODF interrupts */
	__HAL_SPI_DISABLE(&hspi1);
	//CLEAR_BIT(hspi1.Instance->CR2, SPI_RXFIFO_THRESHOLD);
	__HAL_SPI_ENABLE(&hspi1);
	__HAL_SPI_ENABLE_IT(&hspi1, (SPI_IT_RXNE | SPI_IT_ERR));
  /* USER CODE END SPI1_Init 2 */

}
/* SPI2 init function */
void MX_SPI2_Init(void)
{

  /* USER CODE BEGIN SPI2_Init 0 */
	RingBuffer_Init((RINGBUFF_T *)&stSPI_AU_txring, (uint16_t *)auwSPI_AU_txbuff, sizeof(uint16_t), SPI_TX_RING_BUFFER_SIZE_WORD);

  /* USER CODE END SPI2_Init 0 */

  /* USER CODE BEGIN SPI2_Init 1 */

  /* USER CODE END SPI2_Init 1 */
  hspi2.Instance = SPI2;
  hspi2.Init.Mode = SPI_MODE_MASTER;
  hspi2.Init.Direction = SPI_DIRECTION_2LINES;
  hspi2.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi2.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi2.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi2.Init.NSS = SPI_NSS_HARD_OUTPUT;
  hspi2.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_32;// = 170 MHZ / 32 = 5.325 MHZ
  hspi2.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi2.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi2.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi2.Init.CRCPolynomial = 7;
  hspi2.Init.CRCLength = SPI_CRC_LENGTH_DATASIZE;
  hspi2.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  if (HAL_SPI_Init(&hspi2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI2_Init 2 */

  /* USER CODE END SPI2_Init 2 */

}

void HAL_SPI_MspInit(SPI_HandleTypeDef* spiHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(spiHandle->Instance==SPI1)
  {
  /* USER CODE BEGIN SPI1_MspInit 0 */

  /* USER CODE END SPI1_MspInit 0 */
    /* SPI1 clock enable */
    __HAL_RCC_SPI1_CLK_ENABLE();

    __HAL_RCC_GPIOG_CLK_ENABLE();
    /**SPI1 GPIO Configuration
    PG2     ------> SPI1_SCK
    PG3     ------> SPI1_MISO
    PG4     ------> SPI1_MOSI
    PG5     ------> SPI1_NSS
    */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_4|GPIO_PIN_5;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

    /* SPI1 interrupt Init */
    HAL_NVIC_SetPriority(SPI1_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(SPI1_IRQn);
  /* USER CODE BEGIN SPI1_MspInit 1 */

  /* USER CODE END SPI1_MspInit 1 */
  }
  else if(spiHandle->Instance==SPI2)
  {
  /* USER CODE BEGIN SPI2_MspInit 0 */

  /* USER CODE END SPI2_MspInit 0 */
    /* SPI2 clock enable */
    __HAL_RCC_SPI2_CLK_ENABLE();

    __HAL_RCC_GPIOB_CLK_ENABLE();
    /**SPI2 GPIO Configuration
    PB12     ------> SPI2_NSS
    PB13     ------> SPI2_SCK
    PB14     ------> SPI2_MISO
    PB15     ------> SPI2_MOSI
    */
    GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI2;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* SPI2 interrupt Init */
    HAL_NVIC_SetPriority(SPI2_IRQn, 4, 0);
    HAL_NVIC_EnableIRQ(SPI2_IRQn);
  /* USER CODE BEGIN SPI2_MspInit 1 */

  /* USER CODE END SPI2_MspInit 1 */
  }
}

void HAL_SPI_MspDeInit(SPI_HandleTypeDef* spiHandle)
{

  if(spiHandle->Instance==SPI1)
  {
  /* USER CODE BEGIN SPI1_MspDeInit 0 */

  /* USER CODE END SPI1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI1_CLK_DISABLE();

    /**SPI1 GPIO Configuration
    PG2     ------> SPI1_SCK
    PG3     ------> SPI1_MISO
    PG4     ------> SPI1_MOSI
    PG5     ------> SPI1_NSS
    */
    HAL_GPIO_DeInit(GPIOG, GPIO_PIN_2|GPIO_PIN_4|GPIO_PIN_5);

    /* SPI1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(SPI1_IRQn);
  /* USER CODE BEGIN SPI1_MspDeInit 1 */

  /* USER CODE END SPI1_MspDeInit 1 */
  }
  else if(spiHandle->Instance==SPI2)
  {
  /* USER CODE BEGIN SPI2_MspDeInit 0 */

  /* USER CODE END SPI2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI2_CLK_DISABLE();

    /**SPI2 GPIO Configuration
    PB12     ------> SPI2_NSS
    PB13     ------> SPI2_SCK
    PB14     ------> SPI2_MISO
    PB15     ------> SPI2_MOSI
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_15);

    /* SPI2 interrupt Deinit */
    HAL_NVIC_DisableIRQ(SPI2_IRQn);
  /* USER CODE BEGIN SPI2_MspDeInit 1 */

  /* USER CODE END SPI2_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
/* @fn void SPI1_IRQHandler(void)
 * @brief SPI1 global interrupt handling function
 * @param None
 * @return None
 */
void SPI1_IRQHandler(void)
{
   // Adapted from HAL_SPI_IRQHandler for 16-Bit constant Receive only mode
   uint32_t itsource = hspi1.Instance->CR2;
   uint32_t itflag   = hspi1.Instance->SR;
   //uint32_t trigger  = itsource & itflag;
   if ((SPI_CHECK_FLAG(itflag, SPI_FLAG_OVR) == RESET) &&
       (SPI_CHECK_FLAG(itflag, SPI_FLAG_RXNE) != RESET) && (SPI_CHECK_IT_SOURCE(itsource, SPI_IT_RXNE) != RESET))
   {
      // Adapted from SPI_2linesRxISR_16BIT
      uint16_t uwValue = *((__IO uint16_t *)&(hspi1.Instance->DR));
      if( !RingBuffer_Insert((RINGBUFF_T *)&stSPI_AU_rxring, &uwValue) )
      {
         auwStatusCounter_SPI_AU_RX[SPI_COUNTER__RX_OVERFLOW]++;
      }
      return;
   }

   /* SPI in Error Treatment --------------------------------------------------*/
   if (((SPI_CHECK_FLAG(itflag, SPI_FLAG_MODF) != RESET) || (SPI_CHECK_FLAG(itflag, SPI_FLAG_OVR) != RESET)
        || (SPI_CHECK_FLAG(itflag, SPI_FLAG_FRE) != RESET)) && (SPI_CHECK_IT_SOURCE(itsource, SPI_IT_ERR) != RESET))
   {
      /* SPI Overrun error interrupt occurred ----------------------------------*/
      if (SPI_CHECK_FLAG(itflag, SPI_FLAG_OVR) != RESET)
    	  {
    	  	  SET_BIT(hspi1.ErrorCode, HAL_SPI_ERROR_OVR);
    	  	  auwStatusCounter_SPI_AU_RX[SPI_COUNTER__RX_OVERFLOW]++;
    	  }
      /* SPI Mode Fault error interrupt occurred -------------------------------*/
      else if (SPI_CHECK_FLAG(itflag, SPI_FLAG_MODF) != RESET)
    	  {
    	  SET_BIT(hspi1.ErrorCode, HAL_SPI_ERROR_MODF);
    	  	  auwStatusCounter_SPI_AU_RX[SPI_COUNTER__MODE]++;
    	  }
      /* SPI Frame error interrupt occurred ------------------------------------*/
      else if (SPI_CHECK_FLAG(itflag, SPI_FLAG_FRE) != RESET)
    	  {
    	  	  SET_BIT(hspi1.ErrorCode, HAL_SPI_ERROR_FRE);
    	  	  auwStatusCounter_SPI_AU_RX[SPI_COUNTER__FRAME]++;
    	  }
      else auwStatusCounter_SPI_AU_RX[SPI_COUNTER__UNKNOWN]++;
      __HAL_SPI_CLEAR_OVRFLAG(&hspi1);
      __HAL_SPI_CLEAR_MODFFLAG(&hspi1);
      __HAL_SPI_CLEAR_FREFLAG(&hspi1);
  }
   if(hspi1.ErrorCode != HAL_SPI_ERROR_NONE)
   {
	   hspi1.State = HAL_SPI_STATE_READY;
   }
   /* Check if the SPI is already enabled */
   if ((hspi1.Instance->CR1 & SPI_CR1_SPE) != SPI_CR1_SPE)
   {
     /* Enable SPI peripheral */
     __HAL_SPI_ENABLE(&hspi1);
   }
}
/**
  * @brief This function handles SPI2 global interrupt.
  */
void SPI2_IRQHandler(void)
{
  /* USER CODE BEGIN SPI2_IRQn 0 */

  /* USER CODE END SPI2_IRQn 0 */
  HAL_SPI_IRQHandler(&hspi2);
  /* USER CODE BEGIN SPI2_IRQn 1 */

  /* USER CODE END SPI2_IRQn 1 */
}
void HAL_SPI_TxCpltCallback(SPI_HandleTypeDef *hspi)
{
	if(hspi->Instance == hspi2.Instance)
	{
		/* If there are additional elements in the transmit buffer, load them. */
		if( bLoadingTxBufferUI == INACTIVE )
		{
			int iElementsInBuffer = RingBuffer_GetCount((RINGBUFF_T *)&stSPI_AU_txring);
			if( iElementsInBuffer )
			{
				iElementsInBuffer = ( iElementsInBuffer > SPI_MAX_PACKET_SIZE__WORD ) ? SPI_MAX_PACKET_SIZE__WORD : iElementsInBuffer;
				RingBuffer_PopMult((RINGBUFF_T *)&stSPI_AU_txring, (uint16_t *)auwSPI_AU_TempTxbuff, iElementsInBuffer);
				__HAL_SPI_DISABLE(hspi);
				HAL_SPI_Transmit_IT(hspi, (uint8_t *)auwSPI_AU_TempTxbuff, iElementsInBuffer);
			}
		}
	}
}
void HAL_SPI_RxCpltCallback(SPI_HandleTypeDef *hspi)
{
	// Converted to custom
}
void HAL_SPI_ErrorCallback(SPI_HandleTypeDef *hspi)
{
	if(hspi->Instance == hspi2.Instance)
	{
		//		if( hspi->ErrorCode & HAL_SPI_ERROR_UDR ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__UNDERRUN]++;
		//		else
		if( hspi->ErrorCode & HAL_SPI_ERROR_MODF ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__MODE]++;
		else if( hspi->ErrorCode & HAL_SPI_ERROR_FRE ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__FRAME]++;
		else if( hspi->ErrorCode & HAL_SPI_ERROR_CRC ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__CRC]++;
		else if( hspi->ErrorCode & HAL_SPI_ERROR_OVR ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__RX_OVERFLOW]++;
		else if( hspi->ErrorCode != HAL_SPI_ERROR_NONE ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__UNKNOWN]++;
		hspi->ErrorCode = HAL_SPI_ERROR_NONE;

		hspi->State = HAL_SPI_STATE_READY;
		/* If there are additional elements in the transmit buffer, load them. */
		if( bLoadingTxBufferUI == INACTIVE )
		{
			int iElementsInBuffer = RingBuffer_GetCount((RINGBUFF_T *)&stSPI_AU_txring);
			if( iElementsInBuffer )
			{
				iElementsInBuffer = ( iElementsInBuffer > SPI_MAX_PACKET_SIZE__WORD ) ? SPI_MAX_PACKET_SIZE__WORD : iElementsInBuffer;
				RingBuffer_PopMult((RINGBUFF_T *)&stSPI_AU_txring, (uint16_t *)auwSPI_AU_TempTxbuff, iElementsInBuffer);
				__HAL_SPI_DISABLE(hspi);
				HAL_SPI_Transmit_IT(hspi, (uint8_t *)auwSPI_AU_TempTxbuff, iElementsInBuffer);
			}
		}
	}
}
/* @fn uint16_t SPI_TX_GetCounter(en_spi_counter eCounter)
 * @brief Returns requested status counter
 * @param None
 * @return None
 */
uint16_t SPI_TX_GetCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	return auwStatusCounter_SPI_AU_TX[eCounter];
}
void SPI_TX_IncrementCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	if(eDest == SPI_AU)
	{
		auwStatusCounter_SPI_AU_TX[eCounter]++;
	}
}
/* @fn uint16_t SPI_RX_GetCounter(en_spi_counter eCounter)
 * @brief Returns requested status counter
 * @param None
 * @return None
 */
uint16_t SPI_RX_GetCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	return auwStatusCounter_SPI_AU_RX[eCounter];
}
void SPI_RX_IncrementCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	if(eDest == SPI_AU)
	{
		auwStatusCounter_SPI_AU_RX[eCounter]++;
	}
}

/* @fn en_pass_fail SPI_AU_LoadToTxRB(uint16_t *puwData, uint16_t uwNumBytes)
 * @brief Initializes the SPI peripheral for A to UI MCU communication.
 */
en_pass_fail SPI_AU_LoadToTxRB(uint16_t *puwData, uint16_t uwNumItems)
{
	bLoadingTxBufferUI = ACTIVE;
	if( hspi2.State == HAL_SPI_STATE_READY )
	{
		memcpy((uint16_t *)auwSPI_AU_TempTxbuff, puwData, uwNumItems*SPI_WORD_SIZE__BYTE);
		if( HAL_SPI_Transmit_IT(&hspi2, (uint8_t *)auwSPI_AU_TempTxbuff, uwNumItems) == HAL_OK )
		{
			bLoadingTxBufferUI = INACTIVE;
			auwStatusCounter_SPI_AU_TX[SPI_COUNTER__TX]++;
			return PASS;
		}
	}
	if( RingBuffer_InsertMult((RINGBUFF_T *)&stSPI_AU_txring, puwData, uwNumItems) )
	{
		bLoadingTxBufferUI = INACTIVE;
		auwStatusCounter_SPI_AU_TX[SPI_COUNTER__TX]++;
		return PASS;
	}
	else
	{
		bLoadingTxBufferUI = INACTIVE;
		auwStatusCounter_SPI_AU_TX[SPI_COUNTER__TX_OVERFLOW]++;
		return FAIL;
	}
}
/* @fn en_pass_fail SPI_AU_UnloadFromRxRB(uint16_t *puwData)
 * @brief Pops the oldest item from the RX ring buffer if available and returns a 0. Returns 1 otherwise
 */
en_pass_fail SPI_AU_UnloadFromRxRB(uint16_t *puwData)
{
	if( RingBuffer_Pop((RINGBUFF_T *)&stSPI_AU_rxring, puwData) ) return PASS;
	else return FAIL;
}
/* USER CODE END 1 */
