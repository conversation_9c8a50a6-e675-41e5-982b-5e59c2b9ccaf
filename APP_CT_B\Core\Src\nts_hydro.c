#include "nts_hydro.h"
#include "sr_position.h"
#include "param_def.h"
#include "operation_def.h"
#include "car_output_def.h"
#include "car_input_def.h"
#include "compliance_test_def.h"
#include "dg_car_mr_a7.h"

void NTS_Hydro_Init(nts_hydro_t * const self)
{

}

void NTS_Hydro_Run(nts_hydro_t * const self)
{
  /* Generate debounced input signals */
  uint32_t uiPosition_05mm = Position_GetPositionNTS_05mm();
  int16_t uwSpeed_fpm = Position_GetSpeedNTS_fpm();

  uint32_t uiPos_UNTS_05mm = Param_ReadValue_24Bit(CPARAM24__UNTS_Position_05mm);
  uint32_t uiPos_DNTS_05mm = Param_ReadValue_24Bit(CPARAM24__DNTS_Position_05mm);
  if( Operation_GetManualOperation() != MANUAL_OP__OFF && Operation_GetManualOperation() != MANUAL_OP__UNKNOWN )
  {
     uiPos_UNTS_05mm = Param_ReadValue_24Bit(CPARAM24__UNTS_Position_Insp_05mm);
     uiPos_DNTS_05mm = Param_ReadValue_24Bit(CPARAM24__DNTS_Position_Insp_05mm);
  }
  uint8_t bUNTS = ( uiPosition_05mm >= uiPos_UNTS_05mm );
  uint8_t bDNTS = ( uiPosition_05mm <= uiPos_DNTS_05mm );

  switch(NTS_CAST(self)->eState)
  {
     case NTS_STATE__CHECKING: /* Checking for trip event */
        /* If the car is past the NTS trip point, trip NTS */
        if( ( bUNTS == ACTIVE )
         && ( CarOutput_GetOutputValue(COUT__VALVE_UH) || CarInput_GetInputValue(CIN__VALVE_UH) ) )
        {
           NTS_CAST(self)->bTripped_UNTS = ACTIVE;
           NTS_CAST(self)->eState = NTS_STATE__TRIPPED;
        }
        else if( ( bDNTS == ACTIVE )
            && ( CarOutput_GetOutputValue(COUT__VALVE_DH) || CarInput_GetInputValue(CIN__VALVE_DH) ) )
        {
          NTS_CAST(self)->bTripped_DNTS = ACTIVE;
          NTS_CAST(self)->eState = NTS_STATE__TRIPPED;
        }
        else
        {
          NTS_CAST(self)->bTripped_UNTS = INACTIVE;
          NTS_CAST(self)->bTripped_DNTS = INACTIVE;
        }
        break;
     case NTS_STATE__TRIPPED: /* NTS tripped, awaiting car to stop */
        if( uwSpeed_fpm == 0 )
        {
          NTS_CAST(self)->eState = NTS_STATE__CHECKING;
          NTS_CAST(self)->bTripped_UNTS = INACTIVE;
          NTS_CAST(self)->bTripped_DNTS = INACTIVE;
        }
        else if( NTS_CAST(self)->bTripped_UNTS )
        {
          Alarms_SetAlarm(CALM__MCU_NTS_UP);
        }
        else if( NTS_CAST(self)->bTripped_DNTS )
        {
          Alarms_SetAlarm(CALM__MCU_NTS_DOWN);
        }
        break;
     default:
       NTS_CAST(self)->eState = NTS_STATE__CHECKING;
       break;
  }
  en_active_inactive bOverrideTerminalLimits = Operation_GetOverrideTerminalLimits();
  en_active_inactive bBypassForTest = (Operation_GetSemiAutoOperation() == SEMIAUTO_OP__TESTING)
        && (Comp_Test_GetState_MRA() == COMP_TEST_STATE__TSRD_MOV_TOP) && CarInput_GetInputValue(CIN__MR_DIP_07);
  if( bOverrideTerminalLimits || bBypassForTest )
  {
    NTS_CAST(self)->eState = NTS_STATE__CHECKING;
    NTS_CAST(self)->bTripped_UNTS = INACTIVE;
    NTS_CAST(self)->bTripped_DNTS = INACTIVE;
  }
}

