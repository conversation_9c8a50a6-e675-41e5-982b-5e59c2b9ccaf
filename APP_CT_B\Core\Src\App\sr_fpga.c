/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file           : sr_fpga.c
* @version        : 1.0.0
* @brief This subroutine updates features relating to the on board FPGA redundant safety processor
* @details This subroutine updates on board FPGAs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include <stdio.h>
#include "sr_fpga.h"
#include "sr_position.h"
#include "sr_NTS.h"
//#include "nts_hydro.h"
//#include "nts_traction.h"
#include "fpga_def.h"
#include "app_spi.h"
#include "app_gpio.h"
#include "dg_car_mr_a7.h"
#include "dg_car_ct_b4.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/
//static void Init(void);
//static void Run(void);

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (10)
#define SUBROUTINE_RUN_INTERVAL_1MS                (20)
#define TRANSMIT_TIMEOUT__1MS                      (10)

#define FPGA_MESSAGE_RESEND_RATE_1MS               (100)
#define FPGA_OFFLINE_TIMEOUT_1MS                   (3000)

#define FPGA_TOGGLE_TIMEOUT_1MS                    (10000)
#define FPGA_TOGGLE_MIN_SAMPLE_TIME_20MS           (5) /* Minimum amount of time each input must be seen in the pass state during the inactive snapshot phase */

#define FPGA_CROSSCHECK_TIMEOUT_20MS               (100) /* How long the FPGA and MCU input status must be in disagreement before a fault occurs */

#define NUM_CT_TOGGLE_FAULTS                       (42)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/
/* State machine control */
typedef struct
{
   uint8_t aucRxSPIBuf[FPGA_SPI_BUFFER_SIZE__BYTES];
   uint8_t aucTxSPIBuf[FPGA_SPI_BUFFER_SIZE__BYTES];

   uint8_t aucPassingDebounce_20ms[NUM_CT_TOGGLE_FAULTS];                  /* Array for debouncing the time each toggle input was seen in a passing state during the end of run toggle check */

   uint8_t aucXCheckDebounce_20ms[NUM_FPGA_CT_INPUTS];                     /* Array of counters for debouncing input crosscheck violations */

   en_fpga_toggle_states eToggleState_MCU;                                 /* Tracks the MCU side toggle state */
   en_fpga_toggle_states eToggleState_FPGA;                                /* Tracks the FPGA side toggle state */

   en_car_fault eToggleFault;                                              /* Tracks the fault marking the first toggled input encountered that failed the toggle check  */

   uint16_t uwToggleTimer_1ms;                                             /* Tracks how long the toggle process has been in progress (for toggle timeout) */
   uint16_t uwOfflineTimer_1ms;                                            /* Checks if no valid packet ahs been received within a certain period */
   uint16_t uwResendTimer_1ms;                                             /* Forces resend of messages to the fpga */

   uint16_t uwLastRXCounter;                                               /* Tracks the last cycle's RX counter for confirmation transmission was completed */
   uint16_t uwLastTXCounter;                                               /* Tracks the last cycle's RX counter for confirmation transmission was completed */
   uint16_t uwLastErrorCounter;                                            /* Tracks the sum of the last cycle's  SPI3_GetCounter errors. If they've changed, increments ucRxErrorCounter_MCU */

   uint8_t ucRxErrorCounter_MCU;                                           /* Tracks number of number of packet validation errors seen by the MCU */
   uint8_t ucRxErrorCounter_FPGA;                                          /* Tracks number of number of packet validation errors seen by the FPGA */
   uint16_t uwVersion;                                                     /* Reported FPGA version */
   en_fpga_ct_errors eError;

   uint8_t ucRunCycleLag;                                                  /* Tracks estimated number of run cycles this SR was delayed in order to compensate transmit timers */
} st_ct_fpga_control;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
//st_subroutine gstSR_FPGA =
//{
//   .pfnInit = Init,
//   .pfnRun = Run,
//};
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_ct_fpga_control stControlFPGA;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @brief Loads the FPGA status datagram which will be written to the datagram scheduler for transmission to other diagnostics nodes */
void FPGA_LoadDatagram_FPGA_Status_CTB(un_datagram *punDatagram)
{
   punDatagram->auw16[0] = stControlFPGA.uwVersion & FPGA_VERSION_PACKET_BITMASK;
   if( stControlFPGA.uwOfflineTimer_1ms < FPGA_OFFLINE_TIMEOUT_1MS )
   {
      punDatagram->auw16[0] |= FPGA_ONLINE_PACKET_BITMASK;
   }
   punDatagram->auc8[2] = stControlFPGA.eToggleState_FPGA & FPGA_TOGGLE_STATE_PACKET_BITMASK;
   punDatagram->auc8[2] |= ( stControlFPGA.eToggleState_MCU & FPGA_TOGGLE_STATE_PACKET_BITMASK ) << 4;
   punDatagram->auc8[3] = stControlFPGA.eError;
   punDatagram->auc8[4] = stControlFPGA.ucRxErrorCounter_FPGA;
   punDatagram->auc8[5] = stControlFPGA.ucRxErrorCounter_MCU;

   uint8_t *pucInputBitmap = FPGA_GetInputBitmapPointer_CTB();
   // 3 bytes of input data
   punDatagram->auc8[7] = pucInputBitmap[0];
   punDatagram->auc8[8] = pucInputBitmap[1];
   punDatagram->auc8[9] = pucInputBitmap[2];
}

/* @brief Transmit and read back byte arrays to/from the FPGA chip.
 * @return eError, returns 1 if the request has failed
 */
static en_pass_fail SPITransfer(void)
{
   en_pass_fail eError;
   // RK TODO: determine how best to control NSS signal. For now, only working is to enable/disable SPI3.
   //HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_RESET);
   HAL_GPIO_WritePin(GPIOA,GPIO_PIN_15,GPIO_PIN_RESET);//HAL_SPI_MspInit(&hspi3); // Drive NSS low.
   //__disable_irq();
   HAL_StatusTypeDef eStatus = HAL_SPI_TransmitReceive(&hspi3, stControlFPGA.aucTxSPIBuf, stControlFPGA.aucRxSPIBuf, FPGA_SPI_BUFFER_SIZE__BYTES, TRANSMIT_TIMEOUT__1MS);
   //__enable_irq();
   //HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_SET);
   HAL_GPIO_WritePin(GPIOA,GPIO_PIN_15,GPIO_PIN_SET);//HAL_SPI_MspDeInit(&hspi3); // Drive NSS high.
   if( eStatus == HAL_OK )
   {
      eError = PASS;
   }
   else
   {
      printf("RD: %u\n", eStatus);
      eError = FAIL;
   }
   return eError;
}

/* @brief Populate the SPI packet to be sent to FPGA.
 * @return None
 */
static void SetTxPacket(void)
{
   // Set header section
   stControlFPGA.aucTxSPIBuf[0] = 0xFF;
   stControlFPGA.aucTxSPIBuf[1] = 0x00;
   stControlFPGA.aucTxSPIBuf[2] = 0x00;
   stControlFPGA.aucTxSPIBuf[3] = 0x00;

   // Set data section
   // byte4 init
   stControlFPGA.aucTxSPIBuf[4] = 0;
   // byte4 bit7 intentionally cleared
   // byte4 bit6
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__IC_STOP_SWITCH)) << 6;
   // byte4 bit5
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__FIRE_STOP_SWITCH)) << 5;
   // byte4 bit4
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__HA_INSPECTION)) << 4;
   // byte4 bit3
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__IC_INSPECTION)) << 3;
   // byte4 bit2
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__ESC_HATCH)) << 2;
   // byte4 bit1
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__CT_STOP_SWITCH)) << 1;
   // byte4 bit0
   stControlFPGA.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__CT_INSPECTION));
   // byte5 init
   stControlFPGA.aucTxSPIBuf[5] = 0;
   // byte5 bit7
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__GATESWITCH_FRONT)) << 7;
   // byte5 bit6
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__GATESWITCH_REAR)) << 6;
   // byte5 bit5
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DC_IN_06)) << 5;
   // byte5 bit4
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DC_IN_07)) << 4;
   // byte5 bit3
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DC_IN_08)) << 3;
   // byte5 bit2
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DIP_01)) << 2;
   // byte5 bit1
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DIP_02)) << 1;
   // byte5 bit0
   stControlFPGA.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DIP_03));
   // byte6 init
   stControlFPGA.aucTxSPIBuf[6] = 0;
   // byte6 bit7
   stControlFPGA.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DIP_04)) << 7;
   // byte6 bit6
   stControlFPGA.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DIP_05)) << 6;
   // byte6 bit5
   stControlFPGA.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__REAR_DOOR_ENABLE)) << 5;
   // byte6 bit4
   stControlFPGA.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__DIP_07)) << 4;
   // byte6 bit3
   stControlFPGA.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_GetCarInputFromFPGAInput_CTB(FPGA_CT_INPUT__TRACTION_ENABLE)) << 3;
   // duplicate data for validation
   stControlFPGA.aucTxSPIBuf[12] = stControlFPGA.aucTxSPIBuf[4];
   stControlFPGA.aucTxSPIBuf[13] = stControlFPGA.aucTxSPIBuf[5];
   stControlFPGA.aucTxSPIBuf[14] = stControlFPGA.aucTxSPIBuf[6];
   stControlFPGA.aucTxSPIBuf[15] = stControlFPGA.aucTxSPIBuf[7];
   stControlFPGA.aucTxSPIBuf[16] = stControlFPGA.aucTxSPIBuf[8];
   stControlFPGA.aucTxSPIBuf[17] = stControlFPGA.aucTxSPIBuf[9];
   stControlFPGA.aucTxSPIBuf[18] = stControlFPGA.aucTxSPIBuf[10];
   stControlFPGA.aucTxSPIBuf[19] = stControlFPGA.aucTxSPIBuf[11];

   // Set footer section
   stControlFPGA.aucTxSPIBuf[20] = 0x80;
   stControlFPGA.aucTxSPIBuf[21] = 0x00;
   stControlFPGA.aucTxSPIBuf[22] = 0x00;
   stControlFPGA.aucTxSPIBuf[23] = 0x00;
}

/* @brief Validate SPI packet received from FPGA, and extract relevant data.
 * @return en_pass_fail value depending on pass or fail when parsing data.
 */
static en_pass_fail ProcessRxPacket(void)
{
   if( !(stControlFPGA.aucRxSPIBuf[0] == 0xFF && stControlFPGA.aucRxSPIBuf[1] == 0x00
         && stControlFPGA.aucRxSPIBuf[2] == 0x00 && stControlFPGA.aucRxSPIBuf[3] == 0x00) )
   {
      fpga_printf("Error: invalid packet received: header! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return FAIL;
   }
   if( !(stControlFPGA.aucRxSPIBuf[28] == 0x80 && stControlFPGA.aucRxSPIBuf[29] == 0x00
         && stControlFPGA.aucRxSPIBuf[30] == 0x00 && stControlFPGA.aucRxSPIBuf[31] == 0x00) )
   {
      fpga_printf("Error: invalid packet received: footer! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return FAIL;
   }
   for( uint8_t ucI = 4; ucI < 16; ucI++)
   {
      if( stControlFPGA.aucRxSPIBuf[ucI] != stControlFPGA.aucRxSPIBuf[ucI+12] )
      {
         fpga_printf("Error: invalid packet received: data corruption! %s fileLn%d\n", __FUNCTION__, __LINE__);
         return FAIL;
      }
   }

   // Parse data section1
   if( stControlFPGA.aucRxSPIBuf[4] & 0x80 ) // byte4 bit7
   {
      fpga_printf("Error: invalid packet received: data1 1st cleared bit! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return FAIL;
   }
   uint8_t *pucInputBitmap = FPGA_GetInputBitmapPointer_CTB();
   // byte4 bit6
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__ESC_HATCH, (stControlFPGA.aucRxSPIBuf[4] & 0x40) > 0);
   // byte4 bit5
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__CT_STOP_SWITCH, (stControlFPGA.aucRxSPIBuf[4] & 0x20) > 0);
   // byte4 bit4
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__CT_INSPECTION, (stControlFPGA.aucRxSPIBuf[4] & 0x10) > 0);
   // byte4 bit3
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__GATESWITCH_FRONT, (stControlFPGA.aucRxSPIBuf[4] & 0x08) > 0);
   // byte4 bit2
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__GATESWITCH_REAR, (stControlFPGA.aucRxSPIBuf[4] & 0x04) > 0);
   // byte4 bit1
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DC_IN_06, (stControlFPGA.aucRxSPIBuf[4] & 0x02) > 0);
   // byte4 bit0
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DC_IN_07, (stControlFPGA.aucRxSPIBuf[4] & 0x01) > 0);
   // byte5 bit7
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DC_IN_08, (stControlFPGA.aucRxSPIBuf[5] & 0x80) > 0);
   // byte5 bit6
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DIP_01, (stControlFPGA.aucRxSPIBuf[5] & 0x40) > 0);
   // byte5 bit5
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DIP_02, (stControlFPGA.aucRxSPIBuf[5] & 0x20) > 0);
   // byte5 bit4
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DIP_03, (stControlFPGA.aucRxSPIBuf[5] & 0x10) > 0);
   // byte5 bit3
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DIP_04, (stControlFPGA.aucRxSPIBuf[5] & 0x08) > 0);
   // byte5 bit2
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DIP_05, (stControlFPGA.aucRxSPIBuf[5] & 0x04) > 0);
   // byte5 bit1
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__REAR_DOOR_ENABLE, (stControlFPGA.aucRxSPIBuf[5] & 0x02) > 0);
   // byte5 bit0
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DIP_07, (stControlFPGA.aucRxSPIBuf[5] & 0x01) > 0);
   // byte6 bit7
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__TRACTION_ENABLE, (stControlFPGA.aucRxSPIBuf[6] & 0x80) > 0);
   // byte6 bit6
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__NTS_ACTIVE, (stControlFPGA.aucRxSPIBuf[6] & 0x40) > 0);
   // byte6 bit5
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__DOOR_ZONE, (stControlFPGA.aucRxSPIBuf[6] & 0x20) > 0);
   // byte6 bit4
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__SAFETY_TOGGLE, (stControlFPGA.aucRxSPIBuf[6] & 0x10) > 0);
   // byte6 bit3
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__IC_STOP_BYPASS, (stControlFPGA.aucRxSPIBuf[6] & 0x08) > 0);
   // byte6 bit2
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__IC_STOP_SWITCH, (stControlFPGA.aucRxSPIBuf[6] & 0x04) > 0);
   // byte6 bit1
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__FIRE_STOP_SWITCH, (stControlFPGA.aucRxSPIBuf[6] & 0x02) > 0);
   // byte6 bit0
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__HA_INSPECTION, (stControlFPGA.aucRxSPIBuf[6] & 0x01) > 0);
   // byte7 bit7
   System_SetBitByIndex(pucInputBitmap, FPGA_CT_INPUT__IC_INSPECTION, (stControlFPGA.aucRxSPIBuf[7] & 0x80) > 0);
   // byte7 bit6 thru bit0 unused

   // Parse data section2
   if( stControlFPGA.aucRxSPIBuf[8] & 0x80 ) // byte8 bit7
   {
      fpga_printf("Error: invalid packet received: data2 1st cleared bit! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return FAIL;
   }
   // bytes 8 and 9 unused
   // byte 10 and 11
   uint8_t ucVerMSB = stControlFPGA.aucRxSPIBuf[10];
   uint8_t ucVerLSB = stControlFPGA.aucRxSPIBuf[11] >> 4;
   if( !(ucVerMSB & 0x80) ) // board firmware type (0 = MR, 1 = CT)
   {
      fpga_printf("Error: FPGA firmware/board mismatch! %s fileLn%d\n", __FUNCTION__, __LINE__);
      // TODO: set a fault
   }
   ucVerMSB &= 0x7F;
   stControlFPGA.uwVersion = ucVerMSB;
   stControlFPGA.uwVersion = (stControlFPGA.uwVersion << 4) + ucVerLSB;
   stControlFPGA.eToggleState_FPGA = stControlFPGA.aucRxSPIBuf[11] & 0x0F;

   // Parse data section3
   if( stControlFPGA.aucRxSPIBuf[12] & 0x80 ) // byte12 bit7
   {
      fpga_printf("Error: invalid packet received: data3 1st cleared bit! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return FAIL;
   }
   stControlFPGA.eError = stControlFPGA.aucRxSPIBuf[12];
   stControlFPGA.ucRxErrorCounter_FPGA = stControlFPGA.aucRxSPIBuf[13];

   return PASS;
}

/* @brief Handle FPGA errors as car system faults. */
static void HandleError(void)
{
   if( FPGA_GetOnlineFlag_CTB() )
   {
#if 0 // CT PLD sends all its errors to MR at this time, so no need to set them here.
      en_car_fault eFault = FPGA_GetCarFaultFromFPGAError_CTB( FPGA_GetError_CTB() );
      if( eFault > CFLT__NONE && eFault < NUM_CAR_FAULT )
      {
         Faults_SetFault_PLD(eFault);
      }
#endif
   }
   else
   {
      Faults_SetFault(CFLT__FPGA_OFFLINE_CT);
   }
}

/* @brief Check if MCU input states match that of those recived from FPGA. */
static void MCUXcheck(void)
{
   /* Checks input states and sets fault if they don't match after a debounce period. */
   for(en_fpga_ct_inputs eInput = 0; eInput <= NUM_FPGA_CT_INPUTS; eInput++)
   {
      en_car_fault eFault = FPGA_GetXcheckFaultFromInput_CTB(eInput);
      en_car_inputs eCarInput = FPGA_GetCarInputFromFPGAInput_CTB(eInput);
      if( eFault != NUM_CAR_FAULT && eCarInput != NUM_CAR_INPUTS )
      {
         uint8_t bMCUActive = CarInput_GetInputValue(eCarInput);
         uint8_t bFPGAActive = FPGA_GetInputBit_CTB(eInput);
         if(bMCUActive != bFPGAActive)
         {
            if(stControlFPGA.aucXCheckDebounce_20ms[eInput] < FPGA_CROSSCHECK_TIMEOUT_20MS)
            {
               stControlFPGA.aucXCheckDebounce_20ms[eInput] += 1;
            }
            else
            {
               Faults_SetFault(eFault);
            }
         }
         else
         {
            stControlFPGA.aucXCheckDebounce_20ms[eInput] = 0;
         }
      }
   }
}

//#define fpga_ct_stat_dbg
#ifdef fpga_ct_stat_dbg
static void debugFPGAStatus(void)
{
   static uint32_t uiFPGADebugTimer_1ms;
   if( uiFPGADebugTimer_1ms >= 1000 )
   {
      for(int i = 0; i < 8; i++){
         for(int j = 0; j < 4; j++){
            printf("%02x,", stControlFPGA.aucRxSPIBuf[(i*4)+j]);
         }
         printf("\n");
      }
      printf("FPGA_GetVersion_CTB():%d\n", FPGA_GetVersion_CTB());
      printf("FPGA_GetOnlineFlag_CTB():%d\n", FPGA_GetOnlineFlag_CTB());
      printf("FPGA_GetToggleState_FPGA_CTB():%d\n", FPGA_GetToggleState_FPGA_CTB());
      printf("FPGA_GetToggleState_MCU_CTB():%d\n", FPGA_GetToggleState_MCU_CTB());
      printf("FPGA_GetError_CTB():%s\n", FPGA_GetErrorString_CTB(FPGA_GetError_CTB()));
      printf("FPGA_GetRXErrorCounter_FPGA_CTB():%d\n", FPGA_GetRXErrorCounter_FPGA_CTB());
      printf("FPGA_GetRXErrorCounter_MCU_CTB():%d\n", FPGA_GetRXErrorCounter_MCU_CTB());
      printf("FPGA Inputs CTB:\n");
      for(en_fpga_ct_inputs eI = 0; eI < NUM_FPGA_CT_INPUTS; eI++)
      {
         en_car_inputs eMCURef = FPGA_GetCarInputFromFPGAInput_CTB(eI);
         if( eMCURef < NUM_CAR_INPUTS )
         {
            printf(" %s:%d (ref:%d)\n", FPGA_GetInputString_CTB(eI), FPGA_GetInputBit_CTB(eI), CarInput_GetInputValue(eMCURef));
         }
         else
         {
            printf(" %s:%d (ref:NA)\n", FPGA_GetInputString_CTB(eI), FPGA_GetInputBit_CTB(eI));
         }
      }
      printf("----------------\n");
      uiFPGADebugTimer_1ms = 0;
   }
   else
   {
      uiFPGADebugTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
   }
}
#endif

/* @fn oid vFPGA_Task(void *pvParameters)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
void vFPGA_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		if(Param_ReadValue_1Bit(CPARAM1__FPGA_OLD)){
			FPGA_OLD_Run();
		}
		else{
			static uint16_t uwFPGAMessageResendTimer_1ms;

		   GPIO_WriteOutput(enLOCAL_OUT__PIO_01, NTS_GetTrippedFlag());
		   GPIO_WriteOutput(enLOCAL_OUT__PIO_02, Position_GetDZ_Chan_B());
		   GPIO_WriteOutput(enLOCAL_OUT__PIO_04, Safety_CheckIfICStopBypassed_MRA());

			if( Param_GetState() != PARAM_STATE__STARTUP )
			{
				// Make sure MCU rear door enable matches PLD rear door enable
				if( (en_active_inactive)Door_GetRearDoorEnabled() != CarInput_GetInputValue(CIN__CT_DIP_06) )
				{
					Faults_SetFault(CFLT__CT_FPGA_REAR_DOOR_EN);
				}
				// Make sure MCU traction enable matches PLD traction enable
				en_active_inactive eTractionEnabled = System_GetControlMode() == CONTROL_MODE__TRACTION;
				if( eTractionEnabled != CarInput_GetInputValue(CIN__CT_DIP_08) ) 
				{
					Faults_SetFault(CFLT__CT_FPGA_TRACTION_EN);
				}
			}

			if( uwFPGAMessageResendTimer_1ms >= FPGA_MESSAGE_RESEND_RATE_1MS )
			{
				en_pass_fail eTransRet = FAIL;
				en_pass_fail eProcRet = FAIL;
				SetTxPacket();
				memset(stControlFPGA.aucRxSPIBuf, 0, 4); // clear the packet header
				eTransRet = SPITransfer();
				eProcRet = ProcessRxPacket();
				if( eTransRet == FAIL || eProcRet == FAIL )
				{
					stControlFPGA.uwOfflineTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
				}
				else
				{
					stControlFPGA.uwOfflineTimer_1ms = 0;
				}
				if( stControlFPGA.uwOfflineTimer_1ms >= FPGA_OFFLINE_TIMEOUT_1MS )
				{
					stControlFPGA.uwOfflineTimer_1ms = FPGA_OFFLINE_TIMEOUT_1MS;
				}
				// Rx error accounting and prevent it from wrapping.
				if( eProcRet == FAIL && stControlFPGA.ucRxErrorCounter_MCU < 255 )
				{
					stControlFPGA.ucRxErrorCounter_MCU++;
				}
				uwFPGAMessageResendTimer_1ms = 0;
			}
			else
			{
				uwFPGAMessageResendTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
			}
			MCUXcheck();
			HandleError();
	#ifdef fpga_ct_stat_dbg
			debugFPGAStatus();
	#endif
		}
		vTaskDelay(FPGA_TASK_DELAY);
	}
}
