/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_position.h
* @version 		  : 1.0.0
* @brief Subroutine position
* @details Subroutine position
*****************************************************************************/
#ifndef _SR_POSITION_H_
#define _SR_POSITION_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "version.h"
#include "car_alarms_def.h"
#include "car_faults_def.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
//extern st_subroutine gstSR_Position;
/******************************************************************************
* Function Prototypes
*******************************************************************************/
void Position_DefaultToStartingLearnPosition(void);
void Position_SetSelectorReset(en_active_inactive bReset);
en_active_inactive Position_GetSelectorReset(void);
en_car_fault Position_GetLandingSystemFault(void);
en_car_alarm Position_GetLandingSystemAlarm(void);
void vPosition_Task(void *pvParameters);
#endif /* _SR_POSITION_H_ */
