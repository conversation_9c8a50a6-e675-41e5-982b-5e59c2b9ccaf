#MicroXplorer Configuration settings - do not modify
CAD.formats=[]
CAD.pinconfig=Project naming
CAD.provider=
FDCAN1.CalculateBaudRateNominal=2125000
FDCAN1.CalculateTimeBitNominal=470
FDCAN1.CalculateTimeQuantumNominal=94.11764705882354
FDCAN1.IPParameters=CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal
FDCAN2.CalculateBaudRateNominal=2125000
FDCAN2.CalculateTimeBitNominal=470
FDCAN2.CalculateTimeQuantumNominal=94.11764705882354
FDCAN2.IPParameters=CalculateTimeQuantumNominal,CalculateTimeBitNominal,CalculateBaudRateNominal
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32G473QET6
Mcu.Family=STM32G4
Mcu.IP0=CRC
Mcu.IP1=FDCAN1
Mcu.IP2=FDCAN2
Mcu.IP3=IWDG
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SPI1
Mcu.IP7=SPI2
Mcu.IP8=TIM1
Mcu.IPNb=9
Mcu.Name=STM32G473Q(B-C-E)Tx
Mcu.Package=LQFP128
Mcu.Pin0=PF0-OSC_IN
Mcu.Pin1=PB12
Mcu.Pin10=PB6
Mcu.Pin11=VP_CRC_VS_CRC
Mcu.Pin12=VP_IWDG_VS_IWDG
Mcu.Pin13=VP_TIM1_VS_ControllerModeReset
Mcu.Pin14=VP_TIM1_VS_ClockSourceINT
Mcu.Pin15=VP_TIM1_VS_ClockSourceITR
Mcu.Pin2=PB13
Mcu.Pin3=PB15
Mcu.Pin4=PG2
Mcu.Pin5=PG4
Mcu.Pin6=PA11
Mcu.Pin7=PA12
Mcu.Pin8=PG5
Mcu.Pin9=PB5
Mcu.PinsNb=16
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G473QETx
MxCube.Version=6.7.0
MxDb.Version=DB.6.0.70
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.FDCAN1_IT0_IRQn=true\:2\:0\:true\:false\:false\:true\:true\:false
NVIC.FDCAN2_IT0_IRQn=true\:1\:0\:true\:false\:false\:true\:true\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SPI1_IRQn=true\:4\:0\:true\:false\:false\:true\:true\:false
NVIC.SPI2_IRQn=true\:3\:0\:true\:false\:false\:true\:true\:false
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA11.Mode=FDCAN_Activate
PA11.Signal=FDCAN1_RX
PA12.Mode=FDCAN_Activate
PA12.Signal=FDCAN1_TX
PB12.GPIOParameters=GPIO_Speed
PB12.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB12.Mode=NSS_Signal_Hard_Output
PB12.Signal=SPI2_NSS
PB13.GPIOParameters=GPIO_Speed
PB13.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB13.Locked=true
PB13.Mode=TX_Only_Simplex_Unidirect_Master
PB13.Signal=SPI2_SCK
PB15.GPIOParameters=GPIO_Speed
PB15.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PB15.Mode=TX_Only_Simplex_Unidirect_Master
PB15.Signal=SPI2_MOSI
PB5.Mode=FDCAN_Activate
PB5.Signal=FDCAN2_RX
PB6.Mode=FDCAN_Activate
PB6.Signal=FDCAN2_TX
PF0-OSC_IN.Mode=HSE-External-Clock-Source
PF0-OSC_IN.Signal=RCC_OSC_IN
PG2.GPIOParameters=GPIO_Speed
PG2.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG2.Locked=true
PG2.Mode=RX_Only_Simplex_Unidirect_Slave
PG2.Signal=SPI1_SCK
PG4.GPIOParameters=GPIO_Speed
PG4.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG4.Locked=true
PG4.Mode=RX_Only_Simplex_Unidirect_Slave
PG4.Signal=SPI1_MOSI
PG5.GPIOParameters=GPIO_Speed
PG5.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PG5.Locked=true
PG5.Mode=NSS_Signal_Hard_Input
PG5.Signal=SPI1_NSS
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G473QETx
ProjectManager.FirmwarePackage=STM32Cube FW_G4 V1.5.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=APP_COP_EXP.ioc
ProjectManager.ProjectName=APP_COP_EXP
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-true-HAL-false,false-2-MX_GPIO_Init-GPIO-true-HAL-true,false-3-MX_CRC_Init-CRC-true-HAL-true,false-4-MX_IWDG_Init-IWDG-true-HAL-true,false-5-MX_TIM1_Init-TIM1-true-HAL-true,false-6-MX_FDCAN1_Init-FDCAN1-true-HAL-true,false-7-MX_SPI1_Init-SPI1-true-HAL-true,false-8-MX_SPI2_Init-SPI2-true-HAL-true,false-9-MX_FDCAN2_Init-FDCAN2-true-HAL-true
RCC.ADC12Freq_Value=170000000
RCC.ADC345Freq_Value=170000000
RCC.AHBFreq_Value=170000000
RCC.APB1Freq_Value=170000000
RCC.APB1TimFreq_Value=170000000
RCC.APB2Freq_Value=170000000
RCC.APB2TimFreq_Value=170000000
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=170000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=170000000
RCC.FDCANFreq_Value=170000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=170000000
RCC.HRTIM1Freq_Value=170000000
RCC.HSE_VALUE=25000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=170000000
RCC.I2C2Freq_Value=170000000
RCC.I2C3Freq_Value=170000000
RCC.I2C4Freq_Value=170000000
RCC.I2SFreq_Value=170000000
RCC.IPParameters=ADC12Freq_Value,ADC345Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CRSFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HRTIM1Freq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2SFreq_Value,LPTIM1Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,PLLM,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSourceVirtual,PREFETCH_ENABLE,PWRFreq_Value,QSPIFreq_Value,RNGFreq_Value,SAI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=170000000
RCC.LPUART1Freq_Value=170000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=16000000
RCC.PLLM=RCC_PLLM_DIV5
RCC.PLLN=68
RCC.PLLPoutputFreq_Value=170000000
RCC.PLLQoutputFreq_Value=170000000
RCC.PLLRCLKFreq_Value=170000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PREFETCH_ENABLE=1
RCC.PWRFreq_Value=170000000
RCC.QSPIFreq_Value=170000000
RCC.RNGFreq_Value=170000000
RCC.SAI1Freq_Value=170000000
RCC.SYSCLKFreq_VALUE=170000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=170000000
RCC.UART5Freq_Value=170000000
RCC.USART1Freq_Value=170000000
RCC.USART2Freq_Value=170000000
RCC.USART3Freq_Value=170000000
RCC.USBFreq_Value=170000000
RCC.VCOInputFreq_Value=5000000
RCC.VCOOutputFreq_Value=340000000
SPI1.DataSize=SPI_DATASIZE_16BIT
SPI1.Direction=SPI_DIRECTION_2LINES_RXONLY
SPI1.IPParameters=VirtualType,Mode,Direction,VirtualNSS,DataSize
SPI1.Mode=SPI_MODE_SLAVE
SPI1.VirtualNSS=VM_NSSHARD
SPI1.VirtualType=VM_SLAVE
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_4
SPI2.CalculateBaudRate=42.5 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate,VirtualNSS
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualNSS=VM_NSSHARD
SPI2.VirtualType=VM_MASTER
VP_CRC_VS_CRC.Mode=CRC_Activate
VP_CRC_VS_CRC.Signal=CRC_VS_CRC
VP_IWDG_VS_IWDG.Mode=IWDG_Activate
VP_IWDG_VS_IWDG.Signal=IWDG_VS_IWDG
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM1_VS_ClockSourceITR.Mode=TriggerSource_ITR1
VP_TIM1_VS_ClockSourceITR.Signal=TIM1_VS_ClockSourceITR
VP_TIM1_VS_ControllerModeReset.Mode=Reset Mode
VP_TIM1_VS_ControllerModeReset.Signal=TIM1_VS_ControllerModeReset
board=custom
isbadioc=false
