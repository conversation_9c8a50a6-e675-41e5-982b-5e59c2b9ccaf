/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    spi.c
  * @brief   This file provides code for the configuration
  *          of the SPI instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "app_spi.h"

/* USER CODE BEGIN 0 */
#include <string.h>
#include "network.h"
#include "main.h"
/* Variable Definitions ------------------------------------------------------------------*/
/* Structures for accessing spi ring buffers */
static volatile RINGBUFF_T stSPI_AB_txring; // TX A to B
static volatile RINGBUFF_T stSPI_AB_rxring; // RX B to A
static volatile RINGBUFF_T stSPI_AU_txring; // TX A to UI
static volatile RINGBUFF_T stSPI_AU_rxring; // RX UI to A

/* Underlying arrays for SPI ring buffers */
static volatile uint16_t auwSPI_AB_rxbuff[SPI_RX_RING_BUFFER_SIZE_WORD];
static volatile uint16_t auwSPI_AB_txbuff[SPI_TX_RING_BUFFER_SIZE_WORD];
static volatile uint16_t auwSPI_AU_rxbuff[SPI_RX_RING_BUFFER_SIZE_WORD];
static volatile uint16_t auwSPI_AU_txbuff[SPI_TX_RING_BUFFER_SIZE_WORD];

/* Temporary buffers for active transfer */
static volatile uint16_t auwSPI_TempTxbuff[SPI_MAX_PACKET_SIZE__WORD];

static volatile uint16_t auwStatusCounter_SPI_AB_RX[NUM_SPI_COUNTERS];// RX A to B
static volatile uint16_t auwStatusCounter_SPI_AB_TX[NUM_SPI_COUNTERS];// TX B to A
static volatile uint16_t auwStatusCounter_SPI_AU_RX[NUM_SPI_COUNTERS];// RX A to UI
static volatile uint16_t auwStatusCounter_SPI_AU_TX[NUM_SPI_COUNTERS];// TX UI to A

/* Flag indicating the TX ring buffer is being loaded and should not be pulled from */
static volatile en_active_inactive bLoadingTxBuffer;
static volatile en_active_inactive bLoadingTxBufferUI;
/* USER CODE END 0 */

SPI_HandleTypeDef hspi1;
SPI_HandleTypeDef hspi2;
SPI_HandleTypeDef hspi4;
SPI_HandleTypeDef hspi5;
SPI_HandleTypeDef hspi6;

/* SPI1 init function */
void MX_SPI1_Init(void)
{

  /* USER CODE BEGIN SPI1_Init 0 */
	RingBuffer_Init((RINGBUFF_T *)&stSPI_AB_txring, (uint16_t *)auwSPI_AB_txbuff, sizeof(uint16_t), SPI_TX_RING_BUFFER_SIZE_WORD);
  /* USER CODE END SPI1_Init 0 */

  /* USER CODE BEGIN SPI1_Init 1 */

  /* USER CODE END SPI1_Init 1 */
  hspi1.Instance = SPI1;
  hspi1.Init.Mode = SPI_MODE_MASTER;
  hspi1.Init.Direction = SPI_DIRECTION_2LINES_TXONLY;
  hspi1.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi1.Init.NSS = SPI_NSS_HARD_OUTPUT;
  hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_16;// 80 MHZ / 16 = 5 MHZ - ks note: setting this higher consitently caused CRC check failures on the RX side
  hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi1.Init.CRCPolynomial = 0x0;
  hspi1.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  hspi1.Init.NSSPolarity = SPI_NSS_POLARITY_LOW;
  hspi1.Init.FifoThreshold = SPI_FIFO_THRESHOLD_01DATA;
  hspi1.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi1.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi1.Init.MasterSSIdleness = SPI_MASTER_SS_IDLENESS_00CYCLE;
  hspi1.Init.MasterInterDataIdleness = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
  hspi1.Init.MasterReceiverAutoSusp = SPI_MASTER_RX_AUTOSUSP_DISABLE;
  hspi1.Init.MasterKeepIOState = SPI_MASTER_KEEP_IO_STATE_ENABLE;
  hspi1.Init.IOSwap = SPI_IO_SWAP_DISABLE;
  if (HAL_SPI_Init(&hspi1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI1_Init 2 */

  /* USER CODE END SPI1_Init 2 */

}
/* SPI2 init function */
void MX_SPI2_Init(void)
{

  /* USER CODE BEGIN SPI2_Init 0 */
	RingBuffer_Init((RINGBUFF_T *)&stSPI_AB_rxring, (uint16_t *)auwSPI_AB_rxbuff, sizeof(uint16_t), SPI_RX_RING_BUFFER_SIZE_WORD);
  /* USER CODE END SPI2_Init 0 */

  /* USER CODE BEGIN SPI2_Init 1 */

  /* USER CODE END SPI2_Init 1 */
  hspi2.Instance = SPI2;
  hspi2.Init.Mode = SPI_MODE_SLAVE;
  hspi2.Init.Direction = SPI_DIRECTION_2LINES_RXONLY;
  hspi2.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi2.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi2.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi2.Init.NSS = SPI_NSS_HARD_INPUT;
  hspi2.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi2.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi2.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi2.Init.CRCPolynomial = 0x0;
  hspi2.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  hspi2.Init.NSSPolarity = SPI_NSS_POLARITY_LOW;
  hspi2.Init.FifoThreshold = SPI_FIFO_THRESHOLD_01DATA;
  hspi2.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi2.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi2.Init.MasterSSIdleness = SPI_MASTER_SS_IDLENESS_00CYCLE;
  hspi2.Init.MasterInterDataIdleness = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
  hspi2.Init.MasterReceiverAutoSusp = SPI_MASTER_RX_AUTOSUSP_DISABLE;
  hspi2.Init.MasterKeepIOState = SPI_MASTER_KEEP_IO_STATE_DISABLE;
  hspi2.Init.IOSwap = SPI_IO_SWAP_DISABLE;
  if (HAL_SPI_Init(&hspi2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI2_Init 2 */
  /* Initiate Receive only spi that will perpetually unload bytes to a ring buffer. Adapted from HAL_SPI_Receive_IT */
  {
  __HAL_SPI_DISABLE(&hspi2);
  /* Set the number of data at current transfer */
  MODIFY_REG(hspi2.Instance->CR2, SPI_CR2_TSIZE, SPI_RX_TRANSFER_SIZE__WORD);

  /* Enable SPI peripheral */
  __HAL_SPI_ENABLE(&hspi2);

  /* Enable RXP, OVR, FRE, MODF interrupts */
  __HAL_SPI_ENABLE_IT(&hspi2, (SPI_IT_RXP | SPI_IT_OVR | SPI_IT_FRE | SPI_IT_MODF));
  }
  /* USER CODE END SPI2_Init 2 */

}
/* SPI4 init function */
void MX_SPI4_Init(void)
{

  /* USER CODE BEGIN SPI4_Init 0 */

  /* USER CODE END SPI4_Init 0 */

  /* USER CODE BEGIN SPI4_Init 1 */

  /* USER CODE END SPI4_Init 1 */
  hspi4.Instance = SPI4;
  hspi4.Init.Mode = SPI_MODE_MASTER;
  hspi4.Init.Direction = SPI_DIRECTION_2LINES;
  hspi4.Init.DataSize = SPI_DATASIZE_8BIT;
  hspi4.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi4.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi4.Init.NSS = SPI_NSS_HARD_OUTPUT;
  hspi4.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_2;// 80 MHZ / 16 = 5 MHZ - ks note: setting this higher consitently caused CRC check failures on the RX side
  hspi4.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi4.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi4.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi4.Init.CRCPolynomial = 0x0;
  hspi4.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  hspi4.Init.NSSPolarity = SPI_NSS_POLARITY_LOW;
  hspi4.Init.FifoThreshold = SPI_FIFO_THRESHOLD_01DATA;
  hspi4.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi4.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi4.Init.MasterSSIdleness = SPI_MASTER_SS_IDLENESS_00CYCLE;
  hspi4.Init.MasterInterDataIdleness = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
  hspi4.Init.MasterReceiverAutoSusp = SPI_MASTER_RX_AUTOSUSP_DISABLE;
  hspi4.Init.MasterKeepIOState = SPI_MASTER_KEEP_IO_STATE_DISABLE;
  hspi4.Init.IOSwap = SPI_IO_SWAP_DISABLE;
  if (HAL_SPI_Init(&hspi4) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI4_Init 2 */

  /* USER CODE END SPI4_Init 2 */

}
/* SPI5 init function */
void MX_SPI5_Init(void)
{

  /* USER CODE BEGIN SPI5_Init 0 */
	 RingBuffer_Init((RINGBUFF_T *)&stSPI_AU_rxring, (uint16_t *)auwSPI_AU_rxbuff, sizeof(uint16_t), SPI_RX_RING_BUFFER_SIZE_WORD);
  /* USER CODE END SPI5_Init 0 */

  /* USER CODE BEGIN SPI5_Init 1 */

  /* USER CODE END SPI5_Init 1 */
  hspi5.Instance = SPI5;
  hspi5.Init.Mode = SPI_MODE_SLAVE;
  hspi5.Init.Direction = SPI_DIRECTION_2LINES_RXONLY;
  hspi5.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi5.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi5.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi5.Init.NSS = SPI_NSS_HARD_INPUT;
  hspi5.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi5.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi5.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi5.Init.CRCPolynomial = 0x0;
  hspi5.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  hspi5.Init.NSSPolarity = SPI_NSS_POLARITY_LOW;
  hspi5.Init.FifoThreshold = SPI_FIFO_THRESHOLD_01DATA;
  hspi5.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi5.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi5.Init.MasterSSIdleness = SPI_MASTER_SS_IDLENESS_00CYCLE;
  hspi5.Init.MasterInterDataIdleness = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
  hspi5.Init.MasterReceiverAutoSusp = SPI_MASTER_RX_AUTOSUSP_DISABLE;
  hspi5.Init.MasterKeepIOState = SPI_MASTER_KEEP_IO_STATE_DISABLE;
  hspi5.Init.IOSwap = SPI_IO_SWAP_DISABLE;
  if (HAL_SPI_Init(&hspi5) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI5_Init 2 */
  /* Initiate Receive only spi that will perpetually unload bytes to a ring buffer. Adapted from HAL_SPI_Receive_IT */
  {
  __HAL_SPI_DISABLE(&hspi5);
  /* Set the number of data at current transfer */
  MODIFY_REG(hspi5.Instance->CR2, SPI_CR2_TSIZE, SPI_RX_TRANSFER_SIZE__WORD);

  /* Enable SPI peripheral */
  __HAL_SPI_ENABLE(&hspi5);

  /* Enable RXP, OVR, FRE, MODF interrupts */
  __HAL_SPI_ENABLE_IT(&hspi5, (SPI_IT_RXP | SPI_IT_OVR | SPI_IT_FRE | SPI_IT_MODF));
  }
  /* USER CODE END SPI5_Init 2 */

}
/* SPI6 init function */
void MX_SPI6_Init(void)
{

  /* USER CODE BEGIN SPI6_Init 0 */
	RingBuffer_Init((RINGBUFF_T *)&stSPI_AU_txring, (uint16_t *)auwSPI_AU_txbuff, sizeof(uint16_t), SPI_TX_RING_BUFFER_SIZE_WORD);
  /* USER CODE END SPI6_Init 0 */

  /* USER CODE BEGIN SPI6_Init 1 */

  /* USER CODE END SPI6_Init 1 */
  hspi6.Instance = SPI6;
  hspi6.Init.Mode = SPI_MODE_MASTER;
  hspi6.Init.Direction = SPI_DIRECTION_2LINES_TXONLY;
  hspi6.Init.DataSize = SPI_DATASIZE_16BIT;
  hspi6.Init.CLKPolarity = SPI_POLARITY_LOW;
  hspi6.Init.CLKPhase = SPI_PHASE_1EDGE;
  hspi6.Init.NSS = SPI_NSS_HARD_OUTPUT;
  hspi6.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_16;// 80 MHZ / 16 = 5 MHZ - ks note: setting this higher consitently caused CRC check failures on the RX side
  hspi6.Init.FirstBit = SPI_FIRSTBIT_MSB;
  hspi6.Init.TIMode = SPI_TIMODE_DISABLE;
  hspi6.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
  hspi6.Init.CRCPolynomial = 0x0;
  hspi6.Init.NSSPMode = SPI_NSS_PULSE_DISABLE;
  hspi6.Init.NSSPolarity = SPI_NSS_POLARITY_LOW;
  hspi6.Init.FifoThreshold = SPI_FIFO_THRESHOLD_01DATA;
  hspi6.Init.TxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi6.Init.RxCRCInitializationPattern = SPI_CRC_INITIALIZATION_ALL_ZERO_PATTERN;
  hspi6.Init.MasterSSIdleness = SPI_MASTER_SS_IDLENESS_00CYCLE;
  hspi6.Init.MasterInterDataIdleness = SPI_MASTER_INTERDATA_IDLENESS_00CYCLE;
  hspi6.Init.MasterReceiverAutoSusp = SPI_MASTER_RX_AUTOSUSP_DISABLE;
  hspi6.Init.MasterKeepIOState = SPI_MASTER_KEEP_IO_STATE_ENABLE;
  hspi6.Init.IOSwap = SPI_IO_SWAP_DISABLE;
  if (HAL_SPI_Init(&hspi6) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN SPI6_Init 2 */

  /* USER CODE END SPI6_Init 2 */

}

void HAL_SPI_MspInit(SPI_HandleTypeDef* spiHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(spiHandle->Instance==SPI1)
  {
  /* USER CODE BEGIN SPI1_MspInit 0 */

  /* USER CODE END SPI1_MspInit 0 */
    /* SPI1 clock enable */
    __HAL_RCC_SPI1_CLK_ENABLE();

    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOG_CLK_ENABLE();
    /**SPI1 GPIO Configuration
    PD7     ------> SPI1_MOSI
    PG10     ------> SPI1_NSS
    PG11     ------> SPI1_SCK
    */
    GPIO_InitStruct.Pin = GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_11;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

    /* SPI1 interrupt Init */
    HAL_NVIC_SetPriority(SPI1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(SPI1_IRQn);
  /* USER CODE BEGIN SPI1_MspInit 1 */

  /* USER CODE END SPI1_MspInit 1 */
  }
  else if(spiHandle->Instance==SPI2)
  {
  /* USER CODE BEGIN SPI2_MspInit 0 */

  /* USER CODE END SPI2_MspInit 0 */
    /* SPI2 clock enable */
    __HAL_RCC_SPI2_CLK_ENABLE();

    __HAL_RCC_GPIOB_CLK_ENABLE();
    /**SPI2 GPIO Configuration
    PB12     ------> SPI2_NSS
    PB13     ------> SPI2_SCK
    PB15     ------> SPI2_MOSI
    */
    GPIO_InitStruct.Pin = GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_15;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI2;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* SPI2 interrupt Init */
    HAL_NVIC_SetPriority(SPI2_IRQn, 1, 0);
    HAL_NVIC_EnableIRQ(SPI2_IRQn);
  /* USER CODE BEGIN SPI2_MspInit 1 */

  /* USER CODE END SPI2_MspInit 1 */
  }
  else if(spiHandle->Instance==SPI4)
  {
  /* USER CODE BEGIN SPI4_MspInit 0 */

  /* USER CODE END SPI4_MspInit 0 */
    /* SPI4 clock enable */
    __HAL_RCC_SPI4_CLK_ENABLE();

    __HAL_RCC_GPIOE_CLK_ENABLE();
    /**SPI4 GPIO Configuration
    PE11     ------> SPI4_NSS
    PE12     ------> SPI4_SCK
    PE13     ------> SPI4_MISO
    PE14     ------> SPI4_MOSI
    */
    GPIO_InitStruct.Pin = GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI4;
    HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

  /* USER CODE BEGIN SPI4_MspInit 1 */

  /* USER CODE END SPI4_MspInit 1 */
  }
  else if(spiHandle->Instance==SPI5)
  {
  /* USER CODE BEGIN SPI5_MspInit 0 */

  /* USER CODE END SPI5_MspInit 0 */
    /* SPI5 clock enable */
    __HAL_RCC_SPI5_CLK_ENABLE();

    __HAL_RCC_GPIOJ_CLK_ENABLE();
    __HAL_RCC_GPIOK_CLK_ENABLE();
    /**SPI5 GPIO Configuration
    PJ10     ------> SPI5_MOSI
    PK0     ------> SPI5_SCK
    PK1     ------> SPI5_NSS
    */
    GPIO_InitStruct.Pin = GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI5;
    HAL_GPIO_Init(GPIOJ, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI5;
    HAL_GPIO_Init(GPIOK, &GPIO_InitStruct);

    /* SPI5 interrupt Init */
    HAL_NVIC_SetPriority(SPI5_IRQn, 3, 0);
    HAL_NVIC_EnableIRQ(SPI5_IRQn);
  /* USER CODE BEGIN SPI5_MspInit 1 */

  /* USER CODE END SPI5_MspInit 1 */
  }
  else if(spiHandle->Instance==SPI6)
  {
  /* USER CODE BEGIN SPI6_MspInit 0 */

  /* USER CODE END SPI6_MspInit 0 */
    /* SPI6 clock enable */
    __HAL_RCC_SPI6_CLK_ENABLE();

    __HAL_RCC_GPIOG_CLK_ENABLE();
    /**SPI6 GPIO Configuration
    PG8     ------> SPI6_NSS
    PG13     ------> SPI6_SCK
    PG14     ------> SPI6_MOSI
    */
    GPIO_InitStruct.Pin = GPIO_PIN_8|GPIO_PIN_13|GPIO_PIN_14;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF5_SPI6;
    HAL_GPIO_Init(GPIOG, &GPIO_InitStruct);

    /* SPI6 interrupt Init */
    HAL_NVIC_SetPriority(SPI6_IRQn, 2, 0);
    HAL_NVIC_EnableIRQ(SPI6_IRQn);
  /* USER CODE BEGIN SPI6_MspInit 1 */

  /* USER CODE END SPI6_MspInit 1 */
  }
}

void HAL_SPI_MspDeInit(SPI_HandleTypeDef* spiHandle)
{

  if(spiHandle->Instance==SPI1)
  {
  /* USER CODE BEGIN SPI1_MspDeInit 0 */

  /* USER CODE END SPI1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI1_CLK_DISABLE();

    /**SPI1 GPIO Configuration
    PD7     ------> SPI1_MOSI
    PG10     ------> SPI1_NSS
    PG11     ------> SPI1_SCK
    */
    HAL_GPIO_DeInit(GPIOD, GPIO_PIN_7);

    HAL_GPIO_DeInit(GPIOG, GPIO_PIN_10|GPIO_PIN_11);

    /* SPI1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(SPI1_IRQn);
  /* USER CODE BEGIN SPI1_MspDeInit 1 */

  /* USER CODE END SPI1_MspDeInit 1 */
  }
  else if(spiHandle->Instance==SPI2)
  {
  /* USER CODE BEGIN SPI2_MspDeInit 0 */

  /* USER CODE END SPI2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI2_CLK_DISABLE();

    /**SPI2 GPIO Configuration
    PB12     ------> SPI2_NSS
    PB13     ------> SPI2_SCK
    PB15     ------> SPI2_MOSI
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_15);

    /* SPI2 interrupt Deinit */
    HAL_NVIC_DisableIRQ(SPI2_IRQn);
  /* USER CODE BEGIN SPI2_MspDeInit 1 */

  /* USER CODE END SPI2_MspDeInit 1 */
  }
  else if(spiHandle->Instance==SPI4)
  {
  /* USER CODE BEGIN SPI4_MspDeInit 0 */

  /* USER CODE END SPI4_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI4_CLK_DISABLE();

    /**SPI4 GPIO Configuration
    PE11     ------> SPI4_NSS
    PE12     ------> SPI4_SCK
    PE13     ------> SPI4_MISO
    PE14     ------> SPI4_MOSI
    */
    HAL_GPIO_DeInit(GPIOE, GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14);

  /* USER CODE BEGIN SPI4_MspDeInit 1 */

  /* USER CODE END SPI4_MspDeInit 1 */
  }
  else if(spiHandle->Instance==SPI5)
  {
  /* USER CODE BEGIN SPI5_MspDeInit 0 */

  /* USER CODE END SPI5_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI5_CLK_DISABLE();

    /**SPI5 GPIO Configuration
    PJ10     ------> SPI5_MOSI
    PK0     ------> SPI5_SCK
    PK1     ------> SPI5_NSS
    */
    HAL_GPIO_DeInit(GPIOJ, GPIO_PIN_10);

    HAL_GPIO_DeInit(GPIOK, GPIO_PIN_0|GPIO_PIN_1);

    /* SPI5 interrupt Deinit */
    HAL_NVIC_DisableIRQ(SPI5_IRQn);
  /* USER CODE BEGIN SPI5_MspDeInit 1 */

  /* USER CODE END SPI5_MspDeInit 1 */
  }
  else if(spiHandle->Instance==SPI6)
  {
  /* USER CODE BEGIN SPI6_MspDeInit 0 */

  /* USER CODE END SPI6_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_SPI6_CLK_DISABLE();

    /**SPI6 GPIO Configuration
    PG8     ------> SPI6_NSS
    PG13     ------> SPI6_SCK
    PG14     ------> SPI6_MOSI
    */
    HAL_GPIO_DeInit(GPIOG, GPIO_PIN_8|GPIO_PIN_13|GPIO_PIN_14);

    /* SPI6 interrupt Deinit */
    HAL_NVIC_DisableIRQ(SPI6_IRQn);
  /* USER CODE BEGIN SPI6_MspDeInit 1 */

  /* USER CODE END SPI6_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */
/* @fn void SPI1_IRQHandler(void)
 * @brief SPI1 global interrupt handling function
 * @param None
 * @return None
 */
void SPI1_IRQHandler(void)
{
  /* USER CODE BEGIN SPI1_IRQn 0 */

  /* USER CODE END SPI1_IRQn 0 */
  HAL_SPI_IRQHandler(&hspi1);
  /* USER CODE BEGIN SPI1_IRQn 1 */

  /* USER CODE END SPI1_IRQn 1 */
}
/* @fn void SPI2_IRQHandler(void)
 * @brief SPI2 global interrupt handling function
 * @param None
 * @return None
 */
void SPI2_IRQHandler(void)
{
   // Adapted from HAL_SPI_IRQHandler for 16-Bit constant Receive only mode
   uint32_t itsource = hspi2.Instance->IER;
   uint32_t itflag   = hspi2.Instance->SR;
   uint32_t trigger  = itsource & itflag;
   if( HAL_IS_BIT_CLR(trigger, SPI_FLAG_OVR) && HAL_IS_BIT_SET(trigger, SPI_FLAG_RXP) )
   {
      // Adapted from SPI_RxISR_16BIT
      uint16_t uwValue = *((__IO uint16_t *)&(hspi2.Instance->RXDR));
      if( !RingBuffer_Insert((RINGBUFF_T *)&stSPI_AB_rxring, &uwValue) )
      {
         auwStatusCounter_SPI_AB_RX[SPI_COUNTER__RX_OVERFLOW]++;
      }
      return;
   }
   /* Check for EOT or Error - Skipped for inifinite RX transfer size */
//   else if( HAL_IS_BIT_SET(trigger, SPI_FLAG_EOT) )
   /* Check for Error */
   else if (HAL_IS_BIT_SET(itflag, SPI_FLAG_SUSP) && HAL_IS_BIT_SET(itsource, SPI_FLAG_EOT))
   {
      /* Abort on going, clear SUSP flag to avoid infinite looping */
      __HAL_SPI_CLEAR_SUSPFLAG(&hspi2);
      return;
   }
   /* SPI in Error Treatment --------------------------------------------------*/
   if ((trigger & (SPI_FLAG_MODF | SPI_FLAG_OVR | SPI_FLAG_FRE)) != 0UL)
   {
      /* SPI Overrun error interrupt occurred ----------------------------------*/
      if ((trigger & SPI_FLAG_OVR) != 0UL) auwStatusCounter_SPI_AB_RX[SPI_COUNTER__RX_OVERFLOW]++;
      /* SPI Mode Fault error interrupt occurred -------------------------------*/
      else if ((trigger & SPI_FLAG_MODF) != 0UL) auwStatusCounter_SPI_AB_RX[SPI_COUNTER__MODE]++;
      /* SPI Frame error interrupt occurred ------------------------------------*/
      else if ((trigger & SPI_FLAG_FRE) != 0UL) auwStatusCounter_SPI_AB_RX[SPI_COUNTER__FRAME]++;
      else auwStatusCounter_SPI_AB_RX[SPI_COUNTER__UNKNOWN]++;
      __HAL_SPI_CLEAR_OVRFLAG(&hspi2);
      __HAL_SPI_CLEAR_MODFFLAG(&hspi2);
      __HAL_SPI_CLEAR_FREFLAG(&hspi2);
      __HAL_SPI_CLEAR_UDRFLAG(&hspi2);
  }
}
/* @fn void SPI5_IRQHandler(void)
 * @brief SPI5 global interrupt handling function
 * @param None
 * @return None
 */
void SPI5_IRQHandler(void)
{
   // Adapted from HAL_SPI_IRQHandler for 16-Bit constant Receive only mode
   uint32_t itsource = hspi5.Instance->IER;
   uint32_t itflag   = hspi5.Instance->SR;
   uint32_t trigger  = itsource & itflag;
   if( HAL_IS_BIT_CLR(trigger, SPI_FLAG_OVR) && HAL_IS_BIT_SET(trigger, SPI_FLAG_RXP) )
   {
      // Adapted from SPI_RxISR_16BIT
      uint16_t uwValue = *((__IO uint16_t *)&(hspi5.Instance->RXDR));
      if( !RingBuffer_Insert((RINGBUFF_T *)&stSPI_AU_rxring, &uwValue) )
      {
         auwStatusCounter_SPI_AU_RX[SPI_COUNTER__RX_OVERFLOW]++;
      }
      return;
   }
   /* Check for EOT or Error - Skipped for inifinite RX transfer size */
//   else if( HAL_IS_BIT_SET(trigger, SPI_FLAG_EOT) )
   /* Check for Error */
   else if (HAL_IS_BIT_SET(itflag, SPI_FLAG_SUSP) && HAL_IS_BIT_SET(itsource, SPI_FLAG_EOT))
   {
      /* Abort on going, clear SUSP flag to avoid infinite looping */
      __HAL_SPI_CLEAR_SUSPFLAG(&hspi5);
      return;
   }
   /* SPI in Error Treatment --------------------------------------------------*/
   if ((trigger & (SPI_FLAG_MODF | SPI_FLAG_OVR | SPI_FLAG_FRE)) != 0UL)
   {
      /* SPI Overrun error interrupt occurred ----------------------------------*/
      if ((trigger & SPI_FLAG_OVR) != 0UL) auwStatusCounter_SPI_AU_RX[SPI_COUNTER__RX_OVERFLOW]++;
      /* SPI Mode Fault error interrupt occurred -------------------------------*/
      else if ((trigger & SPI_FLAG_MODF) != 0UL) auwStatusCounter_SPI_AU_RX[SPI_COUNTER__MODE]++;
      /* SPI Frame error interrupt occurred ------------------------------------*/
      else if ((trigger & SPI_FLAG_FRE) != 0UL) auwStatusCounter_SPI_AU_RX[SPI_COUNTER__FRAME]++;
      else auwStatusCounter_SPI_AU_RX[SPI_COUNTER__UNKNOWN]++;
      __HAL_SPI_CLEAR_OVRFLAG(&hspi5);
      __HAL_SPI_CLEAR_MODFFLAG(&hspi5);
      __HAL_SPI_CLEAR_FREFLAG(&hspi5);
      __HAL_SPI_CLEAR_UDRFLAG(&hspi5);
  }
}
/* @fn void SPI6_IRQHandler(void)
 * @brief SPI6 global interrupt handling function
 * @param None
 * @return None
 */
void SPI6_IRQHandler(void)
{
  /* USER CODE BEGIN SPI6_IRQn 0 */

  /* USER CODE END SPI6_IRQn 0 */
  HAL_SPI_IRQHandler(&hspi6);
  /* USER CODE BEGIN SPI6_IRQn 1 */

  /* USER CODE END SPI6_IRQn 1 */
}
void HAL_SPI_TxCpltCallback(SPI_HandleTypeDef *hspi)
{
   if( hspi->Instance == hspi1.Instance ) // TX Only
   {
      /* If there are additional elements in the transmit buffer, load them. */
      if( bLoadingTxBuffer == INACTIVE )
      {
         int iElementsInBuffer = RingBuffer_GetCount((RINGBUFF_T *)&stSPI_AB_txring);
         if( iElementsInBuffer )
         {
            iElementsInBuffer = ( iElementsInBuffer > SPI_MAX_PACKET_SIZE__WORD ) ? SPI_MAX_PACKET_SIZE__WORD : iElementsInBuffer;
            RingBuffer_PopMult((RINGBUFF_T *)&stSPI_AB_txring, (uint16_t *)auwSPI_TempTxbuff, iElementsInBuffer);
            HAL_SPI_Transmit_IT(hspi, (uint8_t *)auwSPI_TempTxbuff, iElementsInBuffer);
         }
      }
   }
   else if(hspi->Instance == hspi6.Instance)
   {
	   /* If there are additional elements in the transmit buffer, load them. */
	   if( bLoadingTxBufferUI == INACTIVE )
	   {
	      int iElementsInBuffer = RingBuffer_GetCount((RINGBUFF_T *)&stSPI_AU_txring);
	      if( iElementsInBuffer )
	      {
	         iElementsInBuffer = ( iElementsInBuffer > SPI_MAX_PACKET_SIZE__WORD ) ? SPI_MAX_PACKET_SIZE__WORD : iElementsInBuffer;
	         RingBuffer_PopMult((RINGBUFF_T *)&stSPI_AU_txring, (uint16_t *)auwSPI_TempTxbuff, iElementsInBuffer);
	         HAL_SPI_Transmit_IT(hspi, (uint8_t *)auwSPI_TempTxbuff, iElementsInBuffer);
	      }
	   }
   }
}
void HAL_SPI_RxCpltCallback(SPI_HandleTypeDef *hspi)
{
   // Converted to custom
}
void HAL_SPI_ErrorCallback(SPI_HandleTypeDef *hspi)
{
   if( hspi->Instance == hspi1.Instance ) // TX Only
   {
      if( hspi->ErrorCode & HAL_SPI_ERROR_UDR ) auwStatusCounter_SPI_AB_TX[SPI_COUNTER__UNDERRUN]++;
      else if( hspi->ErrorCode & HAL_SPI_ERROR_MODF ) auwStatusCounter_SPI_AB_TX[SPI_COUNTER__MODE]++;
      else if( hspi->ErrorCode & HAL_SPI_ERROR_FRE ) auwStatusCounter_SPI_AB_TX[SPI_COUNTER__FRAME]++;
      else if( hspi->ErrorCode & HAL_SPI_ERROR_OVR ) auwStatusCounter_SPI_AB_TX[SPI_COUNTER__RX_OVERFLOW]++;
      else if( hspi->ErrorCode != HAL_SPI_ERROR_NONE ) auwStatusCounter_SPI_AB_TX[SPI_COUNTER__UNKNOWN]++;
      hspi->ErrorCode = HAL_SPI_ERROR_NONE;

      hspi->State = HAL_SPI_STATE_READY;
      /* If there are additional elements in the transmit buffer, load them. */
      if( bLoadingTxBuffer == INACTIVE )
      {
         int iElementsInBuffer = RingBuffer_GetCount((RINGBUFF_T *)&stSPI_AB_txring);
         if( iElementsInBuffer )
         {
            iElementsInBuffer = ( iElementsInBuffer > SPI_MAX_PACKET_SIZE__WORD ) ? SPI_MAX_PACKET_SIZE__WORD : iElementsInBuffer;
            RingBuffer_PopMult((RINGBUFF_T *)&stSPI_AB_txring, (uint16_t *)auwSPI_TempTxbuff, iElementsInBuffer);
            HAL_SPI_Transmit_IT(hspi, (uint8_t *)auwSPI_TempTxbuff, iElementsInBuffer);
         }
      }
   }
   else if(hspi->Instance == hspi6.Instance)
   {
	   if( hspi->ErrorCode & HAL_SPI_ERROR_UDR ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__UNDERRUN]++;
	   else if( hspi->ErrorCode & HAL_SPI_ERROR_MODF ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__MODE]++;
	   else if( hspi->ErrorCode & HAL_SPI_ERROR_FRE ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__FRAME]++;
	   else if( hspi->ErrorCode & HAL_SPI_ERROR_OVR ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__RX_OVERFLOW]++;
	   else if( hspi->ErrorCode != HAL_SPI_ERROR_NONE ) auwStatusCounter_SPI_AU_TX[SPI_COUNTER__UNKNOWN]++;
	   hspi->ErrorCode = HAL_SPI_ERROR_NONE;

	   hspi->State = HAL_SPI_STATE_READY;
	   /* If there are additional elements in the transmit buffer, load them. */
	   if( bLoadingTxBufferUI == INACTIVE )
	   {
	      int iElementsInBuffer = RingBuffer_GetCount((RINGBUFF_T *)&stSPI_AU_txring);
	      if( iElementsInBuffer )
	      {
	         iElementsInBuffer = ( iElementsInBuffer > SPI_MAX_PACKET_SIZE__WORD ) ? SPI_MAX_PACKET_SIZE__WORD : iElementsInBuffer;
	         RingBuffer_PopMult((RINGBUFF_T *)&stSPI_AU_txring, (uint16_t *)auwSPI_TempTxbuff, iElementsInBuffer);
	         HAL_SPI_Transmit_IT(hspi, (uint8_t *)auwSPI_TempTxbuff, iElementsInBuffer);
	      }
	   }
   }
}
/* @fn uint16_t SPI_TX_GetCounter(en_spi_counter eCounter)
 * @brief Returns requested status counter
 * @param None
 * @return None
 */
uint16_t SPI_TX_GetCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	if(eDest == SPI_AB)
	{
		return auwStatusCounter_SPI_AB_TX[eCounter];
	}
	return auwStatusCounter_SPI_AU_TX[eCounter];
}
void SPI_TX_IncrementCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	if(eDest == SPI_AB)
	{
		auwStatusCounter_SPI_AB_TX[eCounter]++;
	}
	else
	{
		auwStatusCounter_SPI_AU_TX[eCounter]++;
	}
}
/* @fn uint16_t SPI_RX_GetCounter(en_spi_counter eCounter)
 * @brief Returns requested status counter
 * @param None
 * @return None
 */
uint16_t SPI_RX_GetCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	if(eDest == SPI_AB)
	{
		return auwStatusCounter_SPI_AB_RX[eCounter];
	}
   return auwStatusCounter_SPI_AU_RX[eCounter];
}
void SPI_RX_IncrementCounter(en_spi_destination eDest, en_spi_counter eCounter)
{
	if(eDest == SPI_AB)
	{
		auwStatusCounter_SPI_AB_RX[eCounter]++;
	}
	else
	{
		auwStatusCounter_SPI_AU_RX[eCounter]++;
	}
}
/* @fn en_pass_fail SPI_AB_LoadToTxRB(uint16_t *puwData, uint16_t uwNumBytes)
 * @brief Initializes the SPI_AB peripheral for A to B MCU communication.
 */
en_pass_fail SPI_AB_LoadToTxRB(uint16_t *puwData, uint16_t uwNumItems)
{
   bLoadingTxBuffer = ACTIVE;
   if( hspi1.State == HAL_SPI_STATE_READY )
   {
      memcpy((uint16_t *)auwSPI_TempTxbuff, puwData, uwNumItems*SPI_WORD_SIZE__BYTE);
      if( HAL_SPI_Transmit_IT(&hspi1, (uint8_t *)auwSPI_TempTxbuff, uwNumItems) == HAL_OK )
      {
         bLoadingTxBuffer = INACTIVE;
         auwStatusCounter_SPI_AB_TX[SPI_COUNTER__TX]++;
         return PASS;
      }
   }
   if( RingBuffer_InsertMult((RINGBUFF_T *)&stSPI_AB_txring, puwData, uwNumItems) )
   {
      bLoadingTxBuffer = INACTIVE;
      auwStatusCounter_SPI_AB_TX[SPI_COUNTER__TX]++;
      return PASS;
   }
   else
   {
      bLoadingTxBuffer = INACTIVE;
      auwStatusCounter_SPI_AB_TX[SPI_COUNTER__TX_OVERFLOW]++;
      return FAIL;
   }
}
/* @fn en_pass_fail SPI_AB_UnloadFromRxRB(uint16_t *puwData)
 * @brief Pops the oldest item from the RX ring buffer if available and returns a 0. Returns 1 otherwise
 */
en_pass_fail SPI_AB_UnloadFromRxRB(uint16_t *puwData)
{
   if( RingBuffer_Pop((RINGBUFF_T *)&stSPI_AB_rxring, puwData) ) return PASS;
   else return FAIL;
}
/* @fn en_pass_fail SPI_AU_LoadToTxRB(uint16_t *puwData, uint16_t uwNumBytes)
 * @brief Initializes the SPI peripheral for A to UI MCU communication.
 */
en_pass_fail SPI_AU_LoadToTxRB(uint16_t *puwData, uint16_t uwNumItems)
{
   bLoadingTxBufferUI = ACTIVE;
   if( hspi6.State == HAL_SPI_STATE_READY )
   {
      memcpy((uint16_t *)auwSPI_TempTxbuff, puwData, uwNumItems*SPI_WORD_SIZE__BYTE);
      if( HAL_SPI_Transmit_IT(&hspi6, (uint8_t *)auwSPI_TempTxbuff, uwNumItems) == HAL_OK )
      {
         bLoadingTxBufferUI = INACTIVE;
         auwStatusCounter_SPI_AU_TX[SPI_COUNTER__TX]++;
         return PASS;
      }
   }
   if( RingBuffer_InsertMult((RINGBUFF_T *)&stSPI_AU_txring, puwData, uwNumItems) )
   {
      bLoadingTxBufferUI = INACTIVE;
      auwStatusCounter_SPI_AU_TX[SPI_COUNTER__TX]++;
      return PASS;
   }
   else
   {
      bLoadingTxBufferUI = INACTIVE;
      auwStatusCounter_SPI_AU_TX[SPI_COUNTER__TX_OVERFLOW]++;
      return FAIL;
   }
}
/* @fn en_pass_fail SPI_AU_UnloadFromRxRB(uint16_t *puwData)
 * @brief Pops the oldest item from the RX ring buffer if available and returns a 0. Returns 1 otherwise
 */
en_pass_fail SPI_AU_UnloadFromRxRB(uint16_t *puwData)
{
   if( RingBuffer_Pop((RINGBUFF_T *)&stSPI_AU_rxring, puwData) ) return PASS;
   else return FAIL;
}
/* USER CODE END 1 */
