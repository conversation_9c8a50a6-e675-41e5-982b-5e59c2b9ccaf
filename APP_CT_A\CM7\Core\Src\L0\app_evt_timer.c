#include "evt_timer.h"
#include "stm32h7xx.h"

extern evt_timer_t *head;

void TimerEvt_TimerInit(void)
{
  // Enable timer register peripheral clock
  RCC->APB1LENR |= RCC_APB1LENR_TIM12EN;

  // Disable timer
  TIM12->CR1 &= ~TIM_CR1_CEN;

  // Set pre-scaler and reload register
  TIM12->PSC = 239; // APB1 Clock = 240MHz
  TIM12->ARR = 999;

  // Re-initialize counter and update registers
  TIM12->DIER |= TIM_DIER_UIE;
  TIM12->EGR  |= TIM_EGR_UG;

  // Enable timer
  TIM12->CR1 |= TIM_CR1_CEN;

  NVIC_SetPriority(TIM8_BRK_TIM12_IRQn, 7);
  NVIC_EnableIRQ(TIM8_BRK_TIM12_IRQn);

}

void TIM8_BRK_TIM12_IRQHandler(void)
{
  BaseType_t xHigherPriorityTaskWoken = pdFALSE;

  if((TIM12->SR & TIM_SR_UIF) == TIM_SR_UIF)
  {
    evt_timer_t *next = head;
    while(next != NULL)
    {
      if(next->timeout > 0)
      {
        if(--next->timeout == 0)
        {
          xQueueSendFromISR(next->queue,
              &next->event,
              &xHigherPriorityTaskWoken);
          next->timeout = next->interval;
        }
      }

      next = next->next;
    }

    TIM12->SR &= ~TIM_SR_UIF;
  }

  portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}




