/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_position.c
* @version 		  : 1.0.0
* @brief This subroutine updates the system position
* @details This subroutine updates the system position
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_position.h"
#include "sr_CAN2.h"
#include "operation_def.h"
#include "param_def.h"
#include "position_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                (2)

#define MINIMUM_POSITION_DIFFERENCE__05MM          (0)//(ONE_INCH__05MM)
#define DEFAULT_FLOOR_STARTING_POSITION_05MM       (0x0BB93A) // TODO for traction this will need to change for learning top to bottom

#define MAX_LES02_ERRORCODES						(24)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef struct
{
	en_car_fault eLandingSysFault;
	en_car_alarm eLandingSysAlarm;
} st_LandingSystemErrors;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static en_active_inactive bSelectorReset;
static st_LandingSystemErrors stLandingSystemErrors;
static en_car_alarm LastAlarm;//TODO remove
static uint8_t ucDebounceCounterAlarm;//TODO remove
/******************************************************************************
* Function Definitions
*******************************************************************************/
void Position_SetSelectorReset(en_active_inactive bReset)
{
	bSelectorReset = bReset;
}
en_active_inactive Position_GetSelectorReset(void)
{
	return bSelectorReset;
}
en_car_fault Position_GetLandingSystemFault(void)
{
	return stLandingSystemErrors.eLandingSysFault;
}
en_car_alarm Position_GetLandingSystemAlarm(void)
{
	return stLandingSystemErrors.eLandingSysAlarm;
}
/* @brief Forces position to a default starting value at the start of a hoistway learn */
void Position_DefaultToStartingLearnPosition(void)
{
   uint32_t uiDefaultPos_05mm = DEFAULT_FLOOR_STARTING_POSITION_05MM;
   Position_SetPosition_05mm(uiDefaultPos_05mm);
}

/* @fn static void UpdateSpeedAndPosition(void)
 * @brief Reads the hardware peripheral and update the global position structure */
static void UpdateSpeedAndPosition(void)
{
   uint32_t uiPosition_ErrorCode;
   uint32_t uiPosition_Raw_05mm;
   int16_t wSpeed_Raw_fpm;
   en_car_fault eFault = CFLT__NONE;
   en_car_alarm eAlarm = CALM__NONE;

   uint8_t ucPositionType = Param_ReadValue_8Bit(CPARAM8__PositionSystemType);
   if( ucPositionType == POSITION_SYSTEM__LES02 )
   {
	   uiPosition_ErrorCode = getErrorRaw() & 0x00FFFFFF;
	   uiPosition_Raw_05mm = getPositionRaw() & MAX_POSITION_VALUE_LES02__05MM;
	   wSpeed_Raw_fpm =  getSpeedRaw();

	   if( ( uiPosition_ErrorCode & 0x0000FFF8 ) == 0 )
	   {
		   Position_SetPosition_05mm( uiPosition_Raw_05mm );
		   Position_SetSpeed_fpm((wSpeed_Raw_fpm * 60.0f/ ONE_FOOT__1MM)+0.5f);
	   }
	   for(uint8_t ucbit = 0; ucbit < MAX_LES02_ERRORCODES; ucbit++)
	   {
		   if( GET_BIT_BY_INDEX(uiPosition_ErrorCode, ucbit) )
		   {
			   if( ucbit < 3)
			   {
				   eAlarm = CALM__LES02_ERROR_00+ucbit;
			   }
			   else
			   {
				   eFault = CFLT__LES02_ERROR_03+(ucbit-3);
				   break;
			   }
		   }
	   }
   }
   else
   {
	   //AS todo add other landing systems
   }
   stLandingSystemErrors.eLandingSysFault = eFault;
   //stLandingSystemErrors.eLandingSysAlarm = eAlarm;  TODO uncomment

   if( LastAlarm == eAlarm )//TODO remove test
   {
	   ucDebounceCounterAlarm++;
	   if( ucDebounceCounterAlarm > Param_ReadValue_8Bit(CPARAM8__TEST_ONLY__SELECTOR_WARNINGS_DEBOUNCE) )
	   {
		   ucDebounceCounterAlarm = 0;
		   stLandingSystemErrors.eLandingSysAlarm = eAlarm;
	   }
   }
   else
   {
	   ucDebounceCounterAlarm = 0;
	   stLandingSystemErrors.eLandingSysAlarm = CALM__NONE;
   }

   LastAlarm = eAlarm;
}

/* @fn void vOperation_Task(void *pvParameters)
 * @brief Execute the vParameters_Task to update hoistway position
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vPosition_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		if( Param_GetState() != PARAM_STATE__STARTUP )
		{
			en_manual_operations eManualOp = Operation_GetManualOperation();
			UpdateSpeedAndPosition();
			switch( Position_GetState() )
			{
			case POSITION_STATE__INITIALIZING: /* 0, Waiting for valid readings after a power up */
			case POSITION_STATE__LOST: /* 1, Communication Lost*/
				if( CAN2_CheckIfCommLoss() )
				{
					if( !( Operation_GetOverrideTerminalLimits()
							&& ( eManualOp != MANUAL_OP__OFF ) ) )
					{
						Faults_SetFault(CFLT__CT_OFFLINE_CHANNEL_A);
					}
				}
				else if( stLandingSystemErrors.eLandingSysFault != CFLT__NONE )
				{
					if( !( Operation_GetOverrideTerminalLimits()
							&& ( eManualOp != MANUAL_OP__OFF ) ) )
					{
						Faults_SetFault(stLandingSystemErrors.eLandingSysFault);
					}
				}
				else
				{
					Position_SetState(POSITION_STATE__KNOWN);
				}
				break;
			case POSITION_STATE__KNOWN: /* 2, Hoistway position now known*/
				if( CAN2_CheckIfCommLoss() )
				{
					if( !( Operation_GetOverrideTerminalLimits()
							&& ( eManualOp != MANUAL_OP__OFF ) ) )
					{
						Faults_SetFault(CFLT__CT_OFFLINE_CHANNEL_A);
					}
					Position_SetState(POSITION_STATE__LOST);
				}
				else if( stLandingSystemErrors.eLandingSysFault != CFLT__NONE )
				{
					if( !( Operation_GetOverrideTerminalLimits()
							&& ( eManualOp != MANUAL_OP__OFF ) ) )
					{
						Faults_SetFault(stLandingSystemErrors.eLandingSysFault);
					}
					Position_SetState(POSITION_STATE__LOST);
				}
				break;
			default: Position_SetState(POSITION_STATE__INITIALIZING); break;
			}
			if( stLandingSystemErrors.eLandingSysAlarm != CALM__NONE )
			{
				Alarms_SetAlarm(stLandingSystemErrors.eLandingSysAlarm);
			}
		}

		vTaskDelay(POSITION_TASK_DELAY);
	}
}
