/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_Fault.h
* @version 		  : 1.0.0
* @brief Subroutine updating processor's fault status
* @details Subroutine updating processor's fault status
*****************************************************************************/
#ifndef _SR_FAULT_H_
#define _SR_FAULT_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "main.h"
#include "expansion.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_exp_error Error_GetActiveError(void);
void Error_SetActiveError(en_exp_error eError);
en_ris_error Error_GetActiveErrorRiser(void);
void Error_SetActiveErrorRiser(en_ris_error eError);

void FaultAlarm_Init(void);
void vFaultAlarm_Task(void *pvParams);

#endif /* _SR_FAULT_H_ */
