/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_outputs.c
* @version 		  : 1.0.0
* @brief This subroutine updates local board outputs
* @details This subroutine updates local board outputs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_local_outputs.h"
#include "dg_car_mr_a7.h"
#include "app_gpio.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (1000)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (5)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

static uint32_t auiOutputBitmap[BITMAP32_ARRAY_SIZE(NUM_LOCAL_OUTPUTS)];
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn static void UpdateOutputPins(void)
 * @brief Takes the local output commands and updates the corresponding pin states
 * @param None
 * @return None
 */
static void UpdateOutputPins(void)
{
   /* Extract programmable terminal output commands and update */
   uint32_t uiCommandBitmap = GetLocalOutputs_CT_BOARD_MRA();

   /* Update hardware pin states based on commands */
   for(en_local_outputs eOutput = enLOCAL_OUT__OUTPUT_01; eOutput <= enLOCAL_OUT__OUTPUT_12; eOutput++)
   {
      uint8_t ucBitIndex = eOutput - enLOCAL_OUT__OUTPUT_01;
      uint8_t bActive = GET_BIT_BY_INDEX(uiCommandBitmap, ucBitIndex);
      System_SetBitByIndex(&auiOutputBitmap[0], ucBitIndex, bActive);

      GPIO_WriteOutput(eOutput, bActive);
   }
}

uint32_t LocalOutputs_GetOutputBitmap(uint8_t ucArrayIndex)
{
	return auiOutputBitmap[ucArrayIndex];
}

/* @fn void vOperation_Task(void *pvParameters)
 * @brief Execute the vParameters_Task to update output pins
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vLocalOutput_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		 UpdateOutputPins();
		 vTaskDelay(LOCAL_OUTPUT_TASK_DELAY);
	}
}

