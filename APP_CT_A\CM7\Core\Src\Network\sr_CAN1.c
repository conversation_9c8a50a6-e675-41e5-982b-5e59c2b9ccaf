/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN1.c
 * @version 		  : 1.0.0
 * @brief Subroutine handling CAN1 network
 * @details Subroutine handling CAN1 network
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <string.h>
#include "app.h"
#include "sr_CAN1.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
#include "dg_car_mr_a7.h"
#include "dg_car_mr_b4.h"
#include "dg_car_ct_a7.h"
#include "dg_car_cop_a4.h"
#include "dg_car_ui_a7.h"
#include "app_can.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
// How long the bus can go without receiving a message before an offline flag is set
#define CAN_OFFLINE_TIMEOUT_1MS                    (4000)

/* How long this node can go without receiving a message from the MR board before an offline flag is set */
#define MR_OFFLINE_TIMEOUT_1MS                     (4000)
/* How long this node can go without receiving a message from the COP board before an offline flag is set */
#define COP_OFFLINE_TIMEOUT_1MS                    (4000)
#define UI_MR_OFFLINE_TIMEOUT_1MS                  (61000)
#define UI_COP_OFFLINE_TIMEOUT_1MS                 (61000)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/

/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static uint16_t uwOfflineTimer_1ms = CAN_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_MR_1ms = MR_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_COP_1ms = COP_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_UI_MR_1ms = UI_MR_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_UI_COP_1ms = UI_COP_OFFLINE_TIMEOUT_1MS;
static uint8_t bLastOfflineFlag_MR;
static uint8_t bLastOfflineFlag_COP;
static uint8_t bLastOfflineFlag_UI_MR;
static uint8_t bLastOfflineFlag_UI_COP;
static const en_sys_network eLocalNetwork = SYS_NETWORK__CAR;
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Returns 0 if the packet was successfully loaded for transmit
 * @param pstMsg, pointer to CAN message to transmit
 * @return Returns 0 if the packet was successfully loaded for transmit
 */
en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg) {
   en_pass_fail eError = FAIL;
   if(pstMsg->bExtendedID == INACTIVE) {
      if(pstMsg->uiID & NETWORK_PACKET_ID_EXTENDED_BITMAP)
         pstMsg->bExtendedID = ACTIVE;
      else
         pstMsg->bExtendedID = INACTIVE;
   }
   pstMsg->uiID &= NETWORK_PACKET_ID_EXTENDED_MASK;
   if(CAN1_LoadToRB(pstMsg) == PASS) {
      eError = PASS;
   }
   return eError;
}

/* @fn static void UnloadDatagrams(void)
 * @brief Unloads messages from the CAN ring buffer
 * @param None
 * @return None
 */
static void UnloadDatagrams(void) {
   st_datagram_control *pstControl;
   st_datagram *pstDatagram;
   st_CAN_msg stRxMsg;
   en_sys_network eNetwork;
   en_sys_node eSource;
   uint16_t uwDatagramID;
   uint8_t ucNumBytes;
   for(uint8_t i = 0; i < CAN_RX_RING_BUFFER_SIZE; i++) {
      if(CAN1_UnloadFromRB(&stRxMsg) == PASS) {
         /* Packet encoding exception for high priority position packets */
         if((stRxMsg.uiID & NETWORK_PACKET_ID_EXTENDED_MASK)
            == ( UNIQUE_POSITION_EXTENDED_CAN_ID & NETWORK_PACKET_ID_EXTENDED_MASK)) {
            pstControl = DG_CAR_CT_A7_GetDatagramControlStruct();
            uwDatagramID = DG_CAR_CT_A7__Position;
            pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
            stRxMsg.uiID = Network_GetPacketID(pstDatagram->ucSize_Bytes,
                                               uwDatagramID,
                                               pstControl->ucNetwork,
                                               pstControl->ucSourceNode);
         }
         eNetwork = Network_GetNetworkID(stRxMsg.uiID);
         if(eNetwork == eLocalNetwork) {
            eSource = Network_GetSourceID(stRxMsg.uiID);
            pstControl = Network_GetControlStructure_Car(eSource);
            if(pstControl != 0) {
               uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
               if(uwDatagramID < pstControl->uwNumberOfDG) {
                  pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                  ucNumBytes = pstDatagram->ucSize_Bytes;
                  /* 1. Copy the content of every datagram */
                  memcpy(pstDatagram->paucData, &stRxMsg.unData.auc8[0], ucNumBytes);
                  /* 2. Run the datagram's unload function */
                  if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID()))
                     pstDatagram->pfnUnload();

                  /* 3. Check if the packet should be forwarded */
                  if( ( Network_CheckDestination_Car(NET_FWD_PATH__AB, pstDatagram->uiDestinationBitmap) == PASS )
                   && ( Param_GetState() != PARAM_STATE__STARTUP ) ) /* Prevent forwarding from MCUB when initializing */
                  {
                      stRxMsg.ucDLC = ucNumBytes;
                      SPI_AB_TransmitDatagram(&stRxMsg);
                  }

                  if( ( Network_CheckDestination_Car(NET_FWD_PATH__UI, pstDatagram->uiDestinationBitmap) == PASS )
                   && ( Param_GetState() != PARAM_STATE__STARTUP) ) /* Prevent forwarding from MCUB when initializing */
                  {
                     stRxMsg.ucDLC = ucNumBytes;
                     SPI_AU_TransmitDatagram(&stRxMsg);
                  }

                  /* 4. Increment receive counter */
                  pstDatagram->ucPacketCounter++;

                  /* 5. Mark packet as received */
                  pstDatagram->bDataChanged = ACTIVE;

                  /* 6. Reset offline timers */
                  if( eSource == SYS_NODE__MR_A7 )
                  {
                      uwOfflineTimer_MR_1ms = 0;
                  }
                  else if( eSource == SYS_NODE__COP_A4 )
                  {
                      uwOfflineTimer_COP_1ms = 0;
                  }
                  else if( eSource == SYS_NODE__UI_MR_A7 )
                  {
                	  uwOfflineTimer_UI_MR_1ms = 0;
                  }
                  else if( eSource == SYS_NODE__UI_COP_A7 )
                  {
                	  uwOfflineTimer_UI_COP_1ms = 0;
                  }
               }
            }
         }
         uwOfflineTimer_1ms = 0;
      } else {
         break;
      }
   }
}
/* @fn uint8_t CAN2_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t CAN1_CheckIfCommLoss(void)
{
	return (uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS);
}
/* @fn static void CAN2_UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void)
{
	uint8_t bOfflineFlag_MR;
	uint8_t bOfflineFlag_COP;
	uint8_t bOfflineFlag_UI_MR;
	uint8_t bOfflineFlag_UI_COP;
	en_active_inactive bBypassCommLoss = ( Operation_GetManualOperation() == MANUAL_OP__CONSTRUCTION )
									  || ( Operation_GetManualOperation() == MANUAL_OP__CAR_SAFETY_BYPASS );
    if( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS )
    {

    }
    else
    {
        uwOfflineTimer_1ms += CAN1_TASK_DELAY;
    }
    /* MR */
    if( uwOfflineTimer_MR_1ms >= MR_OFFLINE_TIMEOUT_1MS )
    {
 	    bOfflineFlag_MR = ACTIVE;
 	    if( bBypassCommLoss == INACTIVE )
 	    {
 	    	Faults_SetFault(CFLT__MR_OFFLINE_CT);
 	    }
        if( !bLastOfflineFlag_MR )
        {
        	ClearMRADatagrams();
        	ClearMRBDatagrams();
        }
    }
    else
    {
 	    bOfflineFlag_MR = INACTIVE;
        uwOfflineTimer_MR_1ms += CAN1_TASK_DELAY;
    }
    /* COP */
    if( uwOfflineTimer_COP_1ms >= COP_OFFLINE_TIMEOUT_1MS )
    {
 	    bOfflineFlag_COP = ACTIVE;
 	    if( bBypassCommLoss == INACTIVE )
 	    {
 	        Faults_SetFault(CFLT__COP_OFFLINE_CT);
 	    }
        if( !bLastOfflineFlag_COP )
        {
        	ClearCOPDatagrams();
        }
    }
    else
    {
 	    bOfflineFlag_COP = INACTIVE;
        uwOfflineTimer_COP_1ms += CAN1_TASK_DELAY;
    }
    /* UI MR */
    if( uwOfflineTimer_UI_MR_1ms >= UI_MR_OFFLINE_TIMEOUT_1MS )
    {
 	    bOfflineFlag_UI_MR = ACTIVE;
        /* TODO add alarm */
        if( !bLastOfflineFlag_UI_MR )
        {
        	ClearUIDatagrams(SYS_NODE__UI_MR_A7);
        }
    }
    else
    {
 	    bOfflineFlag_UI_MR = INACTIVE;
        uwOfflineTimer_UI_MR_1ms += CAN1_TASK_DELAY;
    }
    /* UI COP */
    if( uwOfflineTimer_UI_COP_1ms >= UI_COP_OFFLINE_TIMEOUT_1MS )
    {
 	    bOfflineFlag_UI_COP = ACTIVE;
        /* TODO add alarm */
        if( !bLastOfflineFlag_UI_COP )
        {
        	ClearUIDatagrams(SYS_NODE__UI_COP_A7);
        }
    }
    else
    {
 	    bOfflineFlag_UI_COP = INACTIVE;
        uwOfflineTimer_UI_COP_1ms += CAN1_TASK_DELAY;
    }

    bLastOfflineFlag_MR = bOfflineFlag_MR;
    bLastOfflineFlag_COP = bOfflineFlag_COP;
    bLastOfflineFlag_UI_MR = bOfflineFlag_UI_MR;
    bLastOfflineFlag_UI_COP = bOfflineFlag_UI_COP;
}

/* @fn void vCAN1_Task(void *pvParameters)
 * @brief Handles CAN1 communication, updates bus error counters, and manages errors based on board type
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCAN1_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		UnloadDatagrams();

		UpdateOfflineTimer();

		CAN1_UpdateBusErrorCounter();

		if( CAN1_CheckForBusOffline() )
		{
			Alarms_SetAlarm(CALM__BUS_RESET_CT_N1);
		}

		vTaskDelay(CAN1_TASK_DELAY);
	}
}
