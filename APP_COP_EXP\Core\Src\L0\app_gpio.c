/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : app_gpio.c
 * @version 		  : 1.0.0
 * @brief          : GPIO accessing and initialization
 * @details		  : GPIO accessing and initialization
 ********************************************************************************/

/*******************************************************************************
 * Includes
 ********************************************************************************/
#include "app_gpio.h"
#include "system.h"
/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/
/* Pin set command must be shifted by this offset to make it a clear command */
#define GPIO_BSRR_RESET_SHIFT		(16)

/*******************************************************************************
 * Preprocessor Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variable Definitions
 ********************************************************************************/

/*******************************************************************************
 * Variable Definitions
 ********************************************************************************/
const st_gpio_def astGPIO_Def_Inputs[NUM_LOCAL_INPUTS] = {
#define LOCAL_GP_INPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) {PORT, MODE, PIN, PULL, SPEED, INVERT},
                                                           LOCAL_INPUT_TABLE
#undef LOCAL_GP_INPUT
                                                           }
;

const st_gpio_def astGPIO_Def_Outputs[NUM_LOCAL_OUTPUTS] = {
#define LOCAL_GP_OUTPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) {PORT, MODE, PIN, PULL, SPEED, INVERT},
                                                             LOCAL_OUTPUT_TABLE
#undef LOCAL_GP_OUTPUT
                                                             }
;
/* Output read register for this chip queries the actual status of the output pin. Instead track the last command issued to the pin */
static uint8_t aucLastOutputCommand[BITMAP8_ARRAY_SIZE(NUM_LOCAL_OUTPUTS)];
/*******************************************************************************
 * Function Definitions
 ********************************************************************************/
/**
 * @fn  uint8_t GPIO_ReadDIPBank(void)
 * @brief Reads the status of the DIP bank
 * @param void
 * @return Returns the value of the dip bank in bitmap form
 */
uint8_t GPIO_ReadDIPBank(void) {
   uint8_t ucBank = 0;
   for(en_local_inputs eInput = enLOCAL_IN__DIP_SW_01; eInput <= enLOCAL_IN__DIP_SW_08;
         eInput++) {
      uint8_t ucShift = eInput - enLOCAL_IN__DIP_SW_01;
      ucBank |= GPIO_ReadInput(eInput) << ucShift;
   }

   return ucBank;
}
/**
 * @fn  uint8_t GPIO_ReadInput(en_local_inputs eInput)
 * @brief Reads the current status of the specified input
 * @param eInput, Input to check
 * @return Returns 1 if current input status is active, 0 otherwise
 */
uint8_t GPIO_ReadInput(en_local_inputs eInput) {
   uint8_t bActive;
   if((astGPIO_Def_Inputs[eInput].pGPIO_Port->IDR & astGPIO_Def_Inputs[eInput].uwPin)
      != 0) {
      bActive = 1;
   } else {
      bActive = 0;
   }
   bActive ^= astGPIO_Def_Inputs[eInput].bInvert;
   return bActive;
}
/**
 * @fn  uint8_t GPIO_ReadOutput(en_local_outputs eOutput)
 * @brief Reads the current status of the specified output
 * @param eOutput, Output to check
 * @return Returns 1 if current output status is active, 0 otherwise
 */
uint8_t GPIO_ReadOutput(en_local_outputs eOutput) {
   uint8_t bActive;
   if((astGPIO_Def_Outputs[eOutput].pGPIO_Port->ODR & astGPIO_Def_Outputs[eOutput].uwPin)
      != 0) {
      bActive = 1;
   } else {
      bActive = 0;
   }
   bActive ^= astGPIO_Def_Outputs[eOutput].bInvert;
   return bActive;
}
/**
 * @fn  uint8_t GPIO_ReadOutputCommand(en_local_outputs eOutput)
 * @brief Returns the last command issued to the output
 * @param eOutput, Output to check
 * @return Returns 1 if current output status is active, 0 otherwise
 */
uint8_t GPIO_ReadOutputCommand(en_local_outputs eOutput) {
   uint8_t bActive;
   uint8_t ucBitIndex = eOutput % BITMAP8_SIZE_IN_BITS;
   uint8_t ucByteIndex = eOutput / BITMAP8_SIZE_IN_BITS;
   bActive = (aucLastOutputCommand[ucByteIndex] >> ucBitIndex) & 1;
   return bActive;
}
/**
 * @fn  uint8_t *GPIO_GetOutputCommandPointer(void)
 * @brief Returns pointer to output command bitmap
 */
uint8_t* GPIO_GetOutputCommandPointer(void) {
   return &aucLastOutputCommand[0];
}
/**
 * @fn  void GPIO_WriteOutput(en_local_outputs eOutput, uint8_t bActive)
 * @brief Writes a state to the specified output pin
 * @param eOutput, Output to check. bActive state to write to the output
 * @return None
 */
void GPIO_WriteOutput(en_local_outputs eOutput, uint8_t bActive) {
   uint8_t ucBitIndex = eOutput % BITMAP8_SIZE_IN_BITS;
   uint8_t ucByteIndex = eOutput / BITMAP8_SIZE_IN_BITS;
   if(bActive) {
      aucLastOutputCommand[ucByteIndex] |= (1 << ucBitIndex);
   } else {
      aucLastOutputCommand[ucByteIndex] &= ~(1 << ucBitIndex);
   }
   bActive ^= astGPIO_Def_Outputs[eOutput].bInvert;
   if(bActive)// Set output
   {
      astGPIO_Def_Outputs[eOutput].pGPIO_Port->BSRR = astGPIO_Def_Outputs[eOutput].uwPin;
   } else// Clear output
   {
      astGPIO_Def_Outputs[eOutput].pGPIO_Port->BSRR = astGPIO_Def_Outputs[eOutput].uwPin
                                                      << GPIO_BSRR_RESET_SHIFT;
   }
}
/**
 * @fn  uint8_t GPIO_Init(void)
 * @brief A function to initialize all GPIO
 * @param None
 * @return None
 */
void GPIO_Init(void) {
   GPIO_InitTypeDef GPIO_InitStruct = { 0 };

   /* GPIO Ports Clock Enable */
   __HAL_RCC_GPIOA_CLK_ENABLE();
   __HAL_RCC_GPIOB_CLK_ENABLE();
   __HAL_RCC_GPIOC_CLK_ENABLE();
   __HAL_RCC_GPIOD_CLK_ENABLE();
   __HAL_RCC_GPIOE_CLK_ENABLE();
   __HAL_RCC_GPIOF_CLK_ENABLE();
   __HAL_RCC_GPIOG_CLK_ENABLE();

   /* Write all outputs to off */
   for(uint8_t i = 0; i < NUM_LOCAL_OUTPUTS; i++) {
      GPIO_WriteOutput(i, 0);
   }

#define LOCAL_GP_INPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) \
	GPIO_InitStruct.Pin = PIN; \
	GPIO_InitStruct.Mode = MODE; \
	GPIO_InitStruct.Pull = PULL; \
	GPIO_InitStruct.Speed = SPEED; \
	GPIO_InitStruct.Alternate = 0; \
	HAL_GPIO_Init(PORT, &GPIO_InitStruct);
   LOCAL_INPUT_TABLE
#undef LOCAL_GP_INPUT

#define LOCAL_GP_OUTPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) \
	GPIO_InitStruct.Pin = PIN; \
	GPIO_InitStruct.Mode = MODE; \
	GPIO_InitStruct.Pull = PULL; \
	GPIO_InitStruct.Speed = SPEED; \
	GPIO_InitStruct.Alternate = 0; \
	HAL_GPIO_Init(PORT, &GPIO_InitStruct);
   LOCAL_OUTPUT_TABLE
#undef LOCAL_GP_OUTPUT
   /* Write all outputs to off */
   for(uint8_t i = 0; i < NUM_LOCAL_OUTPUTS; i++) {
      GPIO_WriteOutput(i, 0);
   }
}

