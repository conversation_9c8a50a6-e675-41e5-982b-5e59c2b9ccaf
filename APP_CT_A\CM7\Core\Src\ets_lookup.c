#include "ets_lookup.h"

ets_lut_t ETS_LookupTable[ETS_TABLE_COLUMN_SIZE] =
{
    {.speed = 50,   .position = 4},   /* 50   fpm*/
    {.speed = 75,   .position = 7},   /* 75   fpm*/
    {.speed = 100,  .position = 10},  /* 100  fpm*/
    {.speed = 150,  .position = 17},  /* 150  fpm*/
    {.speed = 200,  .position = 22},  /* 200  fpm*/
    {.speed = 250,  .position = 32},  /* 250  fpm*/
    {.speed = 300,  .position = 37},  /* 300  fpm*/
    {.speed = 350,  .position = 48},  /* 350  fpm*/
    {.speed = 400,  .position = 60},  /* 400  fpm*/
    {.speed = 450,  .position = 74},  /* 450  fpm*/
    {.speed = 500,  .position = 83},  /* 500  fpm*/
    {.speed = 600,  .position = 114}, /* 600  fpm*/
    {.speed = 700,  .position = 151}, /* 700  fpm*/
    {.speed = 800,  .position = 192}, /* 800  fpm*/
    {.speed = 900,  .position = 220}, /* 900  fpm*/
    {.speed = 1000, .position = 267}, /* 1000 fpm*/
    {.speed = 1100, .position = 319}, /* 1100 fpm*/
    {.speed = 1200, .position = 375}, /* 1200 fpm*/
    {.speed = 1300, .position = 404}, /* 1300 fpm*/
    {.speed = 1400, .position = 464}, /* 1400 fpm*/
};


