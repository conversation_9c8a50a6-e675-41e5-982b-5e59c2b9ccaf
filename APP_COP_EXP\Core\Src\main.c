/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "sr_fault_alarm.h"
#include "main.h"
#include "sr_LED.h"
#include "sr_Watchdog.h"
#include "sr_CAN1.h"
#include "sr_local_inputs.h"
#include "sr_local_outputs.h"
#include "sr_car_net.h"
#include "sr_SPI_AU.h"
#include "sr_parameters.h"
#include "sr_RTC.h"
#include "sr_CAN2.h"
#include "sr_hall_net.h"
#include "sr_CAN3.h"
#include "sr_fixture_net_1.h"
#include "sr_fixture_net_2.h"

#include "app_gpio.h"
#include "app_timers.h"
#include "app_crc.h"
#include "app_can.h"
#include "app_watchdog.h"
#include "app_spi.h"

#include "bootloader_def.h"
#include "ui_request_def.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
//#ifdef FLASH_LATENCY_4
//#undef FLASH_LATENCY_4
//#define FLASH_LATENCY_4 (0x00000004UL)//AS todo investigate if causes hardfaults when less than 6.
//#endif

static en_expansion_id eLocalExpansionIndex = NUM_EXPANSION_ID;
static en_riser_id eLocalRiserIndex = NUM_RISER_ID;
static en_local_board_type eLocalBoardType = BOARD_INVALID;
static uint8_t ucDipSwitchAddress;
static void CheckDipConfiguration(void) {
   System_SetNodeID(NUM_SYS_NODES);
   ucDipSwitchAddress = GPIO_ReadDIPBank();
   uint8_t ucMainAddress = ucDipSwitchAddress & 0x7F;
// Set system ID
   switch(ucMainAddress) {
      case DIP_ADDRESS__COP:
         System_SetNodeID(SYS_NODE__COP_A4);
         eLocalBoardType = BOARD_COP;
         break;
      case DIP_ADDRESS__EXP_M1 ... (DIP_ADDRESS__EXP_M5+EXPANSION_MAX_BOARDS_PER_MASTER):
         System_SetNodeID(SYS_NODE__EXP);
         eLocalExpansionIndex = ucMainAddress - 1;
         eLocalBoardType =
               ((eLocalExpansionIndex % EXPANSION_MAX_BOARDS_PER_MASTER) == 0) ? BOARD_EXP_MASTER :
                                                                                 BOARD_EXP_SLAVE;
         break;
      case DIP_ADDRESS__RISER1 ... DIP_ADDRESS__RISER4:
         System_SetNodeID(SYS_NODE__RISER);
         eLocalBoardType = BOARD_RISER;
         eLocalRiserIndex = ucMainAddress - DIP_ADDRESS__RISER1;
         break;
      case DIP_ADDRESS__TEST:
         System_SetNodeID(SYS_NODE__COP_A4);
         eLocalBoardType = BOARD_TEST;
         break;
      default:
         break;
   }
}
/* USER CODE END Includes */
#define __APP_HEADER_SECTION     __attribute__((section(".app_header")))
__APP_HEADER_SECTION const uint32_t __app_header[] =
{
 0x34415043, // APP ID
 SOFTWARE_VERSION_MAJOR | (SOFTWARE_VERSION_MINOR  << 16),  // VERSION 0
 SOFTWARE_VERSION_PATCH | (SOFTWARE_VERSION_CUSTOM << 16), // VERSION 1
 0, // ALIGN 0
 0, // ALIGN 1
 0xEEEEEEEE, // DUMMY SIZE
 0xCCCCCCCC, // DUMMY CRC_STORED
 0xBBBBBBBB, // DUMMY FILE_OFFSET
};


/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */
/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
DEFINE_STATIC_TASK(xInitTask, INIT_TASK_STACK_SIZE) // For the Initial Task from Main()

/* Define Static Buffers for All Tasks */
DEFINE_STATIC_TASK(FaultAlarm_Task, FAULT_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CAN1_Task, CAN1_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CAN2_Task, CAN2_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(LocalInputs_Task, LOCAL_INPUTS_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(LocalOutputs_Task, LOCAL_OUTPUTS_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CarNet_Task, CARNET_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(SPIAU_Task, SPIAU_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Parameters_Task, PARAMETERS_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(HallNet_Task, HALL_NET_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CAN3_Task, CAN3_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(FixtureNet1_Task, FIXTURE_NET_1_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(FixtureNet2_Task, FIXTURE_NET_2_TASK_STACK_SIZE)

/*Task Parameters Array */
TaskParams_t TaskParams[] = {
	  {vFaultAlarm_Task, 	  "FaultAlarm_Task",	  FAULT_TASK_STACK_SIZE,		     NULL, FAULT_TASK_PRIORITY, 		    FaultAlarm_Task_Stack, 	  &FaultAlarm_Task_TCB},
    {vCAN1_Task,          "CAN1_Task",          CAN1_TASK_STACK_SIZE,          NULL, CAN1_TASK_PRIORITY,          CAN1_Task_Stack,          &CAN1_Task_TCB},
    {vCAN2_Task,          "CAN2_Task",          CAN2_TASK_STACK_SIZE,          NULL, CAN2_TASK_PRIORITY,          CAN2_Task_Stack,          &CAN2_Task_TCB},
    {vLocalInputs_Task,   "LocalInputs_Task",   LOCAL_INPUTS_TASK_STACK_SIZE,  NULL, LOCAL_INPUTS_TASK_PRIORITY,  LocalInputs_Task_Stack,   &LocalInputs_Task_TCB},
    {vLocalOutputs_Task,  "LocalOutputs_Task",  LOCAL_OUTPUTS_TASK_STACK_SIZE, NULL, LOCAL_OUTPUTS_TASK_PRIORITY, LocalOutputs_Task_Stack,  &LocalOutputs_Task_TCB},
    {vCarNet_Task,        "CarNet_Task",        CARNET_TASK_STACK_SIZE,        NULL, CARNET_TASK_PRIORITY,        CarNet_Task_Stack,        &CarNet_Task_TCB},
    {vSPIAU_Task,         "SPIAU_Task",         SPIAU_TASK_STACK_SIZE,         NULL, SPIAU_TASK_PRIORITY,         SPIAU_Task_Stack,         &SPIAU_Task_TCB},
    {vParameters_Task,    "Parameters_Task",    PARAMETERS_TASK_STACK_SIZE,    NULL, PARAMETERS_TASK_PRIORITY,    Parameters_Task_Stack,    &Parameters_Task_TCB},
    {vHallNet_Task,       "HallNet_Task",       HALL_NET_TASK_STACK_SIZE,      NULL, HALL_NET_TASK_PRIORITY,      HallNet_Task_Stack,       &HallNet_Task_TCB},
    {vCAN3_Task,          "CAN3_Task",          CAN3_TASK_STACK_SIZE,          NULL, CAN3_TASK_PRIORITY,          CAN3_Task_Stack,          &CAN3_Task_TCB},
    {vFixtureNet1_Task,   "FixtureNet1_Task",   FIXTURE_NET_1_TASK_STACK_SIZE, NULL, FIXTURE_NET_1_TASK_PRIORITY, FixtureNet1_Task_Stack,   &FixtureNet1_Task_TCB},
    {vFixtureNet2_Task,   "FixtureNet2_Task",   FIXTURE_NET_2_TASK_STACK_SIZE, NULL, FIXTURE_NET_2_TASK_PRIORITY, FixtureNet2_Task_Stack,   &FixtureNet2_Task_TCB},
};//12 Tasks
const uint32_t TaskParamsCount = sizeof(TaskParams) / sizeof(TaskParams_t);

#define DWT_CTRL (*(volatile uint32_t *)0xE0001000)
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
static void SystemClock_ConfigUser(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* @fn void Modules_Init(void)
 * @brief Initializes all required modules for the system
 * @param None
 * @return None
 */
static void Modules_Init(void)
{
	Init_Watchdog();
	FaultAlarm_Init();
	CAN2_Task_Init();
	CarNet_Task_Init();
	SPIAU_Task_Init();
	Parameters_Task_Init();
	HallNet_Task_Init();
	CAN3_Task_Init();
	FixtureNet1_Task_Init();
	FixtureNet2_Task_Init();
	RTC_Init();
}

/* @fn void Hardware_Init(void)
 * @brief Initializes hardware components for the system
 * @param None
 * @return None
 */
static void Hardware_Init(void)
{
	/* Hardware initialization */
	GPIO_Init();
	CheckDipConfiguration();

	Timer2_Init();
	Timer3_Init();
	Timer5_Init();

	CRC_Init();

	if(Main_GetLocalBoardType() == BOARD_COP) {
	  MX_SPI1_Init();
	  MX_SPI2_Init();

	  Param_Init();
	}
	else if(Main_GetLocalBoardType() == BOARD_RISER) {
	  /* For queuing requests from UI that must be responded to. */
	  UI_Request_InitBuffer();
	}
}

/* @fn void vInitTask(void *pvParameters)
 * @brief Initializes system and tasks, then deletes itself
 * @param pvParameters Pointer to parameters passed to the task
 * @return None
 */
static void vInitTask(void *pvParameters)
{
    #if configUSE_TRACE_FACILITY == 1
    SEGGER_SYSVIEW_Conf();
    SEGGER_SYSVIEW_Start();
    #endif

    Hardware_Init();

    /* Call the init function of all modules */
    vIdleTaskModulesInit();
    Modules_Init();

    DWT_CTRL |= ( 1<<0 );
    /* Static task allocation */
    for (uint32_t i = 0; i < TaskParamsCount; i++)
    {
        TaskParams_t *task = &TaskParams[i];

        // Ensure each task has a dedicated buffer and stack
        if (task->stack_buffer != NULL && task->task_control_block != NULL)
        {
            xTaskCreateStatic(task->function,  // Task function
                              task->name,      // Task name
                              task->stack_size,// Stack size (in words)
                              task->parameters,// Task parameters
                              task->priority,  // Task priority
                              task->stack_buffer,     // Stack buffer
                              task->task_control_block);        // Task TCB
        }
        else
        {
            // Handle errors (e.g., log or assert)
        }
    }
    /* Delete this task */
    vTaskDelete(NULL);
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */
  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */
  SystemClock_ConfigUser();
  /* USER CODE END Init */

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* USER CODE BEGIN 2 */



  /* Initialize all configured peripherals */
  /* USER CODE BEGIN 2 */
   /* Create init task */
   	xTaskCreateStatic(
   	        vInitTask,                 // Task function
   	        "InitTask",                // Task name
   	        INIT_TASK_STACK_SIZE,      // Stack size (in words)
   	        (void*)0,                  // Task parameter
   	        tskIDLE_PRIORITY,          // Task priority
   	        xInitTask_Stack,            // Pointer to the task's stack
   	        &xInitTask_TCB           // Pointer to the task's TCB
   	    );

   	vTaskStartScheduler();
  /* USER CODE END 2 */
  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */

  /* USER CODE END 3 */
/* Should never reach this point */
	for(;;);
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1_BOOST);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_BYPASS;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = RCC_PLLM_DIV5;
  RCC_OscInitStruct.PLL.PLLN = 68;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = RCC_PLLQ_DIV2;
  RCC_OscInitStruct.PLL.PLLR = RCC_PLLR_DIV2;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
en_expansion_id Main_GetLocalExpansionID(void) {
   return eLocalExpansionIndex;
}
en_riser_id Main_GetLocalRiserID(void) {
   return eLocalRiserIndex;
}
en_local_board_type Main_GetLocalBoardType(void) {
   return eLocalBoardType;
}
uint8_t Main_GetStartupDipAddress(void) {
   return ucDipSwitchAddress;
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
static void SystemClock_ConfigUser(void) {
   RCC_OscInitTypeDef RCC_OscInitStruct = { 0 };
   RCC_ClkInitTypeDef RCC_ClkInitStruct = { 0 };

   /** Configure the main internal regulator output voltage
    */
   HAL_PWREx_ControlVoltageScaling(PWR_REGULATOR_VOLTAGE_SCALE1_BOOST);

   /** Initializes the RCC Oscillators according to the specified parameters
    * in the RCC_OscInitTypeDef structure.
    */
   RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE;
   RCC_OscInitStruct.HSEState = RCC_HSE_BYPASS;
   RCC_OscInitStruct.LSIState = RCC_LSI_ON;
   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;// 25 MHZ
   RCC_OscInitStruct.PLL.PLLM = RCC_PLLM_DIV5;// PLL_IN = HSE_OSC/5 = 5MHZ
   RCC_OscInitStruct.PLL.PLLN = 68;// PLLN_CLK = PLL_IN * 68 = 340 MHZ
   RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;// PLLP = PLLN_CLK / 2 = 170 MHZ -> SYSCLK, TIMERS, CAN
   RCC_OscInitStruct.PLL.PLLQ = RCC_PLLQ_DIV2;// PLLQ = PLLN_CLK / 2 = 170 MHZ -> N/A
   RCC_OscInitStruct.PLL.PLLR = RCC_PLLR_DIV2;// PLLR = PLLN_CLK / 2 = 170 MHZ -> N/A
   if(HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
      Error_Handler();
   }

   /** Initializes the CPU, AHB and APB buses clocks
    */
   RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK
                                 | RCC_CLOCKTYPE_SYSCLK
                                 | RCC_CLOCKTYPE_PCLK1
                                 | RCC_CLOCKTYPE_PCLK2;
   RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
   RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
   RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
   RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;
// NOTE: Observed intermittent app write hardfaults when set to the default 4 cycles. ks 12/29/23
   if(HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
      Error_Handler();
   }
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
   /* User can add his own implementation to report the HAL error return state */
   __disable_irq();
   while(1) {
   }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

/* @fn void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName)
 * @brief Stack Overflow hook for FreeRTOS tasks.
 *        This function is called when a stack overflow is detected in any task.
 *        It halts the system if debugging is enabled and performs a system reset.
 * @param xTask Handle of the task that caused the stack overflow.
 * @param pcTaskName Name of the task that caused the stack overflow.
 * @return None
 */
void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName)
{
//	HALT_IF_DEBUGGING();

	/*Reset system*/
}
