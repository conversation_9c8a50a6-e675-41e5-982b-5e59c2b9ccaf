#include "nts_traction.h"
#include "car_faults_def.h"
#include "car_alarms_def.h"
#include "sr_position.h"
#include "operation_def.h"

#include "ulog.h"

void NTS_Traction_Init(nts_traction_t * const self)
{
  NTS_Traction_LoadPoints(&self->unts,
      CPARAM8__Traction_UNTS_Count,
      CPARAM24__Traction_UNTS_Prof_1_Pos_1,
      CPARAM16__Traction_UNTS_Prof_1_Speed_1);
  NTS_Traction_LoadPoints(&self->dnts,
      CPARAM8__Traction_DNTS_Count,
      CPARAM24__Traction_DNTS_Prof_1_Pos_1,
      CPARAM16__Traction_DNTS_Prof_1_Speed_1);
}

void NTS_Traction_LoadPoints(nts_points_t * const self,
    en_car_param_8bit count_param_base,
    en_car_param_24bit pos_param_base,
    en_car_param_16bit speed_param_base)
{
  self->count = Param_ReadValue_8Bit(count_param_base);
  for(uint32_t i = 0; i < self->count; i++)
  {
    self->position[i] = Param_ReadValue_24Bit(pos_param_base + i);
    self->speed[i]    = (int16_t)Param_ReadValue_16Bit(speed_param_base + i);
  }

  self->index = 0;
}

en_active_inactive NTS_Traction_CheckNonZero(nts_points_t * const self)
{
  en_active_inactive status = INACTIVE;
  if(self->count == 0)
  {
    status = ACTIVE;
  }

  for(uint32_t i = 0; i < self->count; i++)
  {
    if((self->position[i] == 0) ||
       (self->speed[i]    == 0))
    {
      status = ACTIVE;
      break;
    }
  }

  return status;
}

en_active_inactive NTS_Traction_CheckUNTSOrder(nts_points_t * const self)
{
  // UNTS position ascending, speed descending
  en_active_inactive status = INACTIVE;
  if(self->count > 1)
  {
    for(uint32_t i = 0; i < (self->count - 1); i++)
    {
      // Position
      if( (self->position[i] > self->position[i + 1]) ||
          (self->speed[i]    < self->speed[i + 1]))
      {
        status = ACTIVE;
        break;
      }
    }
  }

  return status;
}

en_active_inactive NTS_Traction_CheckDNTSOrder(nts_points_t * const self)
{
  // DNTS position descending, speed ascending
  en_active_inactive status = INACTIVE;
  if(self->count > 1)
  {
    for(uint32_t i = 0; i < (self->count - 1); i++)
    {
      // Position
      if( (self->position[i] < self->position[i + 1]) ||
          (self->speed[i]    > self->speed[i + 1]))
      {
        status = ACTIVE;
        break;
      }
    }
  }

  return status;
}

en_active_inactive NTS_Traction_CheckNTSPoints(nts_points_t  * const self,
    nts_check_t type)
{
  en_active_inactive status = INACTIVE;

  // TODO:Contract speed had changed, we might require to re-do the learn
  // How can we detect this?

  // Check that all position and speed values are non-zero
  status = NTS_Traction_CheckNonZero(self);

  // Check descending order on both position and speed
  if(type == CHECK_UNTS)
  {
    status = NTS_Traction_CheckUNTSOrder(self);
  }
  else
  {
    status = NTS_Traction_CheckDNTSOrder(self);
  }

  return status;
}

en_active_inactive NTS_Traction_UNTSCheck(nts_points_t * const self,
    uint32_t uiPosition_05mm,
    int16_t uwSpeed_fpm)
{
  en_active_inactive bNTS     = INACTIVE;

  uint32_t index              = self->index;
  uint32_t uiNextPos_NTS_05mm = self->position[index];
  int16_t uwNextSpeed_fpm     = self->speed[index];

  if(uiPosition_05mm >= uiNextPos_NTS_05mm)
  {
    if(uwSpeed_fpm >= (uwNextSpeed_fpm + Param_ReadValue_8Bit(CPARAM8__Traction_NTS_Tolerance_fpm)))
    {
      // Trigger UNTS
      ULOG_DEBUG("UNTS triggered at point (%i, %i)", uiPosition_05mm, uwSpeed_fpm);
      bNTS = ACTIVE;
    }

    self->index = (index < self->count) ? ++index : index;
  }

  return bNTS;
}

en_active_inactive NTS_Traction_DNTSCheck(nts_points_t * const self,
    uint32_t uiPosition_05mm,
    int16_t uwSpeed_fpm)
{
  en_active_inactive bNTS     = INACTIVE;

  uint32_t index              = self->index;
  uint32_t uiNextPos_NTS_05mm = self->position[index];
  int16_t uwNextSpeed_fpm     = self->speed[index];

  if(uiPosition_05mm <= uiNextPos_NTS_05mm)
  {
    if(uwSpeed_fpm <= (uwNextSpeed_fpm - Param_ReadValue_8Bit(CPARAM8__Traction_NTS_Tolerance_fpm)))
    {
      // Trigger DNTS
      ULOG_DEBUG("DNTS triggered at point (%i, %i)", uiPosition_05mm, uwSpeed_fpm);
      bNTS = ACTIVE;
    }

    self->index = (index < self->count) ? ++index : index;
  }

  return bNTS;
}

void NTS_Traction_Run(nts_traction_t * const self)
{
  uint32_t uiPosition_05mm = Position_GetPositionNTS_05mm();
  int16_t uwSpeed_fpm      = Position_GetSpeedNTS_fpm();

  switch(NTS_CAST(self)->eState)
  {
  case NTS_STATE__IDLE:
    if(uwSpeed_fpm > 0)
    {
      // Going up, start checking UNTS
      NTS_INDEX_RESET(self->unts);
      NTS_CAST(self)->eState = NTS_STATE__CHECK_UNTS;
    }
    else if(uwSpeed_fpm < 0)
    {
      // Going down start checking DNTS
      NTS_INDEX_RESET(self->dnts);
      NTS_CAST(self)->eState = NTS_STATE__CHECK_DNTS;
    }
    break;
  case NTS_STATE__CHECK_UNTS:
  {
    en_active_inactive bUNTS = NTS_Traction_UNTSCheck(&self->unts,
        uiPosition_05mm,
        uwSpeed_fpm);
    if(bUNTS == ACTIVE)
    {
      // UNTS triggered
      NTS_CAST(self)->bTripped_UNTS = ACTIVE;
      NTS_CAST(self)->eState        = NTS_STATE__TRIPPED;
    }
    else if(uwSpeed_fpm == 0)
    {
      NTS_CAST(self)->eState = NTS_STATE__IDLE;
    }
  }
    break;
  case NTS_STATE__CHECK_DNTS:
  {
    en_active_inactive bDNTS = NTS_Traction_DNTSCheck(&self->dnts,
        uiPosition_05mm,
        uwSpeed_fpm);
    if(bDNTS == ACTIVE)
    {
      // DNTS triggered
      NTS_CAST(self)->bTripped_DNTS = ACTIVE;
      NTS_CAST(self)->eState        = NTS_STATE__TRIPPED;
    }
    else if(uwSpeed_fpm == 0)
    {
      NTS_CAST(self)->eState = NTS_STATE__IDLE;
    }
  }
    break;
  case NTS_STATE__TRIPPED: /* NTS tripped, awaiting car to stop */
    if( uwSpeed_fpm == 0 )
    {
      NTS_CAST(self)->eState = NTS_STATE__IDLE;
      NTS_CAST(self)->bTripped_UNTS = INACTIVE;
      NTS_CAST(self)->bTripped_DNTS = INACTIVE;
    }
    else if((NTS_CAST(self)->bTripped_UNTS    == ACTIVE) &&
            (Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN))
    {
      Alarms_SetAlarm(CALM__MCU_NTS_UP);
    }
    else if((NTS_CAST(self)->bTripped_DNTS    == ACTIVE) &&
            (Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN))
    {
      Alarms_SetAlarm(CALM__MCU_NTS_DOWN);
    }
    break;
//  case NTS_STATE__LEARN_NEEDED:
//    if(Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN)
//    {
//      Faults_SetFault(CFLT__NTS_TRACTION_LEARN);
//      if(NTS_Traction_Init(self) == INACTIVE)
//      {
//        NTS_CAST(self)->eState = NTS_STATE__IDLE;
//      }
//    }
//    break;
  default:
    NTS_CAST(self)->eState = NTS_STATE__IDLE;
    break;
  }
}



