/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app.h
* @version 		  : 1.0.0
* @brief          : Main program body
* @details		  : Main program body declarations
********************************************************************************/
#ifndef _APP_H_
#define _APP_H_
/*******************************************************************************
* Includes
********************************************************************************/

#include "FreeRTOS.h"
#include "task.h"

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/


/*******************************************************************************
* Macros
********************************************************************************/

/*CT A CM7 tasks priorities*/
#define FAULT_ALARM_TASK_PRIORITY			(tskIDLE_PRIORITY)
#define LOCAL_INPUT_TASK_PRIORITY			(tskIDLE_PRIORITY)
#define LOCAL_OUTPUT_TASK_PRIORITY		(tskIDLE_PRIORITY)
#define SAFETY_TASK_PRIORITY				  (tskIDLE_PRIORITY)
#define OPERATION_TASK_PRIORITY				(tskIDLE_PRIORITY)
#define LEARNING_TASK_PRIORITY				(tskIDLE_PRIORITY)
#define LEARNING_V2_TASK_PRIORITY     (tskIDLE_PRIORITY)
#define ETS_TASK_PRIORITY             (tskIDLE_PRIORITY)
#define POSITION_TASK_PRIORITY				(tskIDLE_PRIORITY)
#define FRAM_TASK_PRIORITY					  (tskIDLE_PRIORITY)
#define PARAMETERS_TASK_PRIORITY			(tskIDLE_PRIORITY)
#define CAN1_TASK_PRIORITY					  (tskIDLE_PRIORITY)
#define CAN2_TASK_PRIORITY					  (tskIDLE_PRIORITY)
#define SPIAB_TASK_PRIORITY					  (tskIDLE_PRIORITY)
#define SPIAU_TASK_PRIORITY					  (tskIDLE_PRIORITY)
#define CAR_NET_TASK_PRIORITY				  (tskIDLE_PRIORITY)
#define SYSTEM_CHECK_TASK_PRIORITY    (tskIDLE_PRIORITY)

/*CT A CM7 tasks stack size */
#define FAULT_ALARM_TASK_STACK_SIZE		(256)
#define LEARNING_TASK_STACK_SIZE			(256)
#define LEARNING_V2_TASK_STACK_SIZE   (256)
#define LOCAL_INPUT_TASK_STACK_SIZE		(256)
#define LOCAL_OUTPUT_TASK_STACK_SIZE  (256)
#define SAFETY_TASK_STACK_SIZE			  (256)
#define OPERATION_TASK_STACK_SIZE		  (256)
#define POSITION_TASK_STACK_SIZE		  (256)
#define FRAM_TASK_STACK_SIZE				  (256)
#define PARAMETERS_TASK_STACK_SIZE	  (256)
#define CAN1_TASK_STACK_SIZE				  (256)
#define CAN2_TASK_STACK_SIZE				  (256)
#define SPIAB_TASK_STACK_SIZE				  (256)
#define SPIAU_TASK_STACK_SIZE				  (256)
#define CAR_NET_TASK_STACK_SIZE			  (2048)
#define ETS_TASK_STACK_SIZE           (256)
#define SYSTEM_CHECK_TASK_STACK_SIZE  (128)

/*CT A CM7 tasks stack size */
#define FAULT_ALARM_TASK_DELAY	(50)
#define LOCAL_INPUT_TASK_DELAY	(5)
#define LOCAL_OUTPUT_TASK_DELAY	(5)
#define SAFETY_TASK_DELAY				(10)
#define OPERATION_TASK_DELAY		(50)
#define LEARNING_TASK_DELAY			(50)
#define POSITION_TASK_DELAY			(2)
#define FRAM_TASK_DELAY					(10)
#define PARAMETERS_TASK_DELAY		(10)
#define CAN1_TASK_DELAY					(5)
#define CAN2_TASK_DELAY					(5)
#define SPIAB_TASK_DELAY				(5)
#define SPIAU_TASK_DELAY				(5)
#define CAR_NET_TASK_DELAY			(5)
#define ETS_TASK_DELAY          (50)
#define SYSTEM_CHECK_TASK_DELAY (900)

/* Idle task module delays */
#define LED_TICKS_PERIOD				    (100)
#define LOW_PRIORITY_TICKS_PERIOD		(3000)
#define RTC_TICKS_PERIOD				    (1000)
#define WATCHDOG_TICKS_PERIOD			  (500)

/*******************************************************************************
* Typedefs
********************************************************************************/
typedef struct
{
	TaskFunction_t function;
	const char * const name;
	const size_t stack_size;
	void * const parameters;
	UBaseType_t priority;
  StackType_t * const stack_uffer;
  StaticTask_t * const task_buffer;
}TaskParams_t;


/*******************************************************************************
* Global Variables
********************************************************************************/


/*******************************************************************************
* Function Prototypes
********************************************************************************/
void vIdleTaskModulesInit(void);
#endif /* _APP_H_ */
