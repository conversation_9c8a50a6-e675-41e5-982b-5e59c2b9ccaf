/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_Fault.c
 * @version 		  : 1.0.0
 * @brief Subroutine updating processor's fault status
 * @details Subroutine updating processor's fault status
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
//#include "main.h"
#include "sr_fault_alarm.h"
#include "car_alarms_def.h"
#include "car_faults_def.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/

#define ERROR_MIN_TIMEOUT_50MS									  (120)
#define ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS				  (200)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/


/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
uint32_t gFaultAlarm_Task_Delay = 0; // Global variable to hold the delay interval
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static const uint8_t aucPriority[NUM_EXP_ERROR] =
{
   255, //EXP_ERROR__UNKNOWN,              /*  0, Unknown Error, this should not occur. */
   255, //EXP_ERROR__NONE,                 /*  1, No Error is active. */
   0,   //EXP_ERROR__POWER_ON_RESET,       /*  2, Device is powering on after a normal power off event. */
   0,   //EXP_ERROR__WATCHDOG_RESET,       /*  3, Device is powering on after a watchdog reset event. */
   0,   //EXP_ERROR__LOW_POWER_RESET,      /*  4, Device is powering on after dip in board voltage (brown out) event. */
   1,   //EXP_ERROR__COMM_CAN1,            /*  5, No command messages destined for this device have been received on CAN1 in the past 20 seconds. */
   3,   //EXP_ERROR__COMM_CAN2,            /*  6, No messages destined for this device have been received on CAN2 in the past 20 seconds. This check is only valid if a board was initially detected on this network. */
   2,   //EXP_ERROR__DUPLICATE_ADDR,       /*  7, Another device on the network has the same identifier. */
   2,	//EXP_ERROR__INVALID_DEVICE_CAN1,  /*  8, An invalid or misconfigured device has been detected on CAN1. */
   2,	//EXP_ERROR__INVALID_DEVICE_CAN2,  /*  9, An invalid or misconfigured device has been detected on CAN2. */
   4,   //EXP_ERROR__BUS_OFFLINE_CAN1,     /* 10, The CAN 1 controller has experienced excessive bus errors and temporarily taken itself offline. */
   5,   //EXP_ERROR__BUS_OFFLINE_CAN2,     /* 11, The CAN 2 controller has experienced excessive bus errors and temporarily taken itself offline. */
   6,   //EXP_ERROR__DIP_SWITCH_CHANGED,   /* 12, A change in the Dip switch settings has been detected. */
};

static const uint8_t aucPriorityRiser[NUM_RIS_ERROR] =
{
   255, //RIS_ERROR__UNKNOWN,              /* 0, Unknown fault, this should not occur. */
   255, //RIS_ERROR__NONE,                 /* 1, No fault is active. */
   0, //RIS_ERROR__POWER_ON_RESET,       /* 2, Device is powering on after a normal power off event. */
   0, //RIS_ERROR__WATCHDOG_RESET,       /* 3, Device is powering on after a watchdog reset event. */
   0, //RIS_ERROR__LOW_POWER_RESET,      /* 4, Device is powering on after dip in board voltage (brown out) event. */
   1, //RIS_ERROR__COMM_CAN1,            /* 5, No command messages destined for this device have been received on CAN1 in the past 20 seconds. */
   3, //RIS_ERROR__COMM_CAN2,            /* 6, No messages destined for this device have been received on CAN2 in the past 20 seconds. This check is only valid if a board was initially detected on this network. */
   3, //RIS_ERROR__COMM_CAN5,            /* 7, No messages destined for this device have been received on CAN3 in the past 20 seconds. This check is only valid if a board was initially detected on this network. */
   2, //RIS_ERROR__DUPLICATE_ADDR,       /* 8, Another device on the network has the same identifier. */
   2, //RIS_ERROR__INVALID_DEVICE_CAN1,  /* 9, An invalid or misconfigured device has been detected on CAN1. */
   2, //RIS_ERROR__INVALID_DEVICE_CAN2,  /* 10, An invalid or misconfigured device has been detected on CAN2. */
   2, //RIS_ERROR__INVALID_DEVICE_CAN3,  /* 11, An invalid or misconfigured device has been detected on CAN3. */
   4, //RIS_ERROR__BUS_OFFLINE_CAN1,     /* 12, The CAN 1 controller has experienced excessive bus errors and temporarily taken itself offline. */
   5, //RIS_ERROR__BUS_OFFLINE_CAN2,     /* 13, The CAN 2 controller has experienced excessive bus errors and temporarily taken itself offline. */
   5, //RIS_ERROR__BUS_OFFLINE_CAN3,     /* 14, The CAN 3 controller has experienced excessive bus errors and temporarily taken itself offline. */
   6, //RIS_ERROR__DIP_SWITCH_CHANGED,   /* 15, A change in the Dip switch settings has been detected. */
};

static en_exp_error eActiveError = EXP_ERROR__NONE;
static en_ris_error eActiveErrorRiser = RIS_ERROR__NONE;
static uint16_t ucErrorTimeout_50ms = ERROR_MIN_TIMEOUT_50MS;
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_exp_error Error_GetActiveError(void)
 * @brief Access the current active Error
 * @param None
 * @return eActiveError, returns the current Error status
 */
en_exp_error Error_GetActiveError(void)
{
   return eActiveError;
}
/* @fn void Error_SetActiveError(en_exp_error eError)
 * @brief Set a Error
 * @param eError, the Error requested to be the board's active Error
 * @return None
 */
void Error_SetActiveError(en_exp_error eError)
{
   if( aucPriority[eError] < aucPriority[eActiveError] )
   {
      eActiveError = eError;
      ucErrorTimeout_50ms = ( eActiveError == EXP_ERROR__DUPLICATE_ADDR )
                          ? ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS
                          : ERROR_MIN_TIMEOUT_50MS;
   }
   else if( eActiveError == EXP_ERROR__NONE )
   {
      eActiveError = eError;
      ucErrorTimeout_50ms = ( eActiveError == EXP_ERROR__DUPLICATE_ADDR )
                          ? ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS
                          : ERROR_MIN_TIMEOUT_50MS;
   }
   else if( eActiveError == eError )
   {
      ucErrorTimeout_50ms = ( eActiveError == EXP_ERROR__DUPLICATE_ADDR )
                          ? ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS
                          : ERROR_MIN_TIMEOUT_50MS;
   }
}
/* @fn en_ris_error Error_GetActiveErrorRiser(void)
 * @brief Access the current active riser Error
 * @param None
 * @return eActiveErrorRiser, returns the current riser Error status
 */
en_ris_error Error_GetActiveErrorRiser(void)
{
   return eActiveErrorRiser;
}
/* @fn void Error_SetActiveErrorRiser(en_ris_error eError)
 * @brief Set a Error
 * @param eError, the Error requested to be the board's active riser Error
 * @return None
 */
void Error_SetActiveErrorRiser(en_ris_error eError)
{
   if( aucPriorityRiser[eError] < aucPriorityRiser[eActiveErrorRiser] )
   {
      eActiveErrorRiser = eError;
      ucErrorTimeout_50ms = ( eActiveErrorRiser == RIS_ERROR__DUPLICATE_ADDR )
                          ? ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS
                          : ERROR_MIN_TIMEOUT_50MS;
   }
   else if( eActiveErrorRiser == RIS_ERROR__NONE )
   {
      eActiveErrorRiser = eError;
      ucErrorTimeout_50ms = ( eActiveErrorRiser == RIS_ERROR__DUPLICATE_ADDR )
                          ? ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS
                          : ERROR_MIN_TIMEOUT_50MS;
   }
   else if( eActiveErrorRiser == eError )
   {
      ucErrorTimeout_50ms = ( eActiveErrorRiser == RIS_ERROR__DUPLICATE_ADDR )
                          ? ERROR_DUPLICATE_ADDRESS_MIN_TIMEOUT_50MS
                          : ERROR_MIN_TIMEOUT_50MS;
   }
}
/* @fn static void Error_UpdateErrorTimeout(void)
 * @brief Updates Error timeout counter and checks if the active Error is ready to be cleared
 * @param None
 * @return None
 */
static void Error_UpdateErrorTimeout(void)
{
   if(ucErrorTimeout_50ms)
   {
      ucErrorTimeout_50ms--;
   }
   else
   {
      eActiveError = EXP_ERROR__NONE;
	  eActiveErrorRiser = RIS_ERROR__NONE;
   }
}
/* @fn static void CheckForResetFault(void)
 * @brief Checks startup register to diagnose what caused the device to power off and asserts the corresponding fault
 * @param None
 * @return None
 */
static void CheckForResetFault(void)
{
	en_sys_node eNode = System_GetNodeID();
	/* 9.4.4 RCC reset status register (RCC_RSR) */
	uint32_t uiCSR = RCC->CSR;
	if( ( uiCSR & ( RCC_CSR_IWDGRSTF) ) != 0 ) // If watchdog reset
	{
		switch(eNode)
		{
		case SYS_NODE__MR_A7: Faults_SetFault(CFLT__MR_A7_WDT_RESET); break;
		case SYS_NODE__MR_B4: Faults_SetFault(CFLT__MR_B4_WDT_RESET); break;
		case SYS_NODE__CT_A7: Faults_SetFault(CFLT__CT_A7_WDT_RESET); break;
		case SYS_NODE__CT_B4: Faults_SetFault(CFLT__CT_B4_WDT_RESET); break;
		case SYS_NODE__COP_A4: Faults_SetFault(CFLT__COP_A4_WDT_RESET); break;
		default: break;
		}
	}
	else if( ( uiCSR & RCC_CSR_BORRSTF ) != 0 ) // If low power (brown out) reset
	{
		switch(eNode)
		{
		case SYS_NODE__MR_A7: Faults_SetFault(CFLT__MR_A7_BOD_RESET); break;
		case SYS_NODE__MR_B4: Faults_SetFault(CFLT__MR_B4_BOD_RESET); break;
		case SYS_NODE__CT_A7: Faults_SetFault(CFLT__CT_A7_BOD_RESET); break;
		case SYS_NODE__CT_B4: Faults_SetFault(CFLT__CT_B4_BOD_RESET); break;
		case SYS_NODE__COP_A4: Faults_SetFault(CFLT__COP_A4_BOD_RESET); break;
		default: break;
		}
	}
	else // Assume all other sources are regular power off events
	{
		switch(eNode)
		{
		case SYS_NODE__MR_A7: Faults_SetFault(CFLT__MR_A7_POWER_RESET); break;
		case SYS_NODE__MR_B4: Faults_SetFault(CFLT__MR_B4_POWER_RESET); break;
		case SYS_NODE__CT_A7: Faults_SetFault(CFLT__CT_A7_POWER_RESET); break;
		case SYS_NODE__CT_B4: Faults_SetFault(CFLT__CT_B4_POWER_RESET); break;
		case SYS_NODE__COP_A4: Faults_SetFault(CFLT__COP_A4_POWER_RESET); break;
		default: break;
		}
	}

	/* Reset status register by writing to the RMVF bit */
	RCC->CSR = uiCSR | RCC_CSR_RMVF;
}
/* @fn static void CheckForResetFaultOther(en_local_board_type eLocalBoardType)
 * @brief Checks startup register to diagnose what caused the device to power off and asserts the corresponding fault
 * @param None
 * @return None
 */
static void CheckForResetFaultOther(en_local_board_type eLocalBoardType)
{
	/* 9.4.4 RCC reset status register (RCC_RSR) */
	uint32_t uiCSR = RCC->CSR;
	if( ( uiCSR & ( RCC_CSR_IWDGRSTF) ) != 0 ) // If watchdog reset
	{
		switch(eLocalBoardType)
		{
			case BOARD_EXP_MASTER:
			case BOARD_EXP_SLAVE:
				Error_SetActiveError(EXP_ERROR__WATCHDOG_RESET); break;
			case BOARD_RISER: 
				Error_SetActiveErrorRiser(RIS_ERROR__WATCHDOG_RESET); break;
			case BOARD_COP:
			case BOARD_INVALID:
			default:
			break;
		}
	}
	else if( ( uiCSR & RCC_CSR_BORRSTF ) != 0 ) // If low power (brown out) reset
	{
		switch(eLocalBoardType)
		{
			case BOARD_EXP_MASTER:
			case BOARD_EXP_SLAVE:
				Error_SetActiveError(EXP_ERROR__LOW_POWER_RESET); break;
			case BOARD_RISER:
				Error_SetActiveErrorRiser(RIS_ERROR__LOW_POWER_RESET); break;
			case BOARD_COP:
			case BOARD_INVALID:
			default:
			break;
		}
	}
	else // Assume all other sources are regular power off events
	{
		switch(eLocalBoardType)
		{
			case BOARD_EXP_MASTER:
			case BOARD_EXP_SLAVE:
				Error_SetActiveError(EXP_ERROR__POWER_ON_RESET); break;
			case BOARD_RISER:
				Error_SetActiveErrorRiser(RIS_ERROR__POWER_ON_RESET); break;
			case BOARD_COP:
			case BOARD_INVALID:
			default:
			break;
		}
	}

	/* Reset status register by writing to the RMVF bit */
	RCC->CSR = uiCSR | RCC_CSR_RMVF;
}

/* @fn void FaultAlarm_Init(void)
 * @brief Initializes fault alarm task and configures the task delay based on the local board type
 * @param None
 * @return None
 */
void FaultAlarm_Init(void)
{
    // Get the local board type
    en_local_board_type eLocalBoardType = Main_GetLocalBoardType();

    // Switch based on the local board type to initialize fault alarms and configure delay
    switch(eLocalBoardType)
    {
        case BOARD_COP:
            // For COP board, check for reset faults and set the task delay for alarms
            CheckForResetFault();
            gFaultAlarm_Task_Delay = ALARM_TASK_PERIODIC_DELAY_MS;
            break;

        case BOARD_EXP_MASTER:
        case BOARD_EXP_SLAVE:
        case BOARD_RISER:
        case BOARD_INVALID:
        default:
            // For other boards, check for reset faults and set a different task delay
            CheckForResetFaultOther(eLocalBoardType);
            gFaultAlarm_Task_Delay = ALARM_TASK_PERIODIC_DELAY_MS;
            break;
    }
}


/* @fn void vFaultAlarm_Task(void *pvParams)
 * @brief Monitors and updates alarms and faults for different board types
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vFaultAlarm_Task(void *pvParams)
{
   // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1){
        // Switch based on the local board type to handle different alarm/fault actions
        switch(Main_GetLocalBoardType())
        {
            case BOARD_COP:
                /* Added Alarm and Fault handling into the same task for COP board */
                // Update latched fault every periodic delay (for COP board)
                Faults_UpdateLatchedFault(FAULT_TASK_PERIODIC_DELAY_MS);
                // Update latched alarm every periodic delay (for COP board)
                Alarms_UpdateLatchedAlarm(ALARM_TASK_PERIODIC_DELAY_MS);
                break;
            case BOARD_EXP_MASTER:
            case BOARD_EXP_SLAVE:
                // Update error timeout for EXP Master and Slave boards
                Error_UpdateErrorTimeout();
                break;
            case BOARD_RISER:
                // Update error timeout for RISER board
                Error_UpdateErrorTimeout();
                break;
            case BOARD_INVALID:
            default:
                // No action for invalid or default board type
                break;
        }
           // Delay using the configured delay time for fault alarm task
		vTaskDelay(gFaultAlarm_Task_Delay);

    }
}
