/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_CAN1.c
* @version 		  : 1.0.0
* @brief Subroutine handling CAN1 network
* @details Subroutine handling CAN1 network
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_CAN1.h"
#include "sr_position.h"
#include "app_can.h"
#include "car_alarms_def.h"
#include "param_def.h"
#include "position_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (500)
#define SUBROUTINE_RUN_INTERVAL_1MS                (3)

// How long the bus can go without receiving a message before an offline flag is set
#define CAN_OFFLINE_TIMEOUT_1MS                    (1000)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static uint16_t uwOfflineTimer_1ms;

static volatile uint32_t uiPositionRaw_05mm;
static volatile uint32_t uiErrorRaw;
static volatile uint16_t uwSpeedRaw_1mm_s;

/* LES02 Channel B Reset Command */
static const st_CAN_msg_8B stResetMsg_B_LES02 =
{
		.uiID = 0x7F0,
		.ucDLC = 8,
		.bExtendedID = 0,
		.bCAN_FD = 0,
		.unData.auc8[0] = 0x00,
		.unData.auc8[1] = 0x0A,
		.unData.auc8[2] = 0x01,
		.unData.auc8[3] = 0xC2,
		.unData.auc8[4] = 0xC9,
		.unData.auc8[5] = 0x05,
		.unData.auc8[6] = 0x00,
		.unData.auc8[7] = 0x17
};
/******************************************************************************
* Function Definitions
*******************************************************************************/
uint32_t getPositionRaw(void)
{
	return uiPositionRaw_05mm;
}
uint32_t getErrorRaw(void)
{
	return uiErrorRaw;
}
uint16_t getSpeedRaw(void)
{
	return uwSpeedRaw_1mm_s;
}

/* @fn static void CAN1_SendDatagrams(void)
 * @brief Checks if a datagram is due to send. if so, loads the datagram to transmit buffer
 * @param None
 * @return None
 */
static void CAN1_SendDatagrams(void)
{
	if( (Param_GetState() != PARAM_STATE__STARTUP )
	 && !CAN1_CheckIfCommLoss() )
	{
		uint8_t ucPositionType = Param_ReadValue_8Bit(CPARAM8__PositionSystemType);
		if( ucPositionType == POSITION_SYSTEM__LES02 )
		{
			if( Position_GetSelectorReset() == ACTIVE )
			{
				st_CAN_msg_8B stTxMsg = stResetMsg_B_LES02;
			    if(CAN1_LoadToRB(&stTxMsg) == PASS)
			    {

			    }
			    Position_SetSelectorReset(INACTIVE);
			}
		}
		else
		{
			//AS todo add other landing systems
		}
	}
}
/* @fn static void CAN1_SendDatagrams(void)
 * @brief Checks if a datagram is due to send. if so, loads the datagram to transmit buffer
 * @param None
 * @return None
 */
static void CAN1_UnloadDatagrams(void)
{
   st_CAN_msg stRxMsg;
   for(uint8_t i = 0; i < CAN_RX_RING_BUFFER_SIZE; i++)
   {
      if( CAN1_UnloadFromRB(&stRxMsg) == PASS )
      {
         /* Process message */
         if( stRxMsg.uiID == 0xB )
         {
        	 uiErrorRaw = ( stRxMsg.unData.auc8[0] << 16 ) | ( stRxMsg.unData.auc8[1] << 8 ) | stRxMsg.unData.auc8[2];
        	 uwSpeedRaw_1mm_s = ( stRxMsg.unData.auc8[3] << 8 ) | stRxMsg.unData.auc8[4];
        	 uiPositionRaw_05mm = stRxMsg.unData.auc8[5] << 16 | stRxMsg.unData.auc8[6] << 8 | stRxMsg.unData.auc8[7];

            CAN1_IncrementStatusCounter(CAN_COUNTER__RX_PACKET);
         }
         uwOfflineTimer_1ms = 0;
      }
      else
      {
         break;
      }
   }
}
/* @fn uint8_t CAN1_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t CAN1_CheckIfCommLoss(void)
{
   return ( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS );
}
/* @fn static void CAN1_UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void CAN1_UpdateOfflineTimer(void)
{
   if( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS )
   {
      // TODO set alarm
   }
   else
   {
      uwOfflineTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
   }
}

/* @fn void vCAN1_Task(void *pvParameters)
 * @brief Handles CAN1 communication, updates bus error counters, and manages errors based on board type
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCAN1_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
	   CAN1_UnloadDatagrams();

	   CAN1_SendDatagrams();

	   CAN1_UpdateOfflineTimer();

	   CAN1_UpdateBusErrorCounter();

	   if( CAN1_CheckForBusOffline() )
	   {
		  Alarms_SetAlarm(CALM__BUS_RESET_CT_N4);
	   }
	   vTaskDelay(CAN1_TASK_DELAY);
	}
}
