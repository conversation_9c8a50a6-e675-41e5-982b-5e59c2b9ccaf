/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_LED.h
* @version 		  : 1.0.0
* @brief          : LED subroutine
* @details		  : LED subroutine
********************************************************************************/
#ifndef _SR_LED_H_
#define _SR_LED_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/


/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variables
********************************************************************************/


/*******************************************************************************
* Function Prototypes
********************************************************************************/
void vLED_Update(void);

#endif /* _SR_LED_H_ */
