/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_outputs.c
* @version 		  : 1.0.0
* @brief This subroutine updates local board outputs
* @details This subroutine updates local board outputs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_local_outputs.h"
#include "sr_local_inputs.h"
#include "main.h"
#include "app_gpio.h"
#include "dg_group_mr_b4.h"
#include "dg_car_mr_a7.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

static uint8_t auiOutputBitmap[BITMAP8_ARRAY_SIZE(NUM_LOCAL_OUTPUTS)];
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn static void UpdateOutputPins(void)
 * @brief Takes the local output commands and updates the corresponding pin states
 * @param None
 * @return None
 */
static void UpdateOutputPins(void)
{
   /* Extract programmable terminal output commands and update */
	uint32_t uiCommandBitmap = 0;
	switch(Main_GetLocalBoardType())
	{
		case BOARD_COP:
			uiCommandBitmap = GetLocalOutputs_COP_BOARD_MRA();
			break;
		case BOARD_EXP_MASTER:
		case BOARD_EXP_SLAVE:
			uiCommandBitmap = GetLocalOutputs_EXP_BOARD_MRA(Main_GetLocalExpansionID());
			break;
		case BOARD_RISER:
			uiCommandBitmap = GetLocalOutputs_RIS_BOARD_MRB(Main_GetLocalRiserID());
			break;
		case BOARD_INVALID:
		default:
		break;
	}

   /* Update hardware pin states based on commands */
   for(en_local_outputs eOutput = enLOCAL_OUT__OUTPUT_01; eOutput <= enLOCAL_OUT__OUTPUT_16; eOutput++)
   {
      uint8_t ucBitIndex = eOutput - enLOCAL_OUT__OUTPUT_01;
      uint8_t bActive = GET_BIT_BY_INDEX(uiCommandBitmap, ucBitIndex);
      System_SetBitByIndex(&auiOutputBitmap[0], ucBitIndex, bActive);

      GPIO_WriteOutput(eOutput, bActive);
   }
}

uint8_t LocalOutputs_GetOutputBitmap(uint8_t ucArrayIndex)
{
	return auiOutputBitmap[ucArrayIndex];
}

/* @fn static void TestIO(void)
 * @brief Each input n, will activate output n, where n is numbers 1 thru 16.
 * @param None
 * @return None
 */
static void TestIO(void)
{
	en_local_inputs eInput = enLOCAL_IN__DC_IN_01;
	for(en_local_outputs eOutput = enLOCAL_OUT__OUTPUT_01; eOutput <= enLOCAL_OUT__OUTPUT_16; eOutput++)
	{
//		System_SetBitByIndex(&auiOutputBitmap[0], ucBitIndex, bActive);
		GPIO_WriteOutput(eOutput, LocalInputs_GetInputValue(eInput++));
	}
}

/* @fn void vLocalOutputs_Task(void *pvParams)
 * @brief Handles local output tasks by updating output pins or running test IO based on the board type
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vLocalOutputs_Task(void *pvParams)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1)
    {
        // Check the local board type and either run test IO or update output pins
        if(Main_GetLocalBoardType() == BOARD_TEST)
        {
            // Run Test IO for TEST board
            TestIO();
        }
        else
        {
            // Update output pins for other boards
            UpdateOutputPins();
        }

		    vTaskDelay(LOCAL_OUTPUTS_TASK_PERIODIC_DELAY_MS); // Delay using the configured period
    }
}
