/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    spi.h
  * @brief   This file contains all the function prototypes for
  *          the spi.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __SPI_H__
#define __SPI_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdint.h>
#include "stm32g4xx_hal.h"
#include "ring_buffer.h"
#include "network.h"
#include "system.h"
#include "spi_def.h"
/* USER CODE END Includes */

extern SPI_HandleTypeDef hspi1;
extern SPI_HandleTypeDef hspi2;

extern SPI_HandleTypeDef hspi3;

/* USER CODE BEGIN Private defines */
#define SPI_TX_RING_BUFFER_SIZE_WORD                 (512)
#define SPI_RX_RING_BUFFER_SIZE_WORD                 (1024)

/* USER CODE END Private defines */

void MX_SPI1_Init(void);
void MX_SPI2_Init(void);
void MX_SPI3_Init(void);

/* USER CODE BEGIN Prototypes */
uint16_t SPI_TX_GetCounter(en_spi_counter eCounter);
uint16_t SPI_RX_GetCounter(en_spi_counter eCounter);
void SPI_AB_TX_IncrementCounter(en_spi_counter eCounter);
void SPI_AB_RX_IncrementCounter(en_spi_counter eCounter);

en_pass_fail SPI_AB_LoadToTxRB(uint16_t *puwData, uint16_t uwNumBytes);
en_pass_fail SPI_AB_UnloadFromRxRB(uint16_t *puwData);
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __SPI_H__ */

