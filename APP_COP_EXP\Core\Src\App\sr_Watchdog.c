/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_Watchdog.c
 * @version 		  : 1.0.0
 * @brief          : Watchdog subroutine
 * @details		  : Watchdog subroutine
 ********************************************************************************/

/*******************************************************************************
 * Includes
 ********************************************************************************/
#include "sr_Watchdog.h"
#include "app_gpio.h"
#include "app_watchdog.h"
#include "bootloader_def.h"
#include "car_input_def.h"
#include "motion_def.h"
#include "car_faults_def.h"
#include "stm32g4xx_hal.h"
#include "bootloader_cfg.h"
#include "no_init.h"
#include "system.h"

#include "dg_car_mr_a7.h"
#include "dg_car_ct_a7.h"
#include "dg_car_cop_a4.h"
/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/

/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/

#define NEED_TO_RESET_DEBOUNCE_LIMIT__MS      (3000)

/*******************************************************************************
 * Preprocessor Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variable Definitions
 ********************************************************************************/
/*******************************************************************************
 * Variable Definitions
 ********************************************************************************/

/*******************************************************************************
 * Function Definitions
 ********************************************************************************/
/**
 * @fn Init_Watchdog(void)
 * @brief Initializes Watchodg subroutine first run delay, run interval, and variables
 * @param None
 * @return None
 */
void Init_Watchdog(void) {
   if(!((GPIO_ReadDIPBank() >> 7) & 0x01)) {
      Watchdog_Init();
   }
}
/* @fn void vWatchdog_update(void)
 * @brief Checks the bootloader hold signal and manages the watchdog timer accordingly.
 *        If the bootloader hold signal is detected, it triggers a reset and enters the bootloader.
 *        Otherwise, it feeds the watchdog to prevent a system reset.
 * @param None
 * @return None
 */
void vWatchdog_update(void)
{
   if(((Bootloader_GetBootloaderHoldSignal_MR_A7()  == BOOTLOADER_HOLD_MAGIC) ||
       (Bootloader_GetBootloaderHoldSignal_CT_A7()  == BOOTLOADER_HOLD_MAGIC)) &&
       (System_GetNodeID() == SYS_NODE__COP_A4))
   {
     // Set bootloader magic
     boot_set_magic(BOOTLOADER_HOLD_MAGIC);
     // Set new watchdog timeout
     Watchdog_SetTimeout(APP_SLAVE_NODE_WDG_TIMEOUT);

     // Starve watchdog timer
     for(;;);
   }

   Watchdog_Feed();
}
