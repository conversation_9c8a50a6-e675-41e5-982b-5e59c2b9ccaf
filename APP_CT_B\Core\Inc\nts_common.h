#ifndef INC_NTS_COMMON_H_
#define INC_NTS_COMMON_H_

#include "system.h"
#include "nts_lookup.h"

#define NTS_CAST(self) ((nts_common_t* const)self)

typedef enum
{
  NTS_STATE__CHECKING,
  NTS_STATE__TRIPPED,

  NTS_STATE__USER
}nts_states_common_t;

typedef struct
{
  uint8_t eState;
  uint8_t bTripped_UNTS; /* Latching flag inidicating that NTS has tripped */
  uint8_t bTripped_DNTS; /* Latching flag inidicating that NTS has tripped */
}nts_common_t;

en_active_inactive NTS_Common_GetTrippedFlag(nts_common_t * const self);

#endif /* INC_NTS_COMMON_H_ */
