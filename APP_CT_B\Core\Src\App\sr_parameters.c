/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_parameters.c
* @version 		  : 1.0.0
* @brief Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
* @details Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "sr_parameters.h"
#include "dg_car_mr_a7.h"
#include "operation_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/
/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (10)

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn void Parameters_Init(void )
 * @brief Init function for Parameters Init
 * @param None
 * @return None
 */
void Parameters_Init(void)
{

}


/* @fn void vParameters_Task(void *pvParameters)
 * @brie  Parameters task called from the RTOS scheduler
 * @param None
 * @return None
 */
void vParameters_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		Param_Run();
		if( Param_GetState() != PARAM_STATE__STARTUP )
		{
			Floor_UpdateFloorPositions();
			if( Position_GetState() == POSITION_STATE__KNOWN )
			{
				Operation_SetCurrentFloor(Floor_GetFloorIndex(Position_GetPosition_05mm()));
				Operation_SetDestinationFloor(Floor_GetFloorIndex(Motion_GetDestination_05mm_MRA()));
			}
		}
		if( ( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) )
		 && ( ( Param_GetState() == PARAM_STATE__STARTUP )
		   || ( Param_GetBlockToUpdate() != NUM_PARAM_BLOCKS ) ) )
		{
		   Faults_SetFault(CFLT__PARAMETER_SYNC_MR_A4+System_GetNodeID());
		}
		vTaskDelay(PARAMETERS_TASK_DELAY);
	}
}
