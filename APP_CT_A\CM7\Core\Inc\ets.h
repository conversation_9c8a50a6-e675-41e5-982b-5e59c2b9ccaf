#ifndef INC_ETS_H_
#define INC_ETS_H_

#include "system.h"
#include "param_def.h"

typedef enum
{
  CHECK_UETS,
  CHECK_DETS
}ets_check_t;

typedef enum
{
  ETS_STATE_IDLE,
  ETS_STATE_CHECK_DETS,
  ETS_STATE_CHECK_UETS,
  ETS_STATE_TRIPPED
}ets_state_t;

typedef struct
{
  uint32_t position;
  int16_t speed;
}ets_point_t;

typedef struct
{
  ets_state_t state;
  uint8_t tripped_UETS;
  uint8_t tripped_DETS;

  ets_point_t UETS;
  ets_point_t DETS;
}ets_t;


void ETS_Init(ets_t * const self);
void ETS_Run(ets_t * const self);

void ETS_LoadPoint(ets_point_t * const self,
    en_car_param_24bit pos_param_base,
    en_car_param_16bit speed_param_base);
en_active_inactive ETS_CheckETSPoint(ets_point_t * const self);

#endif
