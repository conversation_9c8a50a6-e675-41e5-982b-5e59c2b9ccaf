/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file sr_fixture_net.h
* @version 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/
#ifndef _SR_FIXTURE_NET_H_
#define _SR_FIXTURE_NET_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "app.h"
#include "system.h"
#include "door_def.h"
#include "hall_call_def.h"
#include "dg_car_mr_b4.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/
typedef enum
{
   FIXTURE_NET_STATE__INIT,      /* Waiting for parameter initialization */
   FIXTURE_NET_STATE__RUNNING_MAD,   /* Initialized and sending messages */

   NUM_FIXTURE_NET_STATES
}en_fixture_net_state;
/******************************************************************************
* Global Variables
*******************************************************************************/
//extern st_subroutine gstSR_Fixture_Net;
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_fixture_net_state Fixture_Net_GetState(void);
void vFixtureNet_Task(void *pvParameters);
#endif /* _SR_FIXTURE_NET_H_ */
