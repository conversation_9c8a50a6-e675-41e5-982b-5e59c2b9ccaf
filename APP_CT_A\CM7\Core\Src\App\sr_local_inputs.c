/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_local_inputs.c
* @version 		  : 1.0.0
* @brief This subroutine updates local board inputs
* @details This subroutine updates local board inputs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_local_inputs.h"
#include "system.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_RUN_INTERVAL_1MS                               (5)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

#define LOCAL_INPUT_DEBOUNCE_TIME__1MS                            (50)
/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
/* Local input states in bitmap form, where 1 a a given bit position
 * means the corresponding input is active. THis is a debounced form of the inputs*/
static uint8_t aucInputBitmap[BITMAP8_ARRAY_SIZE(NUM_LOCAL_INPUTS)];
static uint8_t aucDebounceCounter_1ms[NUM_LOCAL_INPUTS];
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn uint32_t LocalInputs_GetInputBitmap(void)
 * @brief Returns the status of inputs in bitmap form
 * @param None
 * @return Returns the status of inputs in bitmap form
 */
uint32_t LocalInputs_GetInputBitmap(uint8_t ucArrayIndex)
{
   return aucInputBitmap[ucArrayIndex];
}
/* @fn uint8_t *LocalInputs_GetInputBitmapPointer(void)
 * @brief Returns pointer to bitmap of local inputs
 */
uint8_t *LocalInputs_GetInputBitmapPointer(void)
{
   return aucInputBitmap;
}
/* @fn uint8_t LocalInputs_GetInputValue(en_local_inputs eInput)
 * @brief Returns the status of a particular input
 * @param eInput, the local input index
 * @return Returns 1 if the input is active, 0 otherwise
 */
uint8_t LocalInputs_GetInputValue(en_local_inputs eInput)
{
   return System_GetBitByIndex(&aucInputBitmap[0], eInput);
}

/* @fn static void LocalInputs_UpdateInputPins(void)
 * @brief Takes the local input commands and updates the corresponding pin states
 * @param None
 * @return None
 */
static void LocalInputs_UpdateDebouncedInputs(void)
{
   /* Checks input pin states and updates the debounced input status flags for each input */
   for(en_local_inputs eInput = 0; eInput <= enLOCAL_IN__DC_IN_24; eInput++)
   {
      uint8_t bLastActive = System_GetBitByIndex(&aucInputBitmap[0], eInput);
      uint8_t bActive = GPIO_ReadInput(eInput);
      if(bLastActive != bActive)
      {
         if(aucDebounceCounter_1ms[eInput] < LOCAL_INPUT_DEBOUNCE_TIME__1MS)
         {
            aucDebounceCounter_1ms[eInput] += SUBROUTINE_RUN_INTERVAL_1MS;
         }
         else
         {
            System_SetBitByIndex(&aucInputBitmap[0], eInput, bActive);
            aucDebounceCounter_1ms[eInput] = 0;
         }
      }
      else
      {
         aucDebounceCounter_1ms[eInput] = 0;
      }
   }
}

/* @fn void vLocalInput_Task(void *pvParameters)
 * @brief Run function vLocalInput_Task that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
void vLocalInput_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		 LocalInputs_UpdateDebouncedInputs();

		 vTaskDelay(LOCAL_INPUT_TASK_DELAY);
	}
}
