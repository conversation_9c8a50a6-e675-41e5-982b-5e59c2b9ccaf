/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_gpio.h
* @version 		  : 1.0.0
* @brief          : GPIO accessing and initialization
* @details		  : GPIO accessing and initialization
********************************************************************************/
#ifndef _APP_GPIO_H_
#define _APP_GPIO_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
#include "stm32h7xx_hal.h"
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/

/*******************************************************************************
* Configuration Constants
********************************************************************************/
/* Note: falling edge interrupt setting is actually rising edge due to input inversion */
//				(NAME,					PORT,	PIN,			MODE,			PULL,		SPEED, 			INVERT)
#define LOCAL_INPUT_TABLE \
	LOCAL_GP_INPUT(ESCAPE_HATCH		, GPIOE, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(CAR_TOP_STOP		, GPIOE, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(CT_INSPECTION	, GPIOE, GPIO_PIN_4 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(GATESWITCH_F		, GPIOE, GPIO_PIN_5 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(GATESWITCH_R		, GPIOE, GPIO_PIN_6 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(UNUSED_PI9		, GPIOI, GPIO_PIN_9 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(UNUSED_PI10		, GPIOI, GPIO_PIN_10 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(UNUSED_PI11		, GPIOI, GPIO_PIN_11 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DIP_SW_01		, GPIOH, GPIO_PIN_9 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_02		, GPIOH, GPIO_PIN_10 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_03		, GPIOH, GPIO_PIN_11 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_04		, GPIOH, GPIO_PIN_12 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_05		, GPIOH, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_06		, GPIOH, GPIO_PIN_7 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_07		, GPIOH, GPIO_PIN_8 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(DIP_SW_08		, GPIOG, GPIO_PIN_0 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(IN_CAR_STOP		, GPIOH, GPIO_PIN_5 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(FIRE_STOP_SWITCH	, GPIOA, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(HA_INSPECTION	, GPIOA, GPIO_PIN_4 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(IC_INSPECTION	, GPIOA, GPIO_PIN_5 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_09			, GPIOF, GPIO_PIN_0 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_10			, GPIOF, GPIO_PIN_1 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_11			, GPIOF, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_12			, GPIOF, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_13			, GPIOF, GPIO_PIN_4 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_14			, GPIOF, GPIO_PIN_5 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_15			, GPIOF, GPIO_PIN_6 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_16			, GPIOF, GPIO_PIN_7 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_17			, GPIOF, GPIO_PIN_8 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_18			, GPIOF, GPIO_PIN_9 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_19			, GPIOF, GPIO_PIN_10 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_20			, GPIOC, GPIO_PIN_0 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_21			, GPIOC, GPIO_PIN_1 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_22			, GPIOC, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_23			, GPIOC, GPIO_PIN_3 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(DC_IN_24			, GPIOH, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(USB_OVERCURRENT	, GPIOD, GPIO_PIN_10 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_INPUT(MCU_ID_0			, GPIOA, GPIO_PIN_0 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(MCU_ID_1			, GPIOA, GPIO_PIN_1 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_INPUT(MCU_ID_2			, GPIOA, GPIO_PIN_2 	, GPIO_MODE_INPUT , GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0)


//				(NAME,					PORT,	PIN,			MODE,			PULL,			SPEED, 			INVERT)
#define LOCAL_OUTPUT_TABLE \
	LOCAL_GP_OUTPUT(OUTPUT_01		, GPIOG, GPIO_PIN_15 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_02		, GPIOD, GPIO_PIN_6 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_03		, GPIOD, GPIO_PIN_5 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_04		, GPIOD, GPIO_PIN_4 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_05		, GPIOD, GPIO_PIN_3 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_06		, GPIOD, GPIO_PIN_2 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_07		, GPIOB, GPIO_PIN_0 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_08		, GPIOI, GPIO_PIN_3 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_09		, GPIOI, GPIO_PIN_2 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_10		, GPIOI, GPIO_PIN_1 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_11		, GPIOI, GPIO_PIN_0 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(OUTPUT_12		, GPIOH, GPIO_PIN_15 	, GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0) \
	LOCAL_GP_OUTPUT(LED_HB			, GPIOD, GPIO_PIN_8 	, GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_FLT			, GPIOD, GPIO_PIN_9 	, GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_ALM			, GPIOD, GPIO_PIN_14 	, GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(FRAM_WP			, GPIOH, GPIO_PIN_4 	, GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(USB_PSE			, GPIOD, GPIO_PIN_11 	, GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 0)
/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/
typedef struct
{
	GPIO_TypeDef *pGPIO_Port;
	uint32_t uiMode;
	uint16_t uwPin;
	uint8_t ucPull;
	uint8_t ucSpeed;
	uint8_t bInvert;
} st_gpio_def;

typedef enum
{
	#define LOCAL_GP_INPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) enLOCAL_IN__##NAME,
	LOCAL_INPUT_TABLE
	#undef LOCAL_GP_INPUT
	NUM_LOCAL_INPUTS
} en_local_inputs;

typedef enum
{
#define LOCAL_GP_OUTPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) enLOCAL_OUT__##NAME,
	LOCAL_OUTPUT_TABLE
#undef LOCAL_GP_OUTPUT
	NUM_LOCAL_OUTPUTS
} en_local_outputs;

/*******************************************************************************
* Global Variables
********************************************************************************/


/*******************************************************************************
* Function Prototypes
********************************************************************************/
void GPIO_Init(void);
uint8_t GPIO_ReadInput(en_local_inputs eInput);
uint8_t GPIO_ReadOutput(en_local_outputs eOutput);
uint8_t GPIO_ReadOutputCommand(en_local_outputs eOutput);
uint8_t *GPIO_GetOutputCommandPointer(void);
void GPIO_WriteOutput(en_local_outputs eOutput, uint8_t bActive);
uint8_t GPIO_ReadDIPBank(void);


#endif /* _APP_GPIO_H_ */
