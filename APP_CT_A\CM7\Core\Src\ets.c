#include "ets.h"

#include "param_def.h"
#include "position_def.h"
#include "car_faults_def.h"
#include "operation_def.h"

#include "ulog.h"

void ETS_Init(ets_t * const self)
{
  ETS_LoadPoint(&self->UETS,
      CPARAM24__Traction_UETS_Pos,
      CPARAM16__Traction_UETS_Speed);
  ETS_LoadPoint(&self->DETS,
      CPARAM24__Traction_DETS_Pos,
      CPARAM16__Traction_DETS_Speed);
}

void ETS_LoadPoint(ets_point_t * const self,
    en_car_param_24bit pos_param_base,
    en_car_param_16bit speed_param_base)
{
  self->position = Param_ReadValue_24Bit(pos_param_base);
  self->speed    = Param_ReadValue_16Bit(speed_param_base);
}

en_active_inactive ETS_CheckETSPoint(ets_point_t * const self)
{
  en_active_inactive status = INACTIVE;
  if((self->position == 0) ||
     (self->speed    == 0))
  {
    status = ACTIVE;
  }

  return status;
}

en_active_inactive ETS_UETSCheck(ets_point_t * const UETS,
    uint32_t uiPosition_05mm,
    int16_t uwSpeed_fpm)
{
  en_active_inactive bETS = INACTIVE;

  uint32_t uiNextPos_ETS_05mm = UETS->position;
  int16_t uwNextSpeed_fpm     = UETS->speed;

  if(uiPosition_05mm >= uiNextPos_ETS_05mm)
  {
    if(uwSpeed_fpm >= (uwNextSpeed_fpm + Param_ReadValue_8Bit(CPARAM8__Traction_ETS_Tolerance_fpm)))
    {
      ULOG_DEBUG("UETS triggered at point (%i, %i)", uiPosition_05mm, uwSpeed_fpm);
      bETS = ACTIVE;
    }
  }

  return bETS;
}

en_active_inactive ETS_DETSCheck(ets_point_t * const UETS,
    uint32_t uiPosition_05mm,
    int16_t uwSpeed_fpm)
{
  en_active_inactive bETS = INACTIVE;

  uint32_t uiNextPos_ETS_05mm = UETS->position;
  int16_t uwNextSpeed_fpm     = UETS->speed;

  if(uiPosition_05mm <= uiNextPos_ETS_05mm)
  {
    if(uwSpeed_fpm <= (uwNextSpeed_fpm - Param_ReadValue_8Bit(CPARAM8__Traction_ETS_Tolerance_fpm)))
    {
      ULOG_DEBUG("DETS triggered at point (%i, %i)", uiPosition_05mm, uwSpeed_fpm);
      bETS = ACTIVE;
    }
  }

  return bETS;
}

void ETS_Run(ets_t * const self)
{
  uint32_t uiPosition_05mm = Position_GetPosition_05mm();
  int16_t uwSpeed_fpm      = Position_GetSpeed_fpm();

  switch(self->state)
  {
  case ETS_STATE_IDLE:
    if(uwSpeed_fpm > 0)
    {
      self->state = ETS_STATE_CHECK_UETS;
    }
    else if(uwSpeed_fpm < 0)
    {
      self->state = ETS_STATE_CHECK_DETS;
    }
    break;
  case ETS_STATE_CHECK_UETS:
  {
    en_active_inactive bUETS = ETS_UETSCheck(&self->UETS,
        uiPosition_05mm,
        uwSpeed_fpm);
    if(bUETS == ACTIVE)
    {
      self->tripped_UETS = ACTIVE;
      self->state        = ETS_STATE_TRIPPED;
    }
    else if(uwSpeed_fpm == 0)
    {
      self->state = ETS_STATE_IDLE;
    }
  }
    break;
  case ETS_STATE_CHECK_DETS:
  {
    en_active_inactive bDETS = ETS_DETSCheck(&self->DETS,
        uiPosition_05mm,
        uwSpeed_fpm);
    if(bDETS == ACTIVE)
    {
      self->tripped_DETS = ACTIVE;
      self->state        = ETS_STATE_TRIPPED;
    }
    else if(uwSpeed_fpm == 0)
    {
      self->state = ETS_STATE_IDLE;
    }
  }
    break;
  case ETS_STATE_TRIPPED:
    if(uwSpeed_fpm == 0)
    {
      self->state        = ETS_STATE_IDLE;
      self->tripped_UETS = INACTIVE;
      self->tripped_DETS = INACTIVE;
    }
    else if((self->tripped_UETS               == ACTIVE) &&
            (Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN))
    {
      Faults_SetFault(CFLT__MCU_ETS_UP);
    }
    else if((self->tripped_DETS               == ACTIVE) &&
            (Operation_GetSemiAutoOperation() != SEMIAUTO_OP__LEARN))
    {
      Faults_SetFault(CFLT__MCU_ETS_DOWN);
    }
    break;
  default:
    self->state = ETS_STATE_IDLE;
    break;
  }
}


