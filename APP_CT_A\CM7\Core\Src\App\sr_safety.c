/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_safety.c
* @version 		  : 1.0.0
* @brief This subroutine updates the SF relay and performs safety string checks
* @details This subroutine updates the SF relay and performs safety string checks
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_safety.h"
#include "car_input_def.h"
#include "operation_def.h"
#include "car_faults_def.h"
#include "position_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (1000)
/* The max speed allowed before tripping TSRD */
/* A17.1 ******** Terminal Speed-Reducing Devices */
#define SAFETY_TSRD_MAX_SPEED_FPM		           (50)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef struct
{
	en_active_inactive bTSRDTripped; /* TSRD flag */
	en_active_inactive bLastResetBtn; /* TSRD flag */

}st_safety_control;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_safety_control stControlSafety;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @brief Checks if the car has tripped TSRD overspeed */
static void Safety_Check_TSRD(void)
{
   uint32_t uiPos_TSRD_05mm = Param_ReadValue_24Bit(CPARAM24__TSRD_Position_05mm);
   if( Operation_GetManualOperation() != MANUAL_OP__OFF && Operation_GetManualOperation() != MANUAL_OP__UNKNOWN )
   {
      uiPos_TSRD_05mm = Param_ReadValue_24Bit(CPARAM24__TSRD_Position_Insp_05mm);
   }
   uint8_t bResetBtn = CarInput_GetInputValue(CIN__FAULT_RST);

   if( Position_GetState() == POSITION_STATE__KNOWN )
   {
	   if( ( Motion_GetMotionDirection() == MOTION_DIR__UP ) )
	   {
		   if( ( Position_GetPosition_05mm() >= uiPos_TSRD_05mm )
			&& ( Position_GetSpeed_fpm() >= SAFETY_TSRD_MAX_SPEED_FPM ) )
		   {
			   stControlSafety.bTSRDTripped = ACTIVE;
			   Faults_SetFault(CFLT__TSRD); /* Immediately set fault to trigger emergency stop */
		   }
	   }
   }

   if( stControlSafety.bTSRDTripped == ACTIVE )
   {
	   Faults_SetFault(CFLT__TSRD); /* Keep asserting the fault */
	   if( !stControlSafety.bLastResetBtn && bResetBtn )
	   {
		   stControlSafety.bTSRDTripped = INACTIVE;
	   }
   }
}

/* @fn void vSafety_Task(void *pvParameters)
 * @brief Execute the vSafety_Task to update terminal limits
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vSafety_Task(void *pvParameters)
{
	vTaskDelay(SUBROUTINE_FIRST_RUN_DELAY_1MS);

	TickType_t uwTaskDelay_1ms = SAFETY_TASK_DELAY;
	while(1)
	{
        if( System_GetControlMode() == CONTROL_MODE__HYDRO )
        {
            if( Position_GetState() == POSITION_STATE__KNOWN )
            {
                if( Operation_GetOverrideTerminalLimits() == INACTIVE )
                {
                	Safety_Check_TSRD();
                }
            }

            uwTaskDelay_1ms = SAFETY_TASK_DELAY;
        }
        else if( System_GetControlMode() == CONTROL_MODE__TRACTION )
        {
            uwTaskDelay_1ms = portMAX_DELAY;
        }
        /* Delay task execution for a specified amount of time  */
        vTaskDelay(uwTaskDelay_1ms);


//		if( Position_GetState() == POSITION_STATE__KNOWN )
//		{
//			if( Operation_GetOverrideTerminalLimits() == INACTIVE )
//			{
//				Safety_Check_TSRD();
//			}
//		}
//		vTaskDelay(SAFETY_TASK_DELAY);
	}
}

