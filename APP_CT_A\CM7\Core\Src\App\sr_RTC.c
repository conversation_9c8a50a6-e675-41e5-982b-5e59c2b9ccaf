/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_RTC.c
* @version 		  : 1.0.0
* @brief Subroutine updating and accessing real time clock
* @details Subroutine updating and accessing real time clock
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include <stdio.h>
#include <time.h>
#include "app.h"
#include "sr_RTC.h"
#include "dg_car_mr_a7.h"
#include "time_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (10)
#define SUBROUTINE_RUN_INTERVAL_1MS                (1000)


/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef enum
{
   RTC_EDIT_INDEX__MONTH,
   RTC_EDIT_INDEX__DATE_OF_MONTH,
   RTC_EDIT_INDEX__YEAR,
   RTC_EDIT_INDEX__HOUR,
   RTC_EDIT_INDEX__MINUTE,
   RTC_EDIT_INDEX__SECOND,
   RTC_EDIT_INDEX__WEEKDAY,

   NUM_RTC_EDIT_INDEX
}en_rtc_edit_index;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
#ifndef _RTC_H_
/* Used by nodes without direct access to a RTC  */
static int32_t iLastRxTime_1ms;
#endif
static st_rtc_request stLastRtcRequest;
/******************************************************************************
* Function Definitions
*******************************************************************************/

static void UpdateCurrentTime(void)
{
#ifdef _RTC_H_ /* If this node has a RTC peripheral, read the hardware peripheral and update the global structure */
   RTC_TimeTypeDef stTime;
   RTC_DateTypeDef stDate;
   st_timestamp_chip *stTimestamp_Current = Time_GetTimestampPointer_Current();
   uint32_t *puiOffset_Current = Time_GetUnixOffsetPointer_Current();
   RTC_GetTime(&stTime);
   RTC_GetDate(&stDate);
   stTimestamp_Current->ucHour = stTime.Hours;
   stTimestamp_Current->ucMinute = stTime.Minutes;
   stTimestamp_Current->ucSecond = stTime.Seconds;
   stTimestamp_Current->ucYear = stDate.Year;
   stTimestamp_Current->ucMonth = stDate.Month;
   stTimestamp_Current->ucDayOfMonth = stDate.Date;
   stTimestamp_Current->ucWeekday = stDate.WeekDay;

   *puiOffset_Current = Time_GetOffsetFromStamp(stTimestamp_Current);
#else /* If it doesn't, modify the global structure using the last received time and apply an offset based on a local timer */
      st_datagram_control *pstControl = DG_CAR_MR_A7_GetDatagramControlStruct();
      st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_MR_A7__RTC];
      if( pstDatagram->bDataChanged )
      {
         pstDatagram->bDataChanged = INACTIVE;
         iLastRxTime_1ms = Timer_GetCount_1ms();
      }
      st_timestamp_chip *pstTimestamp_Current = Time_GetTimestampPointer_Current();
      st_timestamp_chip *pstTimestamp_RX = Time_GetTimestampPointer_RX();
      int32_t iDiff_1s = ( Timer_GetCount_1ms() - iLastRxTime_1ms ) / 1000;
      uint32_t uiSeconds = pstTimestamp_RX->ucSecond + iDiff_1s;
      uint32_t uiMinutes = pstTimestamp_RX->ucMinute + (uiSeconds / 60);
      uint32_t uiHours = pstTimestamp_RX->ucHour + (uiMinutes / 60);
      uint32_t uiDayOffset = (uiHours / 60);
      // TODO review for day ucYear, ucMonth, ucDayOfMonth accuracy engage RTC running on LSI oscillator or use time.h libraries
      pstTimestamp_Current->ucYear = pstTimestamp_RX->ucYear;
      pstTimestamp_Current->ucMonth = pstTimestamp_RX->ucMonth;
      pstTimestamp_Current->ucDayOfMonth = pstTimestamp_RX->ucDayOfMonth;
      pstTimestamp_Current->ucWeekday = ( ( ( pstTimestamp_RX->ucWeekday - WEEKDAY__MONDAY ) + uiDayOffset ) % 7  ) + WEEKDAY__MONDAY;
      pstTimestamp_Current->ucHour = uiHours % 24;
      pstTimestamp_Current->ucMinute = uiMinutes % 60;
      pstTimestamp_Current->ucSecond = uiSeconds % 60;
      *Time_GetUnixOffsetPointer_Current() = Time_GetOffsetFromStamp(pstTimestamp_Current);
#endif
}

static void UpdateTimeChangeRequest(void)
{
	st_rtc_request *stRtcRequest = Time_GetRtcRequestPointer();
   if( memcmp(&stLastRtcRequest, stRtcRequest, MAX_RTC_REQUEST_SIZE__BYTE) != 0 )
   {
#ifdef _RTC_H_ /* If this node has a RTC peripheral, set the time, otherwise load a request */
	  st_timestamp_chip stTimestamp_Request;
	  Time_ConvertOffsetToStamp(stRtcRequest->uiOffset_Request, &stTimestamp_Request);
      RTC_TimeTypeDef stTime = {
            .DayLightSaving = INACTIVE,
            .StoreOperation = RTC_STOREOPERATION_RESET,
            .SecondFraction = 0,
            .SubSeconds = 0,
            .TimeFormat = RTC_HOURFORMAT12_AM,
            .Seconds = stTimestamp_Request.ucSecond,
            .Minutes = stTimestamp_Request.ucMinute,
            .Hours = stTimestamp_Request.ucHour,
      };
      RTC_DateTypeDef stDate = {
            .WeekDay = stTimestamp_Request.ucWeekday,
            .Month = stTimestamp_Request.ucMonth,
            .Date = stTimestamp_Request.ucDayOfMonth,
            .Year = stTimestamp_Request.ucYear,
      };
      RTC_SetTime(&stTime);
	  RTC_SetDate(&stDate);
	  RTC_SetHourFormat((stRtcRequest->uiRtcOption & 0x01));
#else
      Time_SetFileManager_Request(true);
#endif
      memcpy(&stLastRtcRequest, stRtcRequest, MAX_RTC_REQUEST_SIZE__BYTE);
   }
}

/* @fn void RTC_Init(void)
 * @brief
 * @param None
 * @return None
 */
void RTC_Init(void)
{
#ifdef _RTC_H_
   UpdateCurrentTime();
   uint32_t *puiOffset_Current = Time_GetUnixOffsetPointer_Current();
   if(*puiOffset_Current == 0)
   {
	   st_rtc_request *stRtcRequest = Time_GetRtcRequestPointer();
	   stRtcRequest->uiOffset_Request = 1;
   }
#endif
}

/* @fn void vRTC_Update(void)
 * @brief Updates the RTC by checking and applying time changes.
 *        This function checks for any time change requests and updates the RTC's current time.
 * @param None
 * @return None
 */
void vRTC_Update(void)
{
	UpdateTimeChangeRequest();
	UpdateCurrentTime();
}
