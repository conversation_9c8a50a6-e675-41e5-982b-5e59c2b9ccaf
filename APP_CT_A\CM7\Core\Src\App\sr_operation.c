/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_operation.c
* @version 		  : 1.0.0
* @brief This subroutine updates the car's floor
* @details This subroutine updates the car's floor
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_operation.h"
#include "door_def.h"
#include "motion_def.h"
#include "operation_def.h"
#include "position_def.h"
#include "floor_def.h"
#include "param_def.h"
#include "dg_car_mr_a7.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (100)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (50)

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* __weak START */
en_active_inactive Operation_CheckIfPreopening(en_door eDoor)
{
	return Operation_CheckIfPreopening_MRA(eDoor);
}
en_motion_state Motion_GetMotionState(void)
{
    return Motion_GetMotionState_MRA();
}
/* __weak END */
/* @fn static void UpdateFloorIndex(void)
 * @brief Updates the current and destination floor index
 */
static void UpdateFloorIndex(void)
{
   if( ( Param_GetState() == PARAM_STATE__IDLE )
    && ( Position_GetState() == POSITION_STATE__KNOWN ) )
   {
      Operation_SetCurrentFloor(Floor_GetFloorIndex(Position_GetPosition_05mm()));
      Operation_SetDestinationFloor(Floor_GetFloorIndex(Motion_GetDestination_05mm_MRA()));
   }
}

/* @fn void vOperation_Task(void *pvParameters)
 * @brief Execute the vParameters_Task to update the floor index according to UI inputs
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vOperation_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		UpdateFloorIndex();

		vTaskDelay(OPERATION_TASK_DELAY);
	}
}

