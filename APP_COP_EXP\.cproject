<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="elf" artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="rm -rf" description="" errorParsers="org.eclipse.cdt.core.GASErrorParser;org.eclipse.cdt.core.GmakeErrorParser;org.eclipse.cdt.core.GLDErrorParser;org.eclipse.cdt.core.CWDLocator;org.eclipse.cdt.core.GCCErrorParser" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********" name="Debug" parent="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release" postannouncebuildStep="Remove header from elf and create a binary file" postbuildStep="arm-none-eabi-objcopy -O binary --remove-section=.app_header ${ProjName}.elf ${ProjName}_no_header.bin">
					<folderInfo id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********." name="/" resourcePath="">
						<toolChain id="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.908069825" name="MCU ARM GCC" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release">
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.1711969316" name="MCU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu" useByScannerDiscovery="true" value="STM32G473QETx" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.1212346361" name="CPU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid" useByScannerDiscovery="false" value="0" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1110055003" name="Core" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid" useByScannerDiscovery="false" value="0" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.1671190202" name="Floating-point unit" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu" useByScannerDiscovery="true" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.value.fpv4-sp-d16" valueType="enumerated"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.168049197" name="Floating-point ABI" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi" useByScannerDiscovery="true" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.value.hard" valueType="enumerated"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.423993266" name="Board" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board" useByScannerDiscovery="false" value="genericBoard" valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.899018045" name="Defaults" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults" useByScannerDiscovery="false" value="com.st.stm32cube.ide.common.services.build.inputs.revA.1.0.5 || Debug || false || Executable || com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain.value.workspace || STM32G473QETx || 0 || 0 || arm-none-eabi- || ${gnu_tools_for_stm32_compiler_path} || ../Core/Inc | ../Drivers/STM32G4xx_HAL_Driver/Inc | ../Drivers/STM32G4xx_HAL_Driver/Inc/Legacy | ../Drivers/CMSIS/Device/ST/STM32G4xx/Include | ../Drivers/CMSIS/Include ||  ||  || USE_HAL_DRIVER | STM32G473xx ||  || Drivers | Core/Startup | Core ||  ||  || ${workspace_loc:/${ProjName}/STM32G473QETX_FLASH.ld} || true || NonSecure ||  || secure_nsclib.o ||  || None || " valueType="string"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.convertbinary.688475263" name="Convert to binary file (-O binary)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.convertbinary" useByScannerDiscovery="false" value="false" valueType="boolean"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain.1561695554" name="Toolchain" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.toolchain.value.workspace" valueType="string"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform.181913341" isAbstract="false" osList="all" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform"/>
							<builder buildPath="${workspace_loc:/APP_COP_EXP}/Release" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder.62766483" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="Gnu Make Builder" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.builder"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1666144536" name="MCU GCC Assembler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.91827377" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.debuglevel.value.g3" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.definedsymbols.342254941" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.definedsymbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="DEBUG"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths.404418685" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/Config}&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input.563053642" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.621260123" name="MCU GCC Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.703064133" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.debuglevel.value.g3" valueType="enumerated"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.1613864163" name="Optimization level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.optimization.level.value.o0" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols.383624609" name="Define symbols (-D)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.definedsymbols" useByScannerDiscovery="false" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="USE_HAL_DRIVER"/>
									<listOptionValue builtIn="false" value="STM32G473xx"/>
									<listOptionValue builtIn="false" value="DEBUG"/>
									<listOptionValue builtIn="false" value="DISABLE_WDT"/>
									<listOptionValue builtIn="false" value="COP_NODE"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths.703523463" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths" useByScannerDiscovery="false" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc/App}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc/L0}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc/Network}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Car}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_System/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_System/inc/Parameters}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Hall}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/STM32G4xx_HAL_Driver/Inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/STM32G4xx_HAL_Driver/Inc/Legacy}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/CMSIS/Device/ST/STM32G4xx/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/CMSIS/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Group}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_System/Bootloader/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/FreeRTOS-Kernel/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/FreeRTOS-Kernel/portable/GCC/ARM_CM4F}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/Config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/Sample/OS}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/SEGGER}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Control}&quot;"/>
								</option>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.w_switch_default.175749409" name="Warn when a switch statement does not have a default case (-Wswitch-default)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.w_switch_default" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.w_switch_enum.1000844765" name="Warn if switch is used on an enum type and the switch statement lacks case for some enumerations (-Wswitch-enum)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.w_switch_enum" useByScannerDiscovery="false" value="false" valueType="boolean"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.missing_include_dirs.1775561141" name="Warn if a user-supplied include directory does not exist (-Wmissing-include-dirs)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.missing_include_dirs" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.fatal.734687454" name="Abort compilation on first error (-Wfatal-errors)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.fatal" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.conversion.835317322" name="Warn for implicit conversions (-Wconversion)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.warnings.conversion" useByScannerDiscovery="false" value="false" valueType="boolean"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.fnostrictaliasing.1998827668" name="Disable &quot;strict aliasing&quot; optimization (-fno-strict-aliasing)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.fnostrictaliasing" useByScannerDiscovery="false" value="true" valueType="boolean"/>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.978583736" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.677572201" name="MCU G++ Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.438457688" name="Debug level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.debuglevel.value.g0" valueType="enumerated"/>
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level.2041093396" name="Optimization level" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level" useByScannerDiscovery="false" value="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.option.optimization.level.value.os" valueType="enumerated"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1978646338" name="MCU GCC Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker">
								<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.script.312943022" name="Linker Script (-T)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.script" useByScannerDiscovery="false" value="${workspace_loc}\Lib_System\Bootloader\linker_scripts\STM32G473QETX_APPLICATION.ld" valueType="string"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.directories.488300923" name="Library search path (-L)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.option.directories" useByScannerDiscovery="false" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc}\Lib_System\Bootloader\linker_scripts&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.input.1993174418" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
								</inputType>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1546888199" name="MCU G++ Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.2069265977" name="MCU GCC Archiver" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.1231248443" name="MCU Size" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.2134841651" name="MCU Output Converter list file" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1757584694" name="MCU Output Converter Hex" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1975331697" name="MCU Output Converter Binary" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1090025466" name="MCU Output Converter Verilog" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.988912482" name="MCU Output Converter Motorola S-rec" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1769905573" name="MCU Output Converter Motorola S-rec with symbols" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********.1148006185" name="/" resourcePath="ThirdParty/SEGGER">
						<toolChain id="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release.118631201" name="MCU ARM GCC" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.toolchain.exe.release" unusedChildren="">
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.1711969316.1930293576" name="MCU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_mcu.1711969316"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.1212346361.605736864" name="CPU" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_cpuid.1212346361"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1110055003.286587087" name="Core" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_coreid.1110055003"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.1671190202.169736166" name="Floating-point unit" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.fpu.1671190202"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.168049197.126021977" name="Floating-point ABI" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.floatabi.168049197"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.423993266.1156103697" name="Board" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.target_board.423993266"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.899018045.1782303010" name="Defaults" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.defaults.899018045"/>
							<option id="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.convertbinary.688475263.837480727" name="Convert to binary file (-O binary)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.option.convertbinary.688475263"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.ELF" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform" isAbstract="false" osList="all" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.targetplatform"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.839568361" name="MCU GCC Assembler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.1666144536">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths.1142002015" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.option.includepaths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/Config}&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input.78145310" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.assembler.input"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.295066500" name="MCU GCC Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.621260123">
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths.1330002532" name="Include paths (-I)" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.option.includepaths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc/App}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc/L0}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Core/Inc/Network}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Car}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_System/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_System/inc/Parameters}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Hall}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/STM32G4xx_HAL_Driver/Inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/STM32G4xx_HAL_Driver/Inc/Legacy}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/CMSIS/Device/ST/STM32G4xx/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Drivers/CMSIS/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Group}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_System/Bootloader/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/FreeRTOS-Kernel/include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/FreeRTOS-Kernel/portable/GCC/ARM_CM4F}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/Config}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/Sample/OS}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/ThirdParty/SEGGER/SEGGER}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/APP_COP_EXP/Lib_Network/inc/Control}&quot;"/>
								</option>
								<inputType id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.315870003" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c"/>
							</tool>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.2143932512" name="MCU G++ Compiler" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.compiler.677572201"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.717430514" name="MCU GCC Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.linker.1978646338"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.385181136" name="MCU G++ Linker" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.cpp.linker.1546888199"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.272110252" name="MCU GCC Archiver" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.archiver.2069265977"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.999877679" name="MCU Size" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.size.1231248443"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.2055292389" name="MCU Output Converter list file" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objdump.listfile.2134841651"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.742239052" name="MCU Output Converter Hex" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.hex.1757584694"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.190214101" name="MCU Output Converter Binary" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.binary.1975331697"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.649693796" name="MCU Output Converter Verilog" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.verilog.1090025466"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.1028186314" name="MCU Output Converter Motorola S-rec" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.srec.988912482"/>
							<tool id="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.405396260" name="MCU Output Converter Motorola S-rec with symbols" superClass="com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.objcopy.symbolsrec.1769905573"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Core"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Drivers"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Lib_Network"/>
						<entry excluding="Bootloader/Startup/startup_stm32h753vitx.s|Bootloader/src|Bootloader/Startup/startup_stm32h745bitx.s|Bootloader/ports|Bootloader/src/timer.c|Bootloader/src/sr_bootloader.c|Bootloader/src/queue.c|Bootloader/src/fsm.c|Bootloader/src/bootloader.c|Bootloader/src/bootloader_update.c|Bootloader/src/bootloader_temp.c|Bootloader/src/bootloader_image.c|Bootloader/src/bootloader_def.c|Bootloader/src/bootloader_core.c" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="Lib_System"/>
						<entry excluding="FreeRTOS-Kernel/portable/MemMang|FreeRTOS-Kernel/portable/GCC/ARM_CM7" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="ThirdParty"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.pathentry"/>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="APP_COP_EXP.null.574312188" name="APP_COP_EXP"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Release_Pre_Rev3_Board"/>
		<configuration configurationName="Multiple configurations">
			<resource resourceType="PROJECT" workspacePath="/APP_COP_EXP"/>
		</configuration>
		<configuration configurationName="Debug_Pre_Rev3_Board"/>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/APP_COP_EXP"/>
		</configuration>
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/APP_COP_EXP"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.1270546506;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.506693418">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.1878804063;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.1878804063.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.2035295061;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.237305430">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********.979149001;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********.979149001.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.758394778;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.1954665751">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********;com.st.stm32cube.ide.mcu.gnu.managedbuild.config.exe.release.736662969.**********.;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.621260123;com.st.stm32cube.ide.mcu.gnu.managedbuild.tool.c.compiler.input.c.978583736">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
</cproject>