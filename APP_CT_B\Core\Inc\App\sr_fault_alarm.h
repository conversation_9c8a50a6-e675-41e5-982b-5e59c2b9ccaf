/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file sr_Fault.h
* @version 1.0.0
* @brief Subroutine updating processor's fault status
* @details Subroutine updating processor's fault status
*****************************************************************************/
#ifndef _SR_FAULT_
#define _SR_FAULT_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "app.h"
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/

void Fault_Init(void);
void vFaultAlarm_Task(void *pvParams);

#endif /* _SR_FAULT_ */
