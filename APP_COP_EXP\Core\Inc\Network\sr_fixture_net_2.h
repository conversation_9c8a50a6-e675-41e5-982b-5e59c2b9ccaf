/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file sr_fixture_net_2.h
* @version 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/
#ifndef _SR_FIXTURE_NET_2_H_
#define _SR_FIXTURE_NET_2_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
#include "door_def.h"
#include "hall_call_def.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/
// Fixture_net2 Task
#define FIXTURE_NET2_TASK_FIRST_DELAY_MS 6000
#define FIXTURE_NET2_TASK_PERIODIC_DELAY_MS 5

//Inactive Interval Common for all
#define TASK_RUN_INTERVAL_INACTIVE_MS 	(0xFFFF)


/******************************************************************************
* Typedefs
*******************************************************************************/
typedef enum
{
   FIXTURE_NET_2_STATE__INIT,      /* Waiting for parameter initialization */
   FIXTURE_NET_2_STATE__RUNNING_MAD,   /* Initialized and sending messages */

   NUM_FIXTURE_NET_2_STATES
}en_fixture_net_2_state;
/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_fixture_net_2_state Fixture_Net_2_GetState(void);
void FixtureNet2_Task_Init(void);
void vFixtureNet2_Task(void *pvParams);
#endif /* _SR_FIXTURE_NET_2_H_ */
