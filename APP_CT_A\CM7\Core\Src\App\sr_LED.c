/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_LED.c
* @version 		  : 1.0.0
* @brief This subroutine updates on board LEDs
* @details This subroutine updates on board LEDs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_LED.h"
#include "system.h"
#include "car_faults_def.h"
#include "car_alarms_def.h"
#include "app_gpio.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_RUN_INTERVAL_1MS                (100)

#define LED_HEARTBEAT_UPDATE_RATE_100MS            (10)
#define LED_ALARM_UPDATE_RATE_100MS                (5)
#define LED_FAULT_UPDATE_RATE_RESET_100MS          (10)
#define LED_FAULT_UPDATE_RATE_OTHER_100MS          (5)

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn static void LED_UpdateHeartbeat(void)
 * @brief Updates Heart beat LED output
 * @param None
 * @return None
 */
static void LED_UpdateHeartbeat(void)
{
   static uint8_t ucCounter_100ms;
   if(++ucCounter_100ms >= LED_HEARTBEAT_UPDATE_RATE_100MS)
   {
      uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_HB);
      GPIO_WriteOutput(enLOCAL_OUT__LED_HB, !bLastLED);
      ucCounter_100ms = 0;
   }
}
/* @fn static void LED_UpdateAlarm(void)
 * @brief Updates alarm LED output
 * @param None
 * @return None
 */
static void LED_UpdateAlarm(void)
{
   static uint8_t ucCounter_100ms;
   if( Alarms_GetAlarmFlag() )
   {
      if(++ucCounter_100ms >= LED_ALARM_UPDATE_RATE_100MS)
      {
         uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_ALM);
         GPIO_WriteOutput(enLOCAL_OUT__LED_ALM, !bLastLED);
         ucCounter_100ms = 0;
      }
   }
   else
   {
      GPIO_WriteOutput(enLOCAL_OUT__LED_ALM, INACTIVE);
   }
}
/* @fn static void LED_UpdateFault(void)
 * @brief Updates fault LED output
 * @param None
 * @return None
 */
static void LED_UpdateFault(void)
{
   static uint8_t ucCounter_100ms;

   if( Faults_GetFaultFlag() )
   {
      en_car_fault eFault = Faults_GetFaultNumber_ByNode(System_GetNodeID());
      if( ( eFault >= CFLT__CT_A7_POWER_RESET ) && ( eFault <= CFLT__CT_A7_BOD_RESET ) )
      {
         if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_RESET_100MS )
         {
            uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
            GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
            ucCounter_100ms = 0;
         }
      }
      else if( ( eFault == CFLT__CT_A4_OFFLINE_A7 )
            || ( eFault == CFLT__MR_OFFLINE_CT )
            || ( eFault == CFLT__CT_B_OFFLINE_CT_A )
            || ( eFault == CFLT__COP_OFFLINE_CT ) )
      {
         GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, ACTIVE);
      }
      else
      {
         if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_OTHER_100MS )
         {
            uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
            GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
            ucCounter_100ms = 0;
         }
      }
   }
   else
   {
      GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, INACTIVE);
   }
}

/* @fn LED_Update(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
void vLED_Update(void)
{
   LED_UpdateHeartbeat();
   LED_UpdateAlarm();
   LED_UpdateFault();
}

