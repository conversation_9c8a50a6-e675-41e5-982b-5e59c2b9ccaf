/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_car_net.h
* @version 		  : 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/
#ifndef _SR_CAR_NET_
#define _SR_CAR_NET_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_active_inactive GetTransmitFaultDatagramCheck(void);
void SetTransmitFaultDatagramCheck(en_active_inactive bActive);
void CarNet_Task_Init(void);
void vCarNet_Task(void *pvParams);
#endif /* _SR_CAR_NET_ */
