/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_SPI_AU.c
 * @version 		  : 1.0.0
 * @brief This subroutine loads and unloads packets for transmission on SPI which connects CTA to UI
 * @details This subroutine loads and unloads packets for transmission on SPI which connects CTA to UI
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <string.h>
#include "app.h"
#include "sr_SPI_AU.h"
#include "sr_CAN1.h"
#include "sr_SPI_AB.h"
#include "app_spi.h"
#include "param_def.h"
#include "dg_car_ui_a7.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
#define UNLOAD_TIMEOUT_1MS                                        (3)  // How long UnloadDatagrams can be called before the loop must exit

// How long the bus can go without receiving a message before an offline flag is set
#define SPI_AU_OFFLINE_TIMEOUT_1MS                                (61000)

// How long the bus can go without receiving a message before an offline flag is set
#define UI_CT_OFFLINE_TIMEOUT_1MS                                 (61000)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/

/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
#define AUW_RX_MESSAGEBUFFER_SIZE (SPI_MAX_PACKET_PAYLOAD_SIZE__WORD + SPI_STARTING_INDEX_PACKET_ID + 5)/* temporary workaround for SPI issue */
static uint16_t auwRxMessageBuffer[AUW_RX_MESSAGEBUFFER_SIZE];// Account for STX plus 1 to guard against overrun

static uint16_t uwOfflineTimer_1ms;
static uint16_t uwOfflineTimer_UI_CT_1ms = UI_CT_OFFLINE_TIMEOUT_1MS;
static uint8_t bLastOfflineFlag_UI_CT; /* 1 if the UI CT board was offline last routine run */

static uint8_t ucRxMessageIndex;
static uint8_t bSubstituteNextValue;
static const en_sys_network eLocalNetwork = SYS_NETWORK__CAR;
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail SPI_AU_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Takes a CAN message packet and converts it to a format for transmission on the SPI AU network and loads it to a ring buffer for transmission
 * @param None
 * @return Returns 0 if successful in loading the requested datagram
 */
en_pass_fail SPI_AU_TransmitDatagram(st_CAN_msg *pstMsg) {
   uint32_t uiCRC;
   uint16_t auwTxMessageBuffer[SPI_MAX_PACKET_SIZE__WORD];
   uint16_t auwTempBuffer[SPI_MAX_PACKET_PAYLOAD_SIZE__WORD] = { 0 };
   uint16_t uwValue;
   uint8_t ucTempMessageIndex = 0;
   uint8_t ucTxMessageIndex = 0;

   /* Construct a temporary buffer containing just the date to be encapsulated (Packet ID + DATA + CRC) */
   *(uint32_t*) &auwTempBuffer[ucTempMessageIndex] = pstMsg->uiID;
   ucTempMessageIndex += SPI_PACKET_ID_SIZE__WORD;
   uint8_t ucWordLength = NUM_ELEMENTS_U8_TO_U16(pstMsg->ucDLC);
   for(uint8_t i = 0; i < ucWordLength; i++) {
      auwTempBuffer[ucTempMessageIndex] = pstMsg->unData.auw16[i];
      ucTempMessageIndex++;
   }
   /* For DGs that aren't aligned to 2B words, clear out the excess so it doesn't impact the CRC calculation */
   if((pstMsg->ucDLC % SPI_WORD_SIZE__BYTE) != 0)
      auwTempBuffer[ucTempMessageIndex - 1] &= 0x00FF;

   /* Data field sizes are not U32 aligned but CRC calculations are, make sure all U32 fields are a known state */
   uint32_t uiSizeInU32 = NUM_ELEMENTS_U16_TO_U32(ucTempMessageIndex);
   for(uint8_t ucWordIndex = ucTempMessageIndex;
         ucWordIndex < uiSizeInU32 * (BITMAP32_SIZE_IN_BYTES / BITMAP16_SIZE_IN_BYTES);
         ucWordIndex++) {
      auwTempBuffer[ucWordIndex] = 0;
   }
   uiCRC = CRC_Calculate((uint32_t*) &auwTempBuffer[0], uiSizeInU32);

   *(uint32_t*) &auwTempBuffer[ucTempMessageIndex] = uiCRC;
   ucTempMessageIndex += SPI_CRC_SIZE__WORD;

   /* Encode the message in the format for transmission */
   auwTxMessageBuffer[ucTxMessageIndex++] = SPI_START_OF_PACKET_SPECIAL_VALUE;
   for(uint8_t i = 0; i < ucTempMessageIndex; i++) {
      uwValue = auwTempBuffer[i];
      if((uwValue == SPI_START_OF_PACKET_SPECIAL_VALUE)
         || (uwValue == SPI_END_OF_PACKET_SPECIAL_VALUE)
         || (uwValue == SPI_SUBSTITUTE_SPECIAL_VALUE)) {
         auwTxMessageBuffer[ucTxMessageIndex++] = SPI_SUBSTITUTE_SPECIAL_VALUE;
         auwTxMessageBuffer[ucTxMessageIndex++] = ~uwValue;
      } else
         auwTxMessageBuffer[ucTxMessageIndex++] = uwValue;
   }
   auwTxMessageBuffer[ucTxMessageIndex++] = SPI_END_OF_PACKET_SPECIAL_VALUE;

   if(SPI_AU_LoadToTxRB(auwTxMessageBuffer, ucTxMessageIndex) == PASS)
      return PASS;
   else
      return FAIL;
}

/* @fn static void UnloadDatagrams(void)
 * @brief Unloads words from ring buffer, processes the words into packets and unloads them if valid
 * @param None
 * @return None
 */
static void UnloadDatagrams(void) {
   st_datagram_control *pstControl;
   st_datagram *pstDatagram;
   st_CAN_msg stRxMsg;
   en_sys_network eNetwork;
   en_sys_node eSource;
   uint32_t uiCalculatedRxCRC;
   uint32_t uiReceivedCRC;
   uint16_t uwDatagramID;
   uint8_t ucNumBytes;
   uint16_t uwValue;
   int32_t iStartTime_1ms = Timer_GetCount_1ms();
   int32_t iDiff_1ms = 0;
   while(iDiff_1ms < UNLOAD_TIMEOUT_1MS) {
      if(SPI_AU_UnloadFromRxRB(&uwValue) == PASS) {
         if(ucRxMessageIndex
            > SPI_MAX_PACKET_PAYLOAD_SIZE__WORD + SPI_STARTING_INDEX_PACKET_ID) {
            ucRxMessageIndex = 0;
            SPI_RX_IncrementCounter(SPI_AU, SPI_COUNTER__UNDERRUN);
            break;
         }
         /* Check for start of packet indicator */
         else if(uwValue == SPI_START_OF_PACKET_SPECIAL_VALUE) {
            auwRxMessageBuffer[0] = SPI_START_OF_PACKET_SPECIAL_VALUE;
            ucRxMessageIndex = SPI_STARTING_INDEX_PACKET_ID;
         } else if(uwValue == SPI_END_OF_PACKET_SPECIAL_VALUE) {
            /* If CRC and length matches, mark packet for processing */
            stRxMsg.uiID = *(uint32_t*) &auwRxMessageBuffer[SPI_STARTING_INDEX_PACKET_ID];
            stRxMsg.ucDLC = Network_GetNumberOfBytes(stRxMsg.uiID);
            uiReceivedCRC = *(uint32_t*) &auwRxMessageBuffer[ucRxMessageIndex
                                                             - SPI_CRC_SIZE__WORD];

            /* Data field sizes are not U32 aligned but CRC calculations are, make sure all U32 fields are a known state  */
            if(stRxMsg.ucDLC > 64)
            {
              // TODO: probably a corrupted uiID, WE NEED TO TRACK THIS!
              // WARNING: if this is bigger than 64, the application will most likely crash!
              return;
            }
            uint16_t uwDLC_InWords = NUM_ELEMENTS_U8_TO_U16(stRxMsg.ucDLC);
            uint32_t uiSizeInU32 = NUM_ELEMENTS_U16_TO_U32(uwDLC_InWords+SPI_PACKET_ID_SIZE__WORD);
            uint16_t uwStartingIndex = SPI_STARTING_INDEX_DATA + uwDLC_InWords;
            uint16_t uwEndingIndex = SPI_STARTING_INDEX_PACKET_ID
                                     + uiSizeInU32
                                       * (BITMAP32_SIZE_IN_BYTES / BITMAP16_SIZE_IN_BYTES);
            for(uint16_t uwWordIndex = uwStartingIndex; uwWordIndex < uwEndingIndex; uwWordIndex++)
            {
            	auwRxMessageBuffer[uwWordIndex] = 0;
            }

            uiCalculatedRxCRC = CRC_Calculate((uint32_t*) &auwRxMessageBuffer[SPI_STARTING_INDEX_PACKET_ID],
                                              uiSizeInU32);
            eNetwork = Network_GetNetworkID(stRxMsg.uiID);
            eSource = Network_GetSourceID(stRxMsg.uiID);
            uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
            if(uiCalculatedRxCRC == uiReceivedCRC) {
               /* If the CRC passes, convert the packet to can message format
                * Then process the packet */
               if(eNetwork == eLocalNetwork) {
                  pstControl = Network_GetControlStructure_Car(eSource);
                  if(pstControl != 0) {
                     if(uwDatagramID < pstControl->uwNumberOfDG) {
                        pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                        ucNumBytes = pstDatagram->ucSize_Bytes;
                        /* 1. Copy the content of every datagram */
                        memcpy(&pstDatagram->paucData[0],
                               &auwRxMessageBuffer[SPI_STARTING_INDEX_DATA],
                               ucNumBytes);
                        /* 2. Run the datagram's unload function */
                        if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID()))
                           pstDatagram->pfnUnload();

                        /* 3. Check if the packet should be forwarded */
                        if((Network_CheckDestination_Car(NET_FWD_PATH__CAR, pstDatagram->uiDestinationBitmap) == PASS)
                           && (Param_GetState() != PARAM_STATE__STARTUP)) /* Prevent forwarding from MCUB when initializing */
                           {
                           stRxMsg.bCAN_FD = 1;
                           memcpy(&stRxMsg.unData.aui32[0],
                                  &auwRxMessageBuffer[SPI_STARTING_INDEX_DATA],
                                  stRxMsg.ucDLC);
                           CAN1_TransmitDatagram(&stRxMsg);
                        }

                        if((Network_CheckDestination_Car(NET_FWD_PATH__AB,
                                                         pstDatagram->uiDestinationBitmap)
                                   == PASS)) {
                           SPI_AB_TransmitDatagram(&stRxMsg);
                        }
                        /* 4. Increment receive counter */
                        pstDatagram->ucPacketCounter++;

                        /* 5. Mark packet as received */
                        pstDatagram->bDataChanged = ACTIVE;

                        /* 6. Reset offline timers */
                        if( eSource == SYS_NODE__UI_CT_A7 )
                        {
                            uwOfflineTimer_UI_CT_1ms = 0;
                        }
                     }
                  }
               }
               SPI_RX_IncrementCounter(SPI_AU, SPI_COUNTER__RX);
               uwOfflineTimer_1ms = 0;
            } else
               SPI_RX_IncrementCounter(SPI_AU, SPI_COUNTER__CRC);
            ucRxMessageIndex = 0;
         } else if(uwValue == SPI_SUBSTITUTE_SPECIAL_VALUE)
            bSubstituteNextValue = ACTIVE;
         else {
            if(bSubstituteNextValue == ACTIVE) {
               bSubstituteNextValue = INACTIVE;
               uwValue = ~uwValue;
            }
            auwRxMessageBuffer[ucRxMessageIndex++] = uwValue;
         }
      } else
         break;
      iDiff_1ms = Timer_GetCount_1ms() - iStartTime_1ms;
   }
}
/* @fn uint8_t SPI_AU_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t SPI_AU_CheckIfCommLoss(void)
{
    return (uwOfflineTimer_1ms >= SPI_AU_OFFLINE_TIMEOUT_1MS);
}
/* @fn static void UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void)
{
	uint8_t bOfflineFlag_UI_CT;
    if( uwOfflineTimer_1ms >= SPI_AU_OFFLINE_TIMEOUT_1MS )
    {

    }
    else
    {
        uwOfflineTimer_1ms += SPIAU_TASK_DELAY;
    }

    if( uwOfflineTimer_UI_CT_1ms >= UI_CT_OFFLINE_TIMEOUT_1MS )
    {
    	bOfflineFlag_UI_CT = ACTIVE;
    	if( !bLastOfflineFlag_UI_CT )
    	{
    		ClearUIDatagrams(SYS_NODE__UI_CT_A7);
    	}
    }
    else
    {
    	bOfflineFlag_UI_CT = INACTIVE;
        uwOfflineTimer_UI_CT_1ms += SPIAU_TASK_DELAY;
    }

    bLastOfflineFlag_UI_CT = bOfflineFlag_UI_CT;
}

/* @fn void vSPIAU_Task(void *pvParameters)
 * @brief Handles SPI-AU tasks, including unloading data-grams from MCU A to MCU B
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vSPIAU_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		UnloadDatagrams();
		UpdateOfflineTimer();

		vTaskDelay(SPIAU_TASK_DELAY);
	}
}
