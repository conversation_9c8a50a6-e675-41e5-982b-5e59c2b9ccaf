/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_Watchdog.h
 * @version 		  : 1.0.0
 * @brief This subroutine updates the watchdog peripheral to prevent reset
 * @details This subroutine updates the watchdog peripheral to prevent reset
 *****************************************************************************/
#ifndef _SR_WATCHDOG_
#define _SR_WATCHDOG_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>

/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/


/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
void vWatchdog_Update(void);
#endif /* _SR_WATCHDOG_ */
