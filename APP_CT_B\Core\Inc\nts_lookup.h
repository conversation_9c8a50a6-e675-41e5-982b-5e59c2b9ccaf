#ifndef INC_NTS_LOOKUP_H_
#define INC_NTS_LOOKUP_H_

#include <stdint.h>

#define NTS_TABLE_ROW_SIZE      (7U)
#define NTS_TABLE_COLUMN_SIZE   (20U)

#define UNTS_PARAM_OFFSET       (0U)
#define DNTS_PARAM_OFFSET       NTS_TABLE_ROW_SIZE

typedef struct
{
  const uint16_t speed;
  const uint32_t positions[NTS_TABLE_ROW_SIZE];
  const uint8_t count;
  int16_t velocities[NTS_TABLE_ROW_SIZE];
  uint32_t index;
}nts_lut_t;

extern nts_lut_t NTS_LookupTable[];

#endif /* INC_NTS_LOOKUP_H_ */
