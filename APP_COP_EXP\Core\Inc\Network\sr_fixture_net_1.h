/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file sr_fixture_net_1.h
* @version 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/
#ifndef _SR_FIXTURE_NET_1_H_
#define _SR_FIXTURE_NET_1_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
#include "door_def.h"
#include "hall_call_def.h"
#include "dg_car_mr_b4.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/
// Fixture_net1 Task
#define FIXTURE_NET1_TASK_FIRST_DELAY_MS 5000
#define FIXTURE_NET1_TASK_PERIODIC_DELAY_MS 5

//Inactive Interval Common for all
#define TASK_RUN_INTERVAL_INACTIVE_MS 	(0xFFFF)

/******************************************************************************
* Typedefs
*******************************************************************************/
typedef enum
{
   FIXTURE_NET_1_STATE__INIT,      /* Waiting for parameter initialization */
   FIXTURE_NET_1_STATE__RUNNING_MAD,   /* Initialized and sending messages */

   NUM_FIXTURE_NET_1_STATES
}en_fixture_net_1_state;
/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_fixture_net_1_state Fixture_Net_1_GetState(void);
void FixtureNet1_Task_Init(void);
void vFixtureNet1_Task(void *pvParams);
#endif /* _SR_FIXTURE_NET_1_H_ */
