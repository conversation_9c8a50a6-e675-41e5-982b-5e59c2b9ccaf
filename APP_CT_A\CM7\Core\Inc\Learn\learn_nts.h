#ifndef INC_LEARN_LEARN_NTS_H_
#define INC_LEARN_LEARN_NTS_H_

#include "learn_v2_def.h"
#include "LearnDef/learn_usr_evt.h"

#define DEFAULT_NTS_POS_XCHECK_VALUE    10
#define DEFAULT_NTS_SPEED_XCHECK_VALUE  10

learn_status_t Learn_NTS_Init(void);
learn_status_t Learn_NTS_Record(void);
learn_status_t Learn_NTS_SaveParams(learn_usr_t type);
learn_status_t Learn_NTS_ParamSync(learn_usr_t type);
learn_status_t Learn_NTS_SaveXCheck(uint8_t *msg_data);
learn_status_t Learn_NTS_XCheck(void);

#endif
