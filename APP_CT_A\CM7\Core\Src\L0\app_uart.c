#include "app_uart.h"

void UART7_Init(void)
{
  /* PE7 -> UART7 RX
   * PE8 -> UART7 TX
   */
  RCC->AHB4ENR  |= RCC_AHB4ENR_GPIOEEN;
  RCC->APB1LENR |= RCC_APB1LENR_UART7EN;

  /* Set GPIOH 13 and 14 alternate for UART */
  GPIOE->MODER &= ~(GPIO_MODER_MODE7_Msk |
                    GPIO_MODER_MODE8_Msk);
  GPIOE->MODER |= ((2 << GPIO_MODER_MODE7_Pos) |
                   (2 << GPIO_MODER_MODE8_Pos));
  GPIOE->AFR[0] |= (7 << GPIO_AFRL_AFSEL7_Pos);
  GPIOE->AFR[1] |= (7 << GPIO_AFRH_AFSEL8_Pos);

  UART7->CR1 &= ~USART_CR1_UE;

  /* Set UART 4 8 bits, 1 parity bit, 115200 baudrate */
  UART7->CR1 |= USART_CR1_TE;
  /* UART4 Clock = HSI = 64MHz */
  /* BRR = 64MHz / 115200 = 555.55 = 0x22B */
//  UART7->BRR = 0x022C;
  UART7->BRR = 693;

  /* Enable UART4 and clear all interrupt flags*/
  UART7->CR1 |= USART_CR1_UE;
}

void UART7_write(const uint8_t *data, uint32_t size)
{
  /* Write message to message buffer */

  for(uint32_t i = 0; i < size; i++)
  {
    while((UART7->ISR & USART_ISR_TXE_TXFNF) == 0);
    UART7->TDR = data[i];
  }

  while((UART7->ISR & USART_ISR_TC) == 0);
}

