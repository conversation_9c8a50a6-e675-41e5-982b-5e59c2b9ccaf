/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN2.c
 * @version 		  : 1.0.0
 * @brief Subroutine handling CAN2 network
 * @details Subroutine handling CAN2 network
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "sr_fault_alarm.h"
#include <string.h>
#include "sr_CAN2.h"
#include "sr_CAN1.h"
#include "main.h"
#include "car_faults_def.h"
#include "hall_call_def.h"
#include "app_can.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/

// How long the bus can go without receiving a message before an offline flag is set
#define CAN_OFFLINE_TIMEOUT_1MS                    (15000)
#define CAN_OFFLINE_TIMEOUT_INVALID_1MS            (0xFFFF)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
uint32_t gCAN2_Task_Delay = 0; // Global variable to hold the delay interval
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static uint16_t uwOfflineTimer_1ms = CAN_OFFLINE_TIMEOUT_INVALID_1MS;
/* Accounting array to keep track of which types of hall boards are connected to bus. */
static uint8_t aucHBFuncsOnBus[MAX_NUM_HB_FUNC_SW_SETTINGS];
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail CAN2_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Returns 0 if the packet was successfully loaded for transmit
 * @param pstMsg, pointer to CAN message to transmit
 * @return Returns 0 if the packet was successfully loaded for transmit
 */
en_pass_fail CAN2_TransmitDatagram(st_CAN_msg *pstMsg)
{
	en_pass_fail eError = FAIL;
	if( pstMsg->bExtendedID == INACTIVE )
	{
		if( pstMsg->uiID & NETWORK_PACKET_ID_EXTENDED_BITMAP ) pstMsg->bExtendedID = ACTIVE;
		else pstMsg->bExtendedID = INACTIVE;
	}
	pstMsg->uiID &= NETWORK_PACKET_ID_EXTENDED_MASK;
	if( CAN2_LoadToRB(pstMsg) == PASS )
	{
		eError = PASS;
	}
	return eError;
}
/* @fn en_active_inactive CAN2_CheckDestination(uint32_t uiDestinationBitmap)
 * @brief Returns 1 if the packet needs to be transmitted on the CAN2 Car network
 * @param None
 * @return None
 */
en_active_inactive CAN2_CheckDestination(uint32_t uiDestinationBitmap)
{
	switch(Main_GetLocalBoardType())
	{
		case BOARD_COP:
			break;
		case BOARD_EXP_MASTER:
		case BOARD_EXP_SLAVE:
			if( uiDestinationBitmap & (DG_DEST_MAP__ALL_EXP) ) return ACTIVE;
			break;
		case BOARD_RISER:
			break;
		case BOARD_INVALID:
		default:
		break;
	}
	return INACTIVE;
}
/* @fn static en_pass_fail CAN_ExpMessageInRange(uint16_t uwDatagramID)
 * @brief Returns PASS if received expansion message is within range
 * @param uwDatagramID, expansion datagram ID
 * @return Returns PASS if received expansion message is within range
 */
static en_pass_fail CAN_ExpMessageInRange(uint16_t uwDatagramID) {
   uint8_t ucLowerBound = Main_GetLocalExpansionID() / EXPANSION_MAX_BOARDS_PER_MASTER;
   if((uwDatagramID <= ucLowerBound) || (uwDatagramID > (ucLowerBound + 3))) {
      return FAIL;
   }
   return PASS;
}
/* @fn static void CAN2_ResetOfflineTimers_Inter(en_sys_node eSource, uint16_t uwDatagramID)
 * @brief Resets offline timers for the car net
 * @param eSource, message source
 * @return None
 */
static void CAN2_ResetOfflineTimers_Inter(en_sys_node eSource, uint16_t uwDatagramID)
{
	switch(Main_GetLocalBoardType())
	{
		case BOARD_COP:
			Faults_SetFault(CFLT__INVALID_DEVICE_COP_CAN2);
			break;
		case BOARD_EXP_MASTER:
		case BOARD_EXP_SLAVE:
			if(eSource == SYS_NODE__EXP)
			{
				if(CAN_ExpMessageInRange(uwDatagramID) == FAIL)
				{
					Error_SetActiveError(EXP_ERROR__INVALID_DEVICE_CAN2);
				}
				else if( uwDatagramID == Main_GetLocalExpansionID() )
		    	{
		    		Error_SetActiveError(EXP_ERROR__DUPLICATE_ADDR);
		    	}
			}
			break;
		case BOARD_RISER:
			break;
		case BOARD_INVALID:
		default:
		break;
	}
}
/* @fn en_active_inactive CAN2_HallBoardFuncOnBus(uint8_t ucFunc)
 * @brief Determine if a hall board with given function arg is connected to the CAN bus.
 * @param ucFunc Function setting value to check
 * @return ACTIVE if on bus, INACTIVE if not.
 */
en_active_inactive CAN2_HallBoardFuncOnBus(uint8_t ucFunc)
{
	if( ucFunc >= MAX_NUM_HB_FUNC_SW_SETTINGS ) return INACTIVE;
	return aucHBFuncsOnBus[ucFunc];
}
/* @fn static void UnloadDatagrams(void)
 * @brief Unloads messages from the CAN ring buffer
 * @param None
 * @return None
 */
static void UnloadDatagrams(void)
{
	st_datagram_control *pstControl;
	st_datagram *pstDatagram;
	st_CAN_msg stRxMsg;
	en_sys_network eNetwork;
	en_sys_node eSource;
	uint16_t uwDatagramID;
	uint8_t ucNumBytes;
	for(uint8_t i = 0; i < CAN_RX_RING_BUFFER_SIZE; i++)
	{
		if( CAN2_UnloadFromRB(&stRxMsg) == PASS )
		{
			eNetwork = Network_GetNetworkID(stRxMsg.uiID);
			if( eNetwork == SYS_NETWORK__INTERGROUP )
			{
				eSource = Network_GetSourceID(stRxMsg.uiID);
				pstControl = Network_GetControlStructure_Car(eSource);
				if( pstControl != 0 )
				{
					uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
					if( uwDatagramID < pstControl->uwNumberOfDG )
					{
						pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
						ucNumBytes = pstDatagram->ucSize_Bytes;
						/* 1. Copy the content of every datagram */
						memcpy(pstDatagram->paucData, &stRxMsg.unData.auc8[0], ucNumBytes);
						/* 2. Run the datagram's unload function */
						if( pstDatagram->uiDestinationBitmap & ( 1 << System_GetNodeID() ) ) pstDatagram->pfnUnload();

						/* 3. Check if the packet should be forwarded */
		                if( Network_CheckDestination_Car(NET_FWD_PATH__CAR, pstDatagram->uiDestinationBitmap) == PASS )
		                {
		                	if(Main_GetLocalBoardType() == BOARD_EXP_MASTER)
		                	{
		                		stRxMsg.uiID = Network_GetPacketID(stRxMsg.ucDLC, uwDatagramID, SYS_NETWORK__CAR, eSource);
		                	}
		                    CAN1_TransmitDatagram(&stRxMsg);
		                }
						/* 4. Increment receive counter */
						pstDatagram->ucPacketCounter++;

						/* 5. Mark packet as received */
						if(eSource != SYS_NODE__EXP)
						{
							pstDatagram->bDataChanged = ACTIVE;
						}

						/* 6. Reset offline timers */
						CAN2_ResetOfflineTimers_Inter(eSource, uwDatagramID);
					}
				}
			}
			// Check if dg sent from hall board
			else if(stRxMsg.uiID < MAX_NUM_HB_TX_DATAGRAM_IDS)
			{
				uint16_t uwId = Hall_Board_ProcessCallDatagram(&stRxMsg);
				// If it's a valid hall board dg, record that we've found it's function on this bus
				if( uwId )
				{
					st_hb_settings stHBId = Hall_Board_ParseHBTxMsgId((uint32_t)stRxMsg.uiID);
					aucHBFuncsOnBus[stHBId.ucFunc] = ACTIVE;
				}
			}
			else
			{
				Faults_SetFault(CFLT__INVALID_DEVICE_COP_CAN2);
				Error_SetActiveError(EXP_ERROR__INVALID_DEVICE_CAN2);
				Error_SetActiveErrorRiser(RIS_ERROR__INVALID_DEVICE_CAN2);
			}
			CAN2_IncrementStatusCounter(CAN_COUNTER__RX_PACKET);
			uwOfflineTimer_1ms = 0;
		}
		else
		{
			break;
		}
	}
}
/* @fn uint8_t CAN2_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t CAN2_CheckIfCommLoss(void)
{
	return ( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS );
}
/* @fn static void UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void)
{
	if( uwOfflineTimer_1ms != CAN_OFFLINE_TIMEOUT_INVALID_1MS )
	{
		if( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS )
		{
			switch(Main_GetLocalBoardType())
			{
				case BOARD_COP:
					break;
				case BOARD_EXP_MASTER:
				case BOARD_EXP_SLAVE:
					Error_SetActiveError(EXP_ERROR__COMM_CAN2);
					break;
				case BOARD_RISER:
					Error_SetActiveErrorRiser(RIS_ERROR__COMM_CAN2);
					break;
				case BOARD_INVALID:
				default:
				break;
			}
		}
		else
		{
		   uwOfflineTimer_1ms += CAN2_TASK_PERIODIC_DELAY_MS;
		}
	}
}

/* @fn void CAN2_Task_Init(void)
 * @brief Initializes the CAN2 task delay based on the local board type
 * @param None
 * @return None
 */
void CAN2_Task_Init(void)
{
	switch (Main_GetLocalBoardType())
		{
			case BOARD_EXP_MASTER:
			case BOARD_EXP_SLAVE:
			case BOARD_RISER:
				gCAN2_Task_Delay = CAN2_TASK_PERIODIC_DELAY_MS;
				break;
			default:
				gCAN2_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;
				break;
		}
}
/* @fn void vCAN2_Task(void *pvParams)
 * @brief Handles CAN2 communication, updates error counters, and manages errors based on board type
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCAN2_Task(void *pvParams)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1){
	__disable_irq();
        UnloadDatagrams();
	__enable_irq();

        // Update the offline timer
        UpdateOfflineTimer();

        // Update the CAN2 bus error counter
        CAN2_UpdateBusErrorCounter();

        // Check if CAN2 is offline
        if( CAN2_CheckForBusOffline() )
        {
            // Handle bus offline scenario based on local board type
            switch(Main_GetLocalBoardType())
            {
                case BOARD_COP:
                    // No alarm set for COP board on bus offline (commented out)
                    // Alarms_SetAlarm(CALM__BUS_RESET_COP_N2);
                    break;
                case BOARD_EXP_MASTER:
                case BOARD_EXP_SLAVE:
                    // Set active error for EXP board (Master/Slave) on bus offline
                    Error_SetActiveError(EXP_ERROR__BUS_OFFLINE_CAN2);
                    break;
                case BOARD_RISER:
                    // Set active error for RISER board on bus offline
                    Error_SetActiveErrorRiser(RIS_ERROR__BUS_OFFLINE_CAN2);
                    break;
                case BOARD_INVALID:
                default:
                    // No action for invalid or default board types
                    break;
            }
        }
        // Task delay based on the configured CAN2 task period
        vTaskDelay(gCAN2_Task_Delay);
    }
}

