/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : app_watchdog.c
 * @version 		  : 1.0.0
 * @brief Functions for initializing and update the watchdog
 * @details Functions for initializing and update the watchdog
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "app_watchdog.h"

#include "main.h"
#include "stm32h7xx_hal.h"
/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
#define WATCHDOG_TIMEOUT_VALUE            (3000)
#define WATCHDOG_PRESCALE_VALUE           (IWDG_PRESCALER_32) // 1 kHz
#define WATCHDOG_OSCILLATOR_SPEED_HZ      (32000)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
IWDG_HandleTypeDef hiwdg1;
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/

/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Function Definitions
 *******************************************************************************/

/* @fn void Watchdog_Init(void)
 * @brief Initializes watchdog
 * @param None
 * @return None
 */
void Watchdog_Init(void)
{
   /* Initializes for 32khz clock, 3 second timeout window */
   hiwdg1.Instance = IWDG1;
   hiwdg1.Init.Prescaler = WATCHDOG_PRESCALE_VALUE;
   hiwdg1.Init.Window = WATCHDOG_TIMEOUT_VALUE;
   hiwdg1.Init.Reload = WATCHDOG_TIMEOUT_VALUE;
   if(HAL_IWDG_Init(&hiwdg1) != HAL_OK)
   {
      Error_Handler();
   }
}

void Watchdog_SetTimeout(uint32_t timeout)
{
  hiwdg1.Instance = IWDG1;
  hiwdg1.Init.Prescaler = WATCHDOG_PRESCALE_VALUE;
  hiwdg1.Init.Window = timeout;
  hiwdg1.Init.Reload = 0;
  if(HAL_IWDG_Init(&hiwdg1) != HAL_OK)
  {
     Error_Handler();
  }
}

/* @fn void Watchdog_Feed(void)
 * @brief Feeds the watchdog
 * @param None
 * @return None
 */
void Watchdog_Feed(void)
{
  /* Reload IWDG counter with value defined in the reload register */
  HAL_IWDG_Refresh(&hiwdg1);
}
