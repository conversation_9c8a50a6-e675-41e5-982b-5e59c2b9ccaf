[1/10] Performing build step for 'bootloader'
[1/1] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\bootloader\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/bootloader/bootloader.bin"

Bootloader binary size 0x5220 bytes. 0x2de0 bytes (36%) free.


[2/10] No install step for 'bootloader'
[3/10] Completed 'bootloader'
[4/10] Building C object esp-idf/main/CMakeFiles/__idf_main.dir/Src/uart_handler.c.obj
[5/10] Linking C static library esp-idf\main\libmain.a
[6/10] Generating ld/sections.ld
[7/10] Linking CXX executable Vantage_nxESP32_Int_Rel_1_2_0_0.elf
[8/10] Generating binary image from built executable
esptool.py v4.8.1

Creating esp32s3 image...

Merged 2 ELF sections

Successfully created esp32s3 image.

Generated D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_0_0.bin
[9/10] C:\Windows\system32\cmd.exe /C "cd /D D:\BLE\Bitbucket\Feature_ble_fw_esp32\APP_ESP32\build\esp-idf\esptool_py && C:\Users\<USER>\.espressif\python_env\idf5.4_py3.11_env\Scripts\python.exe C:/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/partition_table/partition-table.bin D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/Vantage_nxESP32_Int_Rel_1_2_0_0.bin"
Vantage_nxESP32_Int_Rel_1_2_0_0.bin binary size 0x95100 bytes. Smallest app partition is 0x100000 bytes. 0x6af00 bytes (42%) free.

[9/10] C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.4.1\esp-idf\components\esptool_py && C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.4.1/esp-idf -D SERIAL_TOOL=C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build -P C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake"
esptool.py --chip esp32s3 -p COM33 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size detect 0x0 bootloader/bootloader.bin 0x10000 Vantage_nxESP32_Int_Rel_1_2_0_0.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.8.1
Serial port COM33
Connecting...
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Unknown Embedded PSRAM (AP_1v8)
Crystal is 40MHz
MAC: b4:3a:45:f7:39:08
Uploading stub...
Running stub...

A fatal error occurred: Packet content transfer stopped (received 5 bytes)
FAILED: CMakeFiles/flash D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build/CMakeFiles/flash 
C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\esp\v5.4.1\esp-idf\components\esptool_py && C:\Users\<USER>\.espressif\tools\cmake\3.30.2\bin\cmake.exe -D IDF_PATH=C:/Users/<USER>/esp/v5.4.1/esp-idf -D SERIAL_TOOL=C:/Users/<USER>/.espressif/python_env/idf5.4_py3.11_env/Scripts/python.exe;;C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3 -D SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args -D WORKING_DIRECTORY=D:/BLE/Bitbucket/Feature_ble_fw_esp32/APP_ESP32/build -P C:/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake"
ninja: build stopped: subcommand failed.
