/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_Watchdog.h
 * @version 		  : 1.0.0
 * @brief          : Watchdog subroutine
 * @details		  : Watchdog subroutine
 ********************************************************************************/
#ifndef _SR_WATCHDOG_H_
#define _SR_WATCHDOG_H_
/*******************************************************************************
 * Includes
 ********************************************************************************/
#include <stdint.h>
/*******************************************************************************
 * Preprocessor Constants
 ********************************************************************************/

/*******************************************************************************
 * Configuration Constants
 ********************************************************************************/

/*******************************************************************************
 * Macros
 ********************************************************************************/

/*******************************************************************************
 * Typedefs
 ********************************************************************************/

/*******************************************************************************
 * Global Variables
 ********************************************************************************/

/*******************************************************************************
 * Function Prototypes
 ********************************************************************************/

#endif /* _SR_WATCHDOG_H_ */
