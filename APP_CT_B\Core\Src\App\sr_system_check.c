#include "app.h"
#include "param_def.h"
#include "nts_traction.h"
#include "car_faults_def.h"
#include "operation_def.h"

#define SYSTEM_CHECK_INITIAL_DELAY_1MS 4000

#define LEARN_BYPASS_CHECK    (Operation_GetSemiAutoOperation() == SEMIAUTO_OP__LEARN)
#define MANUAL_BYPASS_CHECK   ( (Operation_GetManualOperation() != MANUAL_OP__OFF) && \
    (Param_ReadValue_1Bit(CPARAM1__Bypass_Landing_System_Feedback) == 1) )

extern nts_traction_t nts_traction;

void SystemCheck_NTSLearnParams(void)
{
  NTS_Traction_LoadPoints(&nts_traction.unts,
      CPARAM8__Traction_UNTS_Count,
      CPARAM24__Traction_UNTS_Prof_1_Pos_1,
      CPARAM16__Traction_UNTS_Prof_1_Speed_1);
  NTS_Traction_LoadPoints(&nts_traction.dnts,
      CPARAM8__Traction_DNTS_Count,
      CPARAM24__Traction_DNTS_Prof_1_Pos_1,
      CPARAM16__Traction_DNTS_Prof_1_Speed_1);

  // Bypass completely if we are trying to learn or if manual mode is on AND
  // 01-033 is ON
  bool learn_bypass  = LEARN_BYPASS_CHECK  ? true : false;
  bool manual_bypass = MANUAL_BYPASS_CHECK ? true : false;
  if((learn_bypass  == false) &&
     (manual_bypass == false))
  {
    if(NTS_Traction_CheckNTSPoints(&nts_traction.unts, CHECK_UNTS) == ACTIVE)
    {
      Faults_SetFault(CFLT__CONFIG_UNTS);
    }

    if(NTS_Traction_CheckNTSPoints(&nts_traction.dnts, CHECK_DNTS) == ACTIVE)
    {
      Faults_SetFault(CFLT__CONFIG_DNTS);
    }
  }
}

void vSystemCheckTask(void *pvArgs)
{
  vTaskDelay(SYSTEM_CHECK_INITIAL_DELAY_1MS);

  while(1)
  {
    if( Param_GetState() != PARAM_STATE__STARTUP )
    {
      // Check NTS parameters
      SystemCheck_NTSLearnParams();
    }

    vTaskDelay(SYSTEM_CHECK_TASK_DELAY);
  }
}


