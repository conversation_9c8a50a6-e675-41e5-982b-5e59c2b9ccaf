#include "Learn/learn_nts.h"
#include "Learn/learn_ets.h"
#include "LearnDef/learn_usr_evt.h"
#include "ets_lookup.h"
#include "nts_lookup.h"
#include "param_def.h"
#include "motion_def.h"
#include "position_def.h"
#include "floor_def.h"

#include "ulog.h"

#include <string.h>
#include <stdlib.h>

learn_usr_t learn_type;

void Learn_Wait_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg);
void Learn_Start_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg);
void Learn_Record_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg);
void Learn_WaitLanding_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg);
void Learn_WaitCTB_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg);
void Learn_SaveParams_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg);

void Learn_Start_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg)
{
  switch(msg->evt)
  {
  case LEARN_EVT_INIT_NTS_ETS:
  {
    ULOG_DEBUG("LEARN_EVT_INIT_NTS_ETS");

    learn_status_t NTS_status = Learn_NTS_Init();
    learn_status_t ETS_status = Learn_ETS_Init();
    if((NTS_status == LEARN_STATUS__OK) &&
       (ETS_status == LEARN_STATUS__OK))
    {
      ULOG_DEBUG("NTS/ETS learn started");

      LEARN_TRANS_POST_EVT(&Learn_Record_NTS_ETS_State,
          LEARN_EVT_RECORD_NTS_ETS);
    }
    else
    {
      /* Send failure to CTA*/
      uint8_t msg[2] = { NTS_status, ETS_status };
      Learn_SendMessage(self,
          LEARN_EVT_FAILED_NTS_ETS,
          &msg[0],
          2);
      LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    }
  }
    break;
  case LEARN_EVT_CANCEL_NTS_ETS:
    LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    ULOG_DEBUG("LEARN_EVT_CANCEL_NTS_ETS");
    break;
  default:
    break;
  }
}

void Learn_Record_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg)
{
  switch(msg->evt)
  {
  case LEARN_EVT_RECORD_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_RECORD_NTS_ETS");
    TimerEvt_Arm(&self->timer, 50, 50);
    break;
  case LEARN_EVT_TIMEOUT:
  {
    learn_status_t NTS_status = Learn_NTS_Record();
    learn_status_t ETS_status = Learn_ETS_Record();
    if((NTS_status == LEARN_STATUS__OK) &&
       (ETS_status == LEARN_STATUS__OK))
    {
      ULOG_DEBUG("NTS/ETS recording complete");

      /* Learn complete, go to wait for landing */
      LEARN_TRANS_POST_EVT(&Learn_WaitLanding_NTS_ETS_State,
          LEARN_EVT_WAIT_LANDING_NTS_ETS);

      TimerEvt_Disarm(&self->timer);
    }
    else if((NTS_status >= LEARN_STATUS__USER_ERROR) ||
            (ETS_status >= LEARN_STATUS__USER_ERROR))
    {
      ULOG_DEBUG("NTS/ETS recording failed");

      /* Send failure to UI*/
      uint8_t msg[2] = { NTS_status, ETS_status };
      Learn_SendMessage(self,
          LEARN_EVT_FAILED_NTS_ETS,
          &msg[0],
          2);
      LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    }
  }
    break;
  case LEARN_EVT_CANCEL_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_CANCEL_NTS_ETS");
    TimerEvt_Disarm(&self->timer);
    LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    break;
  default:
    break;
  }
}

void Learn_WaitLanding_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg)
{
  switch(msg->evt)
  {
  case LEARN_EVT_WAIT_LANDING_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_WAIT_LANDING_NTS_ETS");
    TimerEvt_Arm(&self->timer, 50, 50);
    break;
  case LEARN_EVT_TIMEOUT:
    if( (Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE) &&
        (Position_GetSpeed_fpm()                           == 0))
    {
      ULOG_DEBUG("Car is completely stopped, waiting for CTB...");

      /* Car has come to a complete stop, wait for CTB*/
      LEARN_TRANS(&Learn_WaitCTB_NTS_ETS_State);

      TimerEvt_Disarm(&self->timer);
    }
    break;
  case LEARN_EVT_CANCEL_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_CANCEL_NTS_ETS");
    TimerEvt_Disarm(&self->timer);
    LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    break;
  default:
    break;
  }
}

void Learn_WaitCTB_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg)
{
  switch(msg->evt)
  {
  case LEARN_EVT_CTB_READY_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_CTB_READY_NTS_ETS");
    /* Save cross check values */
    Learn_NTS_SaveXCheck(&msg->data[0]);
    Learn_ETS_SaveXCheck(&msg->data[42]);

    // Run xcheck
    learn_status_t NTS_status = Learn_NTS_XCheck();
    learn_status_t ETS_status = Learn_ETS_XCheck();
    if((NTS_status == LEARN_STATUS__OK) &&
       (ETS_status == LEARN_STATUS__OK))
    {
      ULOG_DEBUG("NTS/ETS successful xcheck");
      LEARN_TRANS_POST_EVT(&Learn_SaveParams_NTS_ETS_State,
          LEARN_EVT_SAVE_PARAMS_NTS_ETS);
    }
    else
    {
     //Send error to UI
      ULOG_DEBUG("NTS/ETS xcheck error");
      uint8_t msg[2] = { NTS_status, ETS_status };
      Learn_SendMessage(self,
          LEARN_EVT_FAILED_NTS_ETS,
          &msg[0],
          2);
      LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    }
    break;
  case LEARN_EVT_CANCEL_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_CANCEL_NTS_ETS");
    LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    break;
  default:
    break;
  }
}

void Learn_SaveParams_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg)
{
  switch(msg->evt)
  {
  case LEARN_EVT_SAVE_PARAMS_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_SAVE_PARAMS_NTS_ETS");
    Learn_NTS_SaveParams(learn_type);
    Learn_ETS_SaveParams(learn_type);

    // TODO: Implement a timeout here
    TimerEvt_Arm(&self->timer, 50, 50);
    break;
  case LEARN_EVT_TIMEOUT:
    if((Param_GetBlockToUpdate() == NUM_PARAM_BLOCKS) &&
       (Learn_NTS_ParamSync(learn_type)    == LEARN_STATUS__OK) &&
       (Learn_ETS_ParamSync(learn_type)    == LEARN_STATUS__OK))
    {
      /* Send success to UI and go back to waiting for NTS/ETS learn*/
      ULOG_DEBUG("Parameters in sync");
      ULOG_DEBUG("Learn complete");
      Learn_SendMessage(self,
          LEARN_EVT_SUCCESS_NTS_ETS,
          NULL,
          0);
      LEARN_TRANS(&Learn_Wait_NTS_ETS_State);

      TimerEvt_Disarm(&self->timer);
    }
    break;
  case LEARN_EVT_CANCEL_NTS_ETS:
    ULOG_DEBUG("LEARN_EVT_CANCEL_NTS_ETS");
    TimerEvt_Disarm(&self->timer);
    LEARN_TRANS(&Learn_Wait_NTS_ETS_State);
    break;
  default:
    break;
  }
}

void Learn_Wait_NTS_ETS_State(learn_t * const self,
    learn_in_msg_t * const msg)
{
  switch(msg->evt)
  {
  case LEARN_EVT_START_NTS_ETS:
    learn_type = (learn_usr_t)msg->data[0];

    LEARN_TRANS_POST_EVT(&Learn_Start_NTS_ETS_State,
        LEARN_EVT_INIT_NTS_ETS);
    break;
  case LEARN_EVT_CANCEL_NTS_ETS:
    // Do nothing, just go stay in this state
    ULOG_DEBUG("LEARN_EVT_CANCEL_NTS_ETS");
    break;
  default:
    break;
  }
}
