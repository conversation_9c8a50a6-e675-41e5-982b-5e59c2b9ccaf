/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_can.c
* @version 		  : 1.0.0
* @brief Functions for accessing and initializing CAN bus
* @details Functions for accessing and initializing CAN bus
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "app_can.h"
#include "stm32h7xx_hal.h"
#include "main.h"
/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define FDCAN_DLC_BYTE_SHIFT              (16) // Return of FDCAN_RxHeaderTypeDef requires shifting of DLC return
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
FDCAN_HandleTypeDef hfdcan1;
FDCAN_HandleTypeDef hfdcan2;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static uint16_t auwStatusCounter_CAN1[NUM_CAN_COUNTERS];
static uint16_t auwStatusCounter_CAN2[NUM_CAN_COUNTERS];

/* Lookup table of CAN bitrate configuration values based on a 48 MHZ clock.
 * Generated via CodeForGeneratingCANBitTIming_STM32H745.c tool assuming
 * sampling point between 74 and 88% */
static const st_can_bitrate_config astBitrateConfig[NUM_CAN_BAUD_RATES] = { {// CAN_BAUD_RATE__50K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 32,
                                                                            .ucTimeSeg2 = 7,
                                                                            .ucPrescale = 24, },
                                                                            {// CAN_BAUD_RATE__75K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 32,
                                                                            .ucTimeSeg2 = 7,
                                                                            .ucPrescale = 16, },
                                                                            {// CAN_BAUD_RATE__100K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 32,
                                                                            .ucTimeSeg2 = 7,
                                                                            .ucPrescale = 12, },
                                                                            {// CAN_BAUD_RATE__125K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 27,
                                                                            .ucTimeSeg2 = 4,
                                                                            .ucPrescale = 12, },
                                                                            {// CAN_BAUD_RATE__150K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 32,
                                                                            .ucTimeSeg2 = 7,
                                                                            .ucPrescale = 8, },
                                                                            {// CAN_BAUD_RATE__200K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 32,
                                                                            .ucTimeSeg2 = 7,
                                                                            .ucPrescale = 6, },
                                                                            {// CAN_BAUD_RATE__250K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 27,
                                                                            .ucTimeSeg2 = 4,
                                                                            .ucPrescale = 6, },
                                                                            {// CAN_BAUD_RATE__500K
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 27,
                                                                            .ucTimeSeg2 = 4,
                                                                            .ucPrescale = 3, },
                                                                            {// CAN_BAUD_RATE__1M
                                                                            .ucSyncJumpWidth = 1,
                                                                            .ucTimeSeg1 = 20,
                                                                            .ucTimeSeg2 = 3,
                                                                            .ucPrescale = 2, }, };
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @brief functions for accessing and incrementing CAN bus error counters */
uint16_t CAN1_GetStatusCounter(en_can_counter eCounter)
{
   return auwStatusCounter_CAN1[eCounter];
}
uint16_t CAN2_GetStatusCounter(en_can_counter eCounter)
{
   return auwStatusCounter_CAN2[eCounter];
}
void CAN1_IncrementStatusCounter(en_can_counter eCounter)
{
   auwStatusCounter_CAN1[eCounter]++;
}
void CAN2_IncrementStatusCounter(en_can_counter eCounter)
{
   auwStatusCounter_CAN2[eCounter]++;
}

/* @fn void CAN1_UpdateBusErrorCounter(void)
 * @brief Checks for bus errors and updates running error counter
 * @param None
 * @return Checks for bus errors and updates running error counter
 */
void CAN1_UpdateBusErrorCounter(void)
{
   static uint16_t uwLastErrorCount;
   /* Sum of running Rx and Tx error counters. Taken from HAL_FDCAN_GetErrorCounters */
   uint16_t uwNewErrorCount = ( ( FDCAN1->ECR & FDCAN_ECR_REC ) >> FDCAN_ECR_REC_Pos )
                            + ( ( FDCAN1->ECR & FDCAN_ECR_TEC ) >> FDCAN_ECR_TEC_Pos )
                            + ( ( FDCAN1->ECR & FDCAN_ECR_CEL ) >> FDCAN_ECR_CEL_Pos );
   /* If the register error counters have changed from last cycle, increment the running counter. */
   if( uwLastErrorCount < uwNewErrorCount )
   {
      auwStatusCounter_CAN1[CAN_COUNTER__BUS_ERROR]++;
   }
   uwLastErrorCount = uwNewErrorCount;
}
/* @fn void CAN2_UpdateBusErrorCounter(void)
 * @brief Checks for bus errors and updates running error counter
 * @param None
 * @return Checks for bus errors and updates running error counter
 */
void CAN2_UpdateBusErrorCounter(void)
{
   static uint16_t uwLastErrorCount;
   /* Sum of running Rx and Tx error counters. Taken from HAL_FDCAN_GetErrorCounters */
   uint16_t uwNewErrorCount = ( ( FDCAN2->ECR & FDCAN_ECR_REC ) >> FDCAN_ECR_REC_Pos )
                            + ( ( FDCAN2->ECR & FDCAN_ECR_TEC ) >> FDCAN_ECR_TEC_Pos )
                            + ( ( FDCAN2->ECR & FDCAN_ECR_CEL ) >> FDCAN_ECR_CEL_Pos );
   /* If the register error counters have changed from last cycle, increment the running counter. */
   if( uwLastErrorCount < uwNewErrorCount )
   {
      auwStatusCounter_CAN2[CAN_COUNTER__BUS_ERROR]++;
   }
   uwLastErrorCount = uwNewErrorCount;
}

/* @fn uint8_t CAN1_CheckForBusOffline(void)
 * @brief Returns 1 if the bus has gone offline. Also attempts to clear the offline state by restarting the CAN controller
 * @param None
 * @return Returns 1 if the bus has gone offline. Also attempts to clear the offline state by restarting the CAN controller
 */
uint8_t CAN1_CheckForBusOffline(void)
{
   static uint8_t bLastOffline;
   uint8_t bBusOffline = FDCAN1->PSR & FDCAN_PSR_BO;
   if( bBusOffline ) // taken from HAL_FDCAN_GetProtocolStatus
   {
      /* Restart the CAN controller which will wait 129 occurrences
       * of bus idle before returning to operation, taken from HAL_FDCAN_Start */
      FDCAN1->CCCR &= ~FDCAN_CCCR_INIT;
      if( !bLastOffline ) auwStatusCounter_CAN1[CAN_COUNTER__BUS_RESET]++;
   }
   bLastOffline = bBusOffline;
   return bBusOffline;
}
/* @fn uint8_t CAN2_CheckForBusOffline(void)
 * @brief Returns 1 if the bus has gone offline. Also attempts to clear the offline state by restarting the CAN controller
 * @param None
 * @return Returns 1 if the bus has gone offline. Also attempts to clear the offline state by restarting the CAN controller
 */
uint8_t CAN2_CheckForBusOffline(void)
{
   static uint8_t bLastOffline;
   uint8_t bBusOffline = FDCAN2->PSR & FDCAN_PSR_BO;
   if( bBusOffline ) // taken from HAL_FDCAN_GetProtocolStatus
   {
      /* Restart the CAN controller which will wait 129 occurrences
       * of bus idle before returning to operation, taken from HAL_FDCAN_Start */
      FDCAN2->CCCR &= ~FDCAN_CCCR_INIT;
      if( !bLastOffline ) auwStatusCounter_CAN2[CAN_COUNTER__BUS_RESET]++;
   }
   bLastOffline = bBusOffline;
   return bBusOffline;
}


/* @fn static uint32_t CAN_AppDLCToHardwareDLC(uint8_t ucDLC)
 * @brief Function to convert application DLC which is in byte counts to the FDCAN FDCAN_data_length_code values
 * @param ucDLC, DLC count in bytes
 * @return returns the DLC encoded as FDCAN_data_length_code
 */
static uint32_t CAN_AppDLCToHardwareDLC(uint8_t ucDLC)
{
   uint32_t uiDLC;
   switch( ucDLC )
   {
      case 0 ... 8:
         uiDLC = ucDLC << FDCAN_DLC_BYTE_SHIFT;
         break;
      case 9 ... 12:
         uiDLC = FDCAN_DLC_BYTES_12;
         break;
      case 13 ... 16:
         uiDLC = FDCAN_DLC_BYTES_16;
         break;
      case 17 ... 20:
         uiDLC = FDCAN_DLC_BYTES_20;
         break;
      case 21 ... 24:
         uiDLC = FDCAN_DLC_BYTES_24;
         break;
      case 25 ... 32:
         uiDLC = FDCAN_DLC_BYTES_32;
         break;
      case 33 ... 48:
         uiDLC = FDCAN_DLC_BYTES_48;
         break;
      default:
         uiDLC = FDCAN_DLC_BYTES_64;
         break;
   }
   return uiDLC;
}
/* @fn static uint32_t CAN_AppDLCToHardwareDLC(uint8_t ucDLC)
 * @brief Function to convert application DLC which is in byte counts to the FDCAN FDCAN_data_length_code values
 * @param ucDLC, DLC count in bytes
 * @return returns the DLC encoded as FDCAN_data_length_code
 */
static uint8_t CAN_HardwareDLCToAppDLC(uint32_t uiDLC)
{
   uint8_t ucDLC;
   switch( uiDLC )
   {
      case FDCAN_DLC_BYTES_0:
         ucDLC = 0;
         break;
      case FDCAN_DLC_BYTES_1:
         ucDLC = 1;
         break;
      case FDCAN_DLC_BYTES_2:
         ucDLC = 2;
         break;
      case FDCAN_DLC_BYTES_3:
         ucDLC = 3;
         break;
      case FDCAN_DLC_BYTES_4:
         ucDLC = 4;
         break;
      case FDCAN_DLC_BYTES_5:
         ucDLC = 5;
         break;
      case FDCAN_DLC_BYTES_6:
         ucDLC = 6;
         break;
      case FDCAN_DLC_BYTES_7:
         ucDLC = 7;
         break;
      case FDCAN_DLC_BYTES_8:
         ucDLC = 8;
         break;
      case FDCAN_DLC_BYTES_12:
         ucDLC = 12;
         break;
      case FDCAN_DLC_BYTES_16:
         ucDLC = 16;
         break;
      case FDCAN_DLC_BYTES_20:
         ucDLC = 20;
         break;
      case FDCAN_DLC_BYTES_24:
         ucDLC = 24;
         break;
      case FDCAN_DLC_BYTES_32:
         ucDLC = 32;
         break;
      case FDCAN_DLC_BYTES_48:
         ucDLC = 48;
         break;
      default:
         ucDLC = 64;
         break;
   }
   return ucDLC;
}

/* @fn en_pass_fail CAN1_UnloadFromRB(st_CAN_msg *pstRxMsg)
 * @brief Function to access the next element of the CAN RX ring buffer
 * @param pstRxMsg pointer to message structure to unload the oldest Rx message to
 * @return returns 1 if unsuccessful, 0 otherwise
 */
en_pass_fail CAN1_UnloadFromRB(st_CAN_msg *pstRxMsg)
{
   en_pass_fail bError = FAIL;
   /* From HAL_FDCAN_GetRxFifoFillLevel, get Rx FIFO 0 fill level */
   uint32_t uiFillLevel = FDCAN1->RXF0S & FDCAN_RXF0S_F0FL; // Number of unread elements in the buffer
   if( uiFillLevel ) /* If there is an element in the fifo */
   {
      // If the RX buffer is ever full, assume Rx overflow. todo replace with interrupt
      if( uiFillLevel == CAN_RX_RING_BUFFER_SIZE )
      {
         auwStatusCounter_CAN1[CAN_COUNTER__RX_OVERFLOW]++;
      }

      FDCAN_RxHeaderTypeDef stHeader;
      /* todo, simplify HAL_FDCAN_GetRxMessage */
      if( HAL_FDCAN_GetRxMessage(&hfdcan1, FDCAN_RX_FIFO0, &stHeader, &pstRxMsg->unData.auc8[0]) == HAL_OK )
      {
         pstRxMsg->uiID = stHeader.Identifier;
         pstRxMsg->bCAN_FD = stHeader.FDFormat != FDCAN_CLASSIC_CAN;
         pstRxMsg->bExtendedID = stHeader.IdType == FDCAN_EXTENDED_ID;
         pstRxMsg->ucDLC = CAN_HardwareDLCToAppDLC(stHeader.DataLength);
         auwStatusCounter_CAN1[CAN_COUNTER__RX_PACKET]++;
         bError = PASS;
      }
   }
   return bError;
}

/* @fn en_pass_fail CAN1_LoadToRB(st_CAN_msg *pstTxMsg)
 * @brief Function to load a new element to the CAN TX ring buffer
 * @param pstTxMsg pointer to message structure to load to the buffer
 * @return returns 1 if unsuccessful, 0 otherwise
 */
en_pass_fail CAN1_LoadToRB(st_CAN_msg *pstTxMsg)
{
   en_pass_fail bError = FAIL;
   uint32_t uiFreeSlots = FDCAN1->TXFQS & FDCAN_TXFQS_TFFL; // From HAL_FDCAN_GetTxFifoFreeLevel
   if( uiFreeSlots )
   {
      FDCAN_TxHeaderTypeDef stHeader;
      stHeader.Identifier = pstTxMsg->uiID;
      stHeader.DataLength = CAN_AppDLCToHardwareDLC(pstTxMsg->ucDLC);
      stHeader.IdType = ( pstTxMsg->bExtendedID ) ? FDCAN_EXTENDED_ID : FDCAN_STANDARD_ID;
      stHeader.FDFormat = ( pstTxMsg->bCAN_FD ) ? FDCAN_FD_CAN : FDCAN_CLASSIC_CAN;
#if CAN1_ENABLE_BITRATE_SWITCHING
      stHeader.BitRateSwitch = FDCAN_BRS_ON;
#else
      stHeader.BitRateSwitch = FDCAN_BRS_OFF;
#endif
      stHeader.ErrorStateIndicator = FDCAN_ESI_PASSIVE; /*!< Transmitting node is error passive */
      stHeader.MessageMarker = 0;
      stHeader.TxEventFifoControl = FDCAN_NO_TX_EVENTS; /*!< Do not store Tx events */
      stHeader.TxFrameType = FDCAN_DATA_FRAME; /* System will not use remote frame types */
      /* todo Simplify HAL_FDCAN_AddMessageToTxFifoQ or HAL_FDCAN_AddMessageToTxBuffer or FDCAN_CopyMessageToRAM */
      if( HAL_FDCAN_AddMessageToTxFifoQ(&hfdcan1, &stHeader, &pstTxMsg->unData.auc8[0]) == HAL_OK )
      {
         auwStatusCounter_CAN1[CAN_COUNTER__TX_PACKET]++;
         bError = PASS;
      }
   }
   else
   {
      auwStatusCounter_CAN1[CAN_COUNTER__TX_OVERFLOW]++;
   }
   return bError;
}
/* @fn en_pass_fail CAN2_UnloadFromRB(st_CAN_msg *pstRxMsg)
 * @brief Function to access the next element of the CAN RX ring buffer
 * @param pstRxMsg pointer to message structure to unload the oldest Rx message to
 * @return returns 1 if unsuccessful, 0 otherwise
 */
en_pass_fail CAN2_UnloadFromRB(st_CAN_msg *pstRxMsg)
{
   en_pass_fail bError = FAIL;
   /* From HAL_FDCAN_GetRxFifoFillLevel, get Rx FIFO 0 fill level */
   uint32_t uiFillLevel = FDCAN2->RXF0S & FDCAN_RXF0S_F0FL; // Number of unread elements in the buffer
   if( uiFillLevel ) /* If there is an element in the fifo */
   {
      // If the RX buffer is ever full, assume Rx overflow. todo replace with interrupt
      if( uiFillLevel == CAN_RX_RING_BUFFER_SIZE )
      {
         auwStatusCounter_CAN2[CAN_COUNTER__RX_OVERFLOW]++;
      }

      FDCAN_RxHeaderTypeDef stHeader;
      /* todo, simplify HAL_FDCAN_GetRxMessage */
      if( HAL_FDCAN_GetRxMessage(&hfdcan2, FDCAN_RX_FIFO0, &stHeader, &pstRxMsg->unData.auc8[0]) == HAL_OK )
      {
         pstRxMsg->uiID = stHeader.Identifier;
         pstRxMsg->bCAN_FD = stHeader.FDFormat != FDCAN_CLASSIC_CAN;
         pstRxMsg->bExtendedID = stHeader.IdType == FDCAN_EXTENDED_ID;
         pstRxMsg->ucDLC = CAN_HardwareDLCToAppDLC(stHeader.DataLength);
         auwStatusCounter_CAN2[CAN_COUNTER__RX_PACKET]++;
         bError = PASS;
      }
   }
   return bError;
}

/* @fn en_pass_fail CAN2_LoadToRB(st_CAN_msg *pstTxMsg)
 * @brief Function to load a new element to the CAN TX ring buffer
 * @param pstTxMsg pointer to message structure to load to the buffer
 * @return returns 1 if unsuccessful, 0 otherwise
 */
en_pass_fail CAN2_LoadToRB(st_CAN_msg_8B *pstTxMsg)
{
   en_pass_fail bError = FAIL;
   uint32_t uiFreeSlots = FDCAN2->TXFQS & FDCAN_TXFQS_TFFL; // From HAL_FDCAN_GetTxFifoFreeLevel
   if( uiFreeSlots )
   {
      FDCAN_TxHeaderTypeDef stHeader;
      stHeader.Identifier = pstTxMsg->uiID;
      stHeader.DataLength = CAN_AppDLCToHardwareDLC(pstTxMsg->ucDLC);
      stHeader.IdType = FDCAN_STANDARD_ID;
      stHeader.FDFormat = FDCAN_CLASSIC_CAN;

      stHeader.BitRateSwitch = FDCAN_BRS_OFF;
      stHeader.ErrorStateIndicator = FDCAN_ESI_PASSIVE; /*!< Transmitting node is error passive */
      stHeader.MessageMarker = 0;
      stHeader.TxEventFifoControl = FDCAN_NO_TX_EVENTS; /*!< Do not store Tx events */
      stHeader.TxFrameType = FDCAN_DATA_FRAME; /* System will not use remote frame types */
      /* todo Simplify HAL_FDCAN_AddMessageToTxFifoQ or HAL_FDCAN_AddMessageToTxBuffer */
      if( HAL_FDCAN_AddMessageToTxFifoQ(&hfdcan2, &stHeader, &pstTxMsg->unData.auc8[0]) == HAL_OK )
      {
         auwStatusCounter_CAN2[CAN_COUNTER__TX_PACKET]++;
         bError = PASS;
      }
   }
   else
   {
      auwStatusCounter_CAN2[CAN_COUNTER__TX_OVERFLOW]++;
   }
   return bError;
}
/* @fn void CAN1_Init(void)
 * @brief Initializes CAN 1 bus peripheral and buffers
 * @param None
 * @return None
 */
void CAN1_Init(void)
{
   /* Setup ring buffers (built into ram for this chip) */

   /* Initialize peripheral */
   hfdcan1.Instance = FDCAN1;
#if CAN1_ENABLE_BITRATE_SWITCHING
   hfdcan1.Init.FrameFormat = FDCAN_FRAME_FD_BRS;
#else
   hfdcan1.Init.FrameFormat = FDCAN_FRAME_FD_NO_BRS;
#endif
   hfdcan1.Init.Mode = FDCAN_MODE_NORMAL;
   hfdcan1.Init.AutoRetransmission = ENABLE;
   hfdcan1.Init.TransmitPause = ENABLE;
   hfdcan1.Init.ProtocolException = ENABLE;
   hfdcan1.Init.NominalPrescaler = astBitrateConfig[CAN1_BAUD_RATE].ucPrescale;
   hfdcan1.Init.NominalSyncJumpWidth = astBitrateConfig[CAN1_BAUD_RATE].ucSyncJumpWidth;
   hfdcan1.Init.NominalTimeSeg1 = astBitrateConfig[CAN1_BAUD_RATE].ucTimeSeg1;
   hfdcan1.Init.NominalTimeSeg2 = astBitrateConfig[CAN1_BAUD_RATE].ucTimeSeg2;
   hfdcan1.Init.DataPrescaler = astBitrateConfig[CAN1_BAUD_BRS_RATE].ucPrescale;
   hfdcan1.Init.DataSyncJumpWidth = astBitrateConfig[CAN1_BAUD_BRS_RATE].ucSyncJumpWidth;
   hfdcan1.Init.DataTimeSeg1 = astBitrateConfig[CAN1_BAUD_BRS_RATE].ucTimeSeg1;
   hfdcan1.Init.DataTimeSeg2 = astBitrateConfig[CAN1_BAUD_BRS_RATE].ucTimeSeg2;
   hfdcan1.Init.MessageRAMOffset = 0;
   hfdcan1.Init.StdFiltersNbr = 0;
   hfdcan1.Init.ExtFiltersNbr = 0;
   hfdcan1.Init.RxFifo0ElmtsNbr = CAN_RX_RING_BUFFER_SIZE;
   hfdcan1.Init.RxFifo0ElmtSize = FDCAN_DATA_BYTES_64;
   hfdcan1.Init.RxFifo1ElmtsNbr = 0;
   hfdcan1.Init.RxFifo1ElmtSize = FDCAN_DATA_BYTES_64;
   hfdcan1.Init.RxBuffersNbr = 0;
   hfdcan1.Init.RxBufferSize = FDCAN_DATA_BYTES_64;
   hfdcan1.Init.TxEventsNbr = 0;
   hfdcan1.Init.TxBuffersNbr = 0;
   hfdcan1.Init.TxFifoQueueElmtsNbr = CAN_TX_RING_BUFFER_SIZE;
   hfdcan1.Init.TxFifoQueueMode = FDCAN_TX_FIFO_OPERATION;
   hfdcan1.Init.TxElmtSize = FDCAN_DATA_BYTES_64;
   if (HAL_FDCAN_Init(&hfdcan1) != HAL_OK)
   {
     Error_Handler();
   }
   SET_BIT(FDCAN_CCU->CCFG, FDCANCCU_CCFG_BCC); // Bypass automatic clock calibration for now.

   /* Transmission will be ensured by minimum resend rates.
    * On FIFO overrun configure the controller to overwrite
    * the oldest data */
   HAL_FDCAN_ConfigRxFifoOverwrite(&hfdcan1, FDCAN_RX_FIFO0, FDCAN_RX_FIFO_OVERWRITE);

   /* todo investigate tx delay compensation. HAL_FDCAN_ConfigTxDelayCompensation */

   /* Start peripheral */
   HAL_FDCAN_Start(&hfdcan1);
}
/* @fn void CAN2_Init(void)
 * @brief Initializes CAN 2 bus peripheral and buffers
 * @param None
 * @return None
 */
void CAN2_Init(void)
{
   /* Setup ring buffers (built into ram for this chip) */
//   CAN2_Init_RingBuffer();

//   FDCAN_FLAG_BUS_OFF
//   FDCAN_IT_RX_FIFO0_MESSAGE_LOST
//   FDCAN_IT_BUS_OFF
   //__HAL_FDCAN_ENABLE_IT, HAL_FDCAN_ConfigInterruptLines
   // __HAL_FDCAN_GET_IT
   // __HAL_FDCAN_CLEAR_IT
//HAL_FDCAN_EnableEdgeFiltering ?
   //HAL_FDCAN_EnableISOMode
   /* Bus offline recovery:
    * The Bus_Off recovery sequence (see CAN Specification Rev. 2.0 or ISO11898-1) cannot be
    * shortened by setting or resetting FDCAN_CCCR.INIT. If the device goes Bus_Off, it will set
    * FDCAN_CCCR.INIT of its own, stopping all bus activities. Once FDCAN_CCCR.INIT has
    * been cleared by the CPU, the device will then wait for 129 occurrences of bus Idle (129 × 11
    * consecutive recessive bits) before resuming normal operation. At the end of the Bus_Off
    * recovery sequence, the error management counters will be reset. During the waiting time
    * after the reset of FDCAN_CCCR.INIT, each time a sequence of 11 recessive bits has been
    * monitored, a Bit0 error code is written to FDCAN_PSR.LEC, enabling the CPU to readily
    * check up whether the CAN bus is stuck at dominant or continuously disturbed and to
    * monitor the Bus_Off recovery sequence. FDCAN_ECR.REC is used to count these
    * sequences.
    *
    * Detect CAN bus offline with FDCAN_PSR protocol status register, bit 7 BO
    * Error passive bit 5 EP?
    *
    * Activity B4:3 ACT
    *
    * */
   /* Initialize peripheral */
   hfdcan2.Instance = FDCAN2;
   hfdcan2.Init.FrameFormat = FDCAN_FRAME_CLASSIC;
   hfdcan2.Init.Mode = FDCAN_MODE_NORMAL;
   hfdcan2.Init.AutoRetransmission = ENABLE;
   hfdcan2.Init.TransmitPause = ENABLE;
   hfdcan2.Init.ProtocolException = ENABLE;
   hfdcan2.Init.NominalPrescaler = astBitrateConfig[CAN2_BAUD_RATE].ucPrescale;
   hfdcan2.Init.NominalSyncJumpWidth = astBitrateConfig[CAN2_BAUD_RATE].ucSyncJumpWidth;
   hfdcan2.Init.NominalTimeSeg1 = astBitrateConfig[CAN2_BAUD_RATE].ucTimeSeg1;
   hfdcan2.Init.NominalTimeSeg2 = astBitrateConfig[CAN2_BAUD_RATE].ucTimeSeg2;
   hfdcan2.Init.DataPrescaler = astBitrateConfig[CAN2_BAUD_RATE].ucPrescale;
   hfdcan2.Init.DataSyncJumpWidth = astBitrateConfig[CAN2_BAUD_RATE].ucSyncJumpWidth;
   hfdcan2.Init.DataTimeSeg1 = astBitrateConfig[CAN2_BAUD_RATE].ucTimeSeg1;
   hfdcan2.Init.DataTimeSeg2 = astBitrateConfig[CAN2_BAUD_RATE].ucTimeSeg2;
   hfdcan2.Init.MessageRAMOffset = 1280; // Both can bus peripherals share a message RAM space, this setting splits this space in 2
   hfdcan2.Init.StdFiltersNbr = 0;
   hfdcan2.Init.ExtFiltersNbr = 0;
   hfdcan2.Init.RxFifo0ElmtsNbr = CAN_RX_RING_BUFFER_SIZE;
   hfdcan2.Init.RxFifo0ElmtSize = FDCAN_DATA_BYTES_8;
   hfdcan2.Init.RxFifo1ElmtsNbr = 0;
   hfdcan2.Init.RxFifo1ElmtSize = FDCAN_DATA_BYTES_8;
   hfdcan2.Init.RxBuffersNbr = 0;
   hfdcan2.Init.RxBufferSize = FDCAN_DATA_BYTES_8;
   hfdcan2.Init.TxEventsNbr = 0;
   hfdcan2.Init.TxBuffersNbr = 0;
   hfdcan2.Init.TxFifoQueueElmtsNbr = CAN_TX_RING_BUFFER_SIZE;
   hfdcan2.Init.TxFifoQueueMode = FDCAN_TX_FIFO_OPERATION;
   hfdcan2.Init.TxElmtSize = FDCAN_DATA_BYTES_8;
   if (HAL_FDCAN_Init(&hfdcan2) != HAL_OK)
   {
     Error_Handler();
   }
   SET_BIT(FDCAN_CCU->CCFG, FDCANCCU_CCFG_BCC); // Bypass automatic clock calibration for now.
   /* Transmission will be ensured by minimum resend rates.
    * On FIFO overrun configure the controller to overwrite
    * the oldest data */
   if(HAL_FDCAN_ConfigRxFifoOverwrite(&hfdcan2, FDCAN_RX_FIFO0, FDCAN_RX_FIFO_OVERWRITE) != HAL_OK)
   {
	   Error_Handler();
   }
   /* todo investigate tx delay compensation. HAL_FDCAN_ConfigTxDelayCompensation */

   /* Enable RX overflow isr detection */
//   HAL_FDCAN_ConfigInterruptLines(&hfdcan2, FDCAN_IT_RX_FIFO0_MESSAGE_LOST, FDCAN_INTERRUPT_LINE0);

   /* Start peripheral */
   if(HAL_FDCAN_Start(&hfdcan2) != HAL_OK)
   {
	   Error_Handler();
   }
}
/* @fn void HAL_FDCAN_MspInit(FDCAN_HandleTypeDef* hfdcan)
 * @brief Initializes CAN pins and clocks. Auto generated by stm32cubeIDE tool
 * @param hfdcan pointer to a CAN configuration structure
 * @return None
 */
static uint32_t HAL_RCC_FDCAN_CLK_ENABLED;
void HAL_FDCAN_MspInit(FDCAN_HandleTypeDef* fdcanHandle)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(fdcanHandle->Instance==FDCAN1)
  {
  /* USER CODE BEGIN FDCAN1_MspInit 0 */

  /* USER CODE END FDCAN1_MspInit 0 */
     /* Peripheral clock enable */
     HAL_RCC_FDCAN_CLK_ENABLED++;
     if(HAL_RCC_FDCAN_CLK_ENABLED==1){
       __HAL_RCC_FDCAN_CLK_ENABLE();
     }

     __HAL_RCC_GPIOD_CLK_ENABLE();
     /**FDCAN1 GPIO Configuration
     PB8     ------> FDCAN1_RX
     PB9     ------> FDCAN1_TX
     */
     GPIO_InitStruct.Pin = GPIO_PIN_0|GPIO_PIN_1;
     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
     GPIO_InitStruct.Pull = GPIO_NOPULL;
     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
     GPIO_InitStruct.Alternate = GPIO_AF9_FDCAN1;
     HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);

  /* USER CODE BEGIN FDCAN1_MspInit 1 */

  /* USER CODE END FDCAN1_MspInit 1 */
  }
  else if(fdcanHandle->Instance==FDCAN2)
  {
  /* USER CODE BEGIN FDCAN2_MspInit 0 */

  /* USER CODE END FDCAN2_MspInit 0 */
     /* Peripheral clock enable */
     HAL_RCC_FDCAN_CLK_ENABLED++;
     if(HAL_RCC_FDCAN_CLK_ENABLED==1){
       __HAL_RCC_FDCAN_CLK_ENABLE();
     }

     __HAL_RCC_GPIOB_CLK_ENABLE();
     /**FDCAN2 GPIO Configuration
     PB13     ------> FDCAN2_TX
     PB5     ------> FDCAN2_RX
     */
     GPIO_InitStruct.Pin = GPIO_PIN_5|GPIO_PIN_6;
     GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
     GPIO_InitStruct.Pull = GPIO_NOPULL;
     GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
     GPIO_InitStruct.Alternate = GPIO_AF9_FDCAN2;
     HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* FDCAN2 interrupt Init */

  /* USER CODE BEGIN FDCAN2_MspInit 1 */

  /* USER CODE END FDCAN2_MspInit 1 */
  }
}
/* @fn void HAL_FDCAN_MspDeInit(FDCAN_HandleTypeDef* hfdcan)
 * @brief Initializes CAN pins and clocks. Auto generated by stm32cubeIDE tool
 * @param hfdcan pointer to a CAN configuration structure
 * @return None
 */
void HAL_FDCAN_MspDeInit(FDCAN_HandleTypeDef* fdcanHandle)
{
  if(fdcanHandle->Instance==FDCAN1)
  {
  /* USER CODE BEGIN FDCAN1_MspDeInit 0 */

  /* USER CODE END FDCAN1_MspDeInit 0 */
     /* Peripheral clock disable */
     HAL_RCC_FDCAN_CLK_ENABLED--;
     if(HAL_RCC_FDCAN_CLK_ENABLED==0){
       __HAL_RCC_FDCAN_CLK_DISABLE();
     }

     /**FDCAN1 GPIO Configuration
     PB8     ------> FDCAN1_RX
     PB9     ------> FDCAN1_TX
     */
     HAL_GPIO_DeInit(GPIOD, GPIO_PIN_0|GPIO_PIN_1);

    /* FDCAN1 interrupt Deinit */

  /* USER CODE BEGIN FDCAN1_MspDeInit 1 */

  /* USER CODE END FDCAN1_MspDeInit 1 */
  }
  else if(fdcanHandle->Instance==FDCAN2)
  {
  /* USER CODE BEGIN FDCAN2_MspDeInit 0 */

  /* USER CODE END FDCAN2_MspDeInit 0 */
     /* Peripheral clock disable */
     HAL_RCC_FDCAN_CLK_ENABLED--;
     if(HAL_RCC_FDCAN_CLK_ENABLED==0){
       __HAL_RCC_FDCAN_CLK_DISABLE();
     }

     /**FDCAN2 GPIO Configuration
     PB13     ------> FDCAN2_TX
     PB5     ------> FDCAN2_RX
     */
     HAL_GPIO_DeInit(GPIOB, GPIO_PIN_5|GPIO_PIN_6);

    /* FDCAN2 interrupt Deinit */

  /* USER CODE BEGIN FDCAN2_MspDeInit 1 */

  /* USER CODE END FDCAN2_MspDeInit 1 */
  }
}

