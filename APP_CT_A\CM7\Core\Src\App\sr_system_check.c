#include "app.h"
#include "param_def.h"
#include "ets.h"
#include "car_faults_def.h"
#include "operation_def.h"

#define SYSTEM_CHECK_INITIAL_DELAY_1MS 4000

#define LEARN_BYPASS_CHECK    (Operation_GetSemiAutoOperation() == SEMIAUTO_OP__LEARN)
#define MANUAL_BYPASS_CHECK   ( (Operation_GetManualOperation() != MANUAL_OP__OFF) && \
    (Param_ReadValue_1Bit(CPARAM1__Bypass_Landing_System_Feedback) == 1) )

extern ets_t ETS;

void SystemCheck_ETSLearnParams(void)
{
  ETS_LoadPoint(&ETS.UETS,
      CPARAM24__Traction_UETS_Pos,
      CPARAM16__Traction_UETS_Speed);
  ETS_LoadPoint(&ETS.DETS,
      CPARAM24__Traction_DETS_Pos,
      CPARAM16__Traction_DETS_Speed);

  // Bypass completely if we are trying to learn or if manual mode is on AND
  // 01-033 is ON
  bool learn_bypass  = LEARN_BYPASS_CHECK  ? true : false;
  bool manual_bypass = MANUAL_BYPASS_CHECK ? true : false;
  if((learn_bypass  == false) &&
     (manual_bypass == false))
  {
    if(ETS_CheckETSPoint(&ETS.UETS) == ACTIVE)
    {
      Faults_SetFault(CFLT__CONFIG_UETS);
    }

    if(ETS_CheckETSPoint(&ETS.DETS) == ACTIVE)
    {
      Faults_SetFault(CFLT__CONFIG_DETS);
    }
  }
}

void vSystemCheckTask(void *pvArgs)
{
  vTaskDelay(SYSTEM_CHECK_INITIAL_DELAY_1MS);

  while(1)
  {
    if( Param_GetState() != PARAM_STATE__STARTUP )
    {
      // Check NTS parameters
      SystemCheck_ETSLearnParams();
    }

    vTaskDelay(SYSTEM_CHECK_TASK_DELAY);
  }
}


