@echo off
echo Building Bootloader_STMH7_NoUI...

REM STM32CubeIDE paths (your specific version)
set STM32CUBEIDE_PATH=C:\ST\STM32CubeIDE_1.12.1\STM32CubeIDE
set TOOLCHAIN_PATH=C:\Program Files (x86)\GNU Arm Embedded Toolchain\10 2021.10\bin

echo Using STM32CubeIDE at: %STM32CUBEIDE_PATH%

REM Find make in STM32CubeIDE installation
setlocal enabledelayedexpansion
for /d %%i in ("%STM32CUBEIDE_PATH%\plugins\com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.*") do (
    if exist "%%i\tools\bin\make.exe" (
        set MAKE_PATH=%%i\tools\bin
        goto found_make
    )
)

echo Error: Could not find make in STM32CubeIDE installation
echo Searched in: %STM32CUBEIDE_PATH%\plugins\
pause
exit /b 1

:found_make
echo Found make at: %MAKE_PATH%
set "PATH=%MAKE_PATH%;%TOOLCHAIN_PATH%;%PATH%"

REM Clean previous build
echo Cleaning previous build...
make clean

REM Build both CM7 and CM4 cores
echo Building CM7 and CM4 cores...
make all

REM Check if build was successful
if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build completed successfully!
    echo Output files:
    echo - build/CM7/Bootloader_STM32H7_NoUI_CM7.elf
    echo - build/CM7/Bootloader_STM32H7_NoUI_CM7.hex
    echo - build/CM7/Bootloader_STM32H7_NoUI_CM7.bin
    echo - build/CM4/Bootloader_STM32H7_NoUI_CM4.elf
    echo - build/CM4/Bootloader_STM32H7_NoUI_CM4.hex
    echo - build/CM4/Bootloader_STM32H7_NoUI_CM4.bin
) else (
    echo.
    echo Build failed with error code %ERRORLEVEL%
)

pause

