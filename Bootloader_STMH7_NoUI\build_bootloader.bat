@echo off
echo Building Bootloader_STMH7_NoUI...

REM STM32CubeIDE paths (your specific version)
set STM32CUBEIDE_PATH=C:\ST\STM32CubeIDE_1.12.1\STM32CubeIDE

echo Using STM32CubeIDE at: %STM32CUBEIDE_PATH%

REM Try multiple locations for build tools
setlocal enabledelayedexpansion
set MAKE_PATH=
set TOOLCHAIN_PATH=

REM Method 1: Look for make in STM32CubeIDE plugins
echo Searching for GNU tools in STM32CubeIDE plugins...
for /d %%i in ("%STM32CUBEIDE_PATH%\plugins\com.st.stm32cube.ide.mcu.externaltools.gnu-tools-for-stm32.*") do (
    echo Checking: %%i
    if exist "%%i\tools\bin\make.exe" (
        echo Found make.exe in: %%i\tools\bin\
        set MAKE_PATH=%%i\tools\bin
        set TOOLCHAIN_PATH=%%i\tools\bin
        goto found_tools
    )
    REM Try alternative path structure
    if exist "%%i\bin\make.exe" (
        echo Found make.exe in: %%i\bin\
        set MAKE_PATH=%%i\bin
        set TOOLCHAIN_PATH=%%i\bin
        goto found_tools
    )
)

REM Method 2: Look for make in STM32CubeIDE installation directory
echo Searching for make in STM32CubeIDE installation...
for /r "%STM32CUBEIDE_PATH%" %%i in (make.exe) do (
    echo Found make.exe at: %%i
    for %%j in ("%%i") do set MAKE_PATH=%%~dpj
    set MAKE_PATH=!MAKE_PATH:~0,-1!
    set TOOLCHAIN_PATH=!MAKE_PATH!
    goto found_tools
)

REM Method 3: Try to use system make or mingw make
echo Searching for system make...
where make.exe >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo Found system make
    set MAKE_PATH=
    set TOOLCHAIN_PATH=
    goto found_tools
)

echo Error: Could not find make anywhere!
echo Tried:
echo - STM32CubeIDE plugins directory
echo - STM32CubeIDE installation directory
echo - System PATH
echo.
echo Please install one of the following:
echo - GNU Arm Embedded Toolchain
echo - MinGW-w64
echo - Or ensure STM32CubeIDE includes build tools
pause
exit /b 1

:found_tools
if defined MAKE_PATH (
    echo Found build tools at: %MAKE_PATH%
    set "PATH=%MAKE_PATH%;%PATH%"
) else (
    echo Using system make
)

REM Clean previous build
echo Cleaning previous build...
make clean

REM Build both CM7 and CM4 cores
echo Building CM7 and CM4 cores...
make all

REM Check if build was successful
if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build completed successfully!
    echo Output files:
    echo - build/CM7/Bootloader_STM32H7_NoUI_CM7.elf
    echo - build/CM7/Bootloader_STM32H7_NoUI_CM7.hex
    echo - build/CM7/Bootloader_STM32H7_NoUI_CM7.bin
    echo - build/CM4/Bootloader_STM32H7_NoUI_CM4.elf
    echo - build/CM4/Bootloader_STM32H7_NoUI_CM4.hex
    echo - build/CM4/Bootloader_STM32H7_NoUI_CM4.bin
) else (
    echo.
    echo Build failed with error code %ERRORLEVEL%
)

pause

