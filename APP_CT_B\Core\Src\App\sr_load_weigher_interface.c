/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_load_weigher_interface.c
* @version 		  : 1.0.0
* @brief Subroutine handling CAN communication with LWD
* @details Subroutine handling CAN communication with LWD
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "app_can.h"
#include "sr_CAN2.h"
#include "sr_load_weigher_interface.h"

#include "system.h"
#include "operation_def.h"
#include "dg_car_ct_b4.h"
#include "ring_buffer.h"
#include "load_weigher_def.h"

/******************************************************************************
* Function Prototypes
*******************************************************************************/
/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define NUM_WEIGHT_SAMPLES                      (8)
#define LW_OFFLINE_TIMER                        (3000)

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static volatile int16_t uiWeightSamples[NUM_WEIGHT_SAMPLES];
static int32_t uiRunningSum;
static volatile RINGBUFF_T stWeightSamplesRing;
static uint32_t uiTimeSinceLastZero; //timer prevents multiple zero requests being sent to LW within a short time which can causes issues
static uint32_t uiTimeSinceLastWeightReading;

/******************************************************************************
* Function Definitions
*******************************************************************************/
void LoadWeigher_NewReading(int16_t uiWeight)
{
    if ( RingBuffer_IsFull((RINGBUFF_T*) &stWeightSamplesRing) )
    {
        int16_t uwReading;
        if( RingBuffer_Pop((RINGBUFF_T*) &stWeightSamplesRing, &uwReading) )
        {
            uiRunningSum -= uwReading;
        }
    }
    
    if (RingBuffer_Insert((RINGBUFF_T*) &stWeightSamplesRing, &uiWeight) )
    {
        uiRunningSum += uiWeight;
        uiTimeSinceLastWeightReading = 0;
    }
    
}

int16_t LoadWeigher_GetAverageReading()
{
    uint8_t ucNumReadings = RingBuffer_GetCount((RINGBUFF_T*) &stWeightSamplesRing);
    if ( ucNumReadings > 0 )
    {
        return ( uiRunningSum / ucNumReadings);
    }
    return 0;
}

/* @brief sends CAN message to LW telling it to set current weight reading to 0 */
void LoadWeigher_ZeroLoadWeigher(void)
{
    st_CAN_msg stTxMsg;
    stTxMsg.bExtendedID = INACTIVE;
    stTxMsg.bCAN_FD = INACTIVE;
    stTxMsg.uiID = DINACELL_LWD_WRITE_PARAMETER;
    stTxMsg.unData.auc8[1] = 0x17; //Op code for Zero Cabin (from Dinacell documentation)
    stTxMsg.ucDLC = 2;
    CAN2_LoadToRB(&stTxMsg);
    
    uiTimeSinceLastZero = 0;
}

/* @fn void LoadWeigher_Init(void)
 * @brief Init function for LoadWeigher Init
 * @param None
 * @return None
 */
void LoadWeigher_Init(void)
{
    //TO DO get readings from CT A FRAM
    RingBuffer_Init((RINGBUFF_T*) &stWeightSamplesRing, (int16_t*) uiWeightSamples, sizeof(int16_t), NUM_WEIGHT_SAMPLES);
    CAN2_Init();
}

/* @fn void vLoadWeigher_Interface_Task(void *pvParameters)
 * @brief place holder
 * @param pvParameters Pointer to parameters passed to the task
 * @return None
 */
void vLoadWeigher_Interface_Task(void *pvParameters)
{
    // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{

		if ( LoadWeigher_GetCalibrationRequest() == ACTIVE && uiTimeSinceLastZero > 5000 )
		{
			LoadWeigher_ZeroLoadWeigher();
		}
        else
        {
            uiTimeSinceLastZero += LW_INTERFACE_TASK_DELAY;
        }
        
        uiTimeSinceLastWeightReading += LW_INTERFACE_TASK_DELAY;
        if ( uiTimeSinceLastWeightReading < LW_OFFLINE_TIMER )
        {
            LoadWeigher_SetOnlineStatus(ACTIVE);
        }
        else LoadWeigher_SetOnlineStatus(INACTIVE);
        
		vTaskDelay(LW_INTERFACE_TASK_DELAY);
	}
}
