/*******************************************************************************
* @Copyright (C) 2022 by Vantage Elevation
* @file           : app.h
* @version 		  : 1.0.0
* @brief          : Main program body
* @details		  : Main program body declarations
********************************************************************************/
#ifndef _APP_H_
#define _APP_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include "FreeRTOS.h"
#include "task.h"

#define DEBUG_DELAY 0
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/

/*******************************************************************************
* Configuration Constants
********************************************************************************/
/* Idle task period tasks*/
#define LED_TICKS_PERIOD				1000
#define LOW_PRIORITY_TICKS_PERIOD		3000
#define RTC_TICKS_PERIOD				1000
#define WATCHDOG_TICKS_PERIOD			2000


/*******************************************************************************
* Macros for RTOS Task Priority
********************************************************************************/
#define FAULT_TASK_PRIORITY			(tskIDLE_PRIORITY)
#define WATCHDOG_TASK_PRIORITY      (tskIDLE_PRIORITY)
#define CAN1_TASK_PRIORITY          (tskIDLE_PRIORITY)
#define CAN2_TASK_PRIORITY          (tskIDLE_PRIORITY)
#define LOCAL_INPUTS_TASK_PRIORITY (tskIDLE_PRIORITY)
#define LOCAL_OUTPUTS_TASK_PRIORITY (tskIDLE_PRIORITY)
#define CARNET_TASK_PRIORITY        (tskIDLE_PRIORITY)
#define SPIAU_TASK_PRIORITY         (tskIDLE_PRIORITY)
#define PARAMETERS_TASK_PRIORITY    (tskIDLE_PRIORITY)
#define HALL_NET_TASK_PRIORITY      (tskIDLE_PRIORITY)
#define CAN3_TASK_PRIORITY          (tskIDLE_PRIORITY)
#define FIXTURE_NET_1_TASK_PRIORITY (tskIDLE_PRIORITY)
#define FIXTURE_NET_2_TASK_PRIORITY (tskIDLE_PRIORITY)

/*******************************************************************************
* Macros for RTOS Task Stack Size :
********************************************************************************/
#define INIT_TASK_STACK_SIZE  		 configMINIMAL_STACK_SIZE
#define FAULT_TASK_STACK_SIZE         (128)
#define WATCHDOG_TASK_STACK_SIZE      (128)
#define CAN1_TASK_STACK_SIZE          (128)
#define CAN2_TASK_STACK_SIZE          (128)
#define LOCAL_INPUTS_TASK_STACK_SIZE  (128)
#define LOCAL_OUTPUTS_TASK_STACK_SIZE (128)
#define CARNET_TASK_STACK_SIZE        (2048)
#define SPIAU_TASK_STACK_SIZE         (128)
#define PARAMETERS_TASK_STACK_SIZE    (128)
#define HALL_NET_TASK_STACK_SIZE      (128)
#define CAN3_TASK_STACK_SIZE          (128)
#define FIXTURE_NET_1_TASK_STACK_SIZE (128)
#define FIXTURE_NET_2_TASK_STACK_SIZE (128)

/*******************************************************************************
* Macros for RTOS Task Delay :
********************************************************************************/
#define TASK_IDLE_DELAY					100
#define FAULT_TASK_DELAY				50
#define NTS_TASK_DELAY					100
#define POSITION_TASK_DELAY				2
#define CAR_NET_TASK_DELAY				5
#define CAN1_TASK_DELAY					3
#define CAN2_TASK_DELAY					3
#define SPIAB_TASK_DELAY				5
#define PARAMETERS_TASK_DELAY			10

/*******************************************************************************
* Macros for RTOS Idle Task Period :
********************************************************************************/
// LED Task
#define LED_TASK_FIRST_DELAY_MS        0
#define LED_TASK_PERIODIC_DELAY_MS    100

// Watchdog Task
#define WATCHDOG_TASK_FIRST_DELAY_MS    0
#define WATCHDOG_TASK_PERIODIC_DELAY_MS 500

// RTC Task
#define RTC_TASK_FIRST_DELAY_MS       10
#define RTC_TASK_PERIODIC_DELAY_MS  1000

/*******************************************************************************
* Macros for RTOS vTask Period :
********************************************************************************/
//Inactive Interval Common for all
#define TASK_RUN_INTERVAL_INACTIVE_MS 	(0xFFFF)

//Alarm  Task
#define ALARM_TASK_FIRST_DELAY_MS    1000
#define ALARM_TASK_PERIODIC_DELAY_MS  50

// Fault Task
#define FAULT_TASK_FIRST_DELAY_MS    1000
#define FAULT_TASK_PERIODIC_DELAY_MS  50

// CAN1 Task
#define CAN1_TASK_COP_FIRST_DELAY_MS    0
#define CAN1_TASK_EXP_FIRST_DELAY_MS    0
#define CAN1_TASK_RIS_FIRST_DELAY_MS    2000
#define CAN1_TASK_PERIODIC_DELAY_MS     5

// CAN2 Task
#define CAN2_TASK_FIRST_DELAY_MS       0
#define CAN2_TASK_PERIODIC_DELAY_MS    5

// Local Inputs Task
#define LOCAL_INPUTS_TASK_FIRST_DELAY_MS  0
#define LOCAL_INPUTS_TASK_PERIODIC_DELAY_MS 5

// Local Outputs Task
#define LOCAL_OUTPUTS_TASK_FIRST_DELAY_MS 1000
#define LOCAL_OUTPUTS_TASK_PERIODIC_DELAY_MS 5

// CarNet Task
#define CAR_NET_TASK_COP_FIRST_DELAY_MS    10
#define CAR_NET_TASK_EXP_FIRST_DELAY_MS    10
#define CAR_NET_TASK_RIS_FIRST_DELAY_MS    10
#define CAR_NET_TASK_PERIODIC_DELAY_MS     5

// SPI_AU Task
#define SPI_AU_TASK_FIRST_DELAY_MS     0
#define SPI_AU_TASK_PERIODIC_DELAY_MS  5

// Parameters Task
#define PARAMETERS_TASK_FIRST_DELAY_MS  0
#define PARAMETERS_TASK_PERIODIC_DELAY_MS 100

// Hall_Net Task
#define HALL_NET_TASK_FIRST_DELAY_MS  1000
#define HALL_NET_TASK_PERIODIC_DELAY_MS 50

// CAN3 Task
#define CAN3_TASK_FIRST_DELAY_MS        0
#define CAN3_TASK_PERIODIC_DELAY_MS     5

/*******************************************************************************
* Macros for RTOS Static Buffers for Task Stacks and TCBs :
********************************************************************************/
/* Define Static Buffers for Task Stacks and TCBs */

#define DEFINE_STATIC_TASK(name, stack_size)          \
    StackType_t name##_Stack[stack_size];            \
    StaticTask_t name##_TCB;


/*******************************************************************************
* Typedefs Structure for vTask RTOS :
********************************************************************************/

typedef struct
{
	TaskFunction_t function;
	const char * const name;
	const size_t stack_size;
	void * const parameters;
	UBaseType_t priority;
    StackType_t *const stack_buffer;
    StaticTask_t *const task_control_block;
}TaskParams_t;

/*******************************************************************************
* Global Variables
********************************************************************************/
/*******************************************************************************
* Function Prototypes Idle Task for RTOS:
********************************************************************************/
void vIdleTaskModulesInit(void);

/*******************************************************************************
* Function Prototypes
********************************************************************************/

#endif /* _APP_H_ */
