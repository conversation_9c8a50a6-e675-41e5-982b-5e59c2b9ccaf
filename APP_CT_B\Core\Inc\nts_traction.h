#ifndef INC_NTS_TRACTION_H_
#define INC_NTS_TRACTION_H_

#include "nts_common.h"
#include "param_def.h"

#define NTS_INDEX_RESET(self)  (self.index = 0)

typedef enum
{
  CHECK_UNTS,
  CHECK_DNTS
}nts_check_t;

typedef enum
{
  NTS_STATE__IDLE = NTS_STATE__USER,
  NTS_STATE__CHECK_UNTS,
  NTS_STATE__CHECK_DNTS,
//  NTS_STATE__LEARN_NEEDED
}nts_traction_states_t;

typedef struct
{
  uint8_t count;
  uint32_t position[NTS_TABLE_ROW_SIZE];
  int16_t speed[NTS_TABLE_ROW_SIZE];
  uint32_t index;
}nts_points_t;

typedef struct
{
  nts_common_t super;
  nts_points_t unts;
  nts_points_t dnts;
}nts_traction_t;

void NTS_Traction_Init(nts_traction_t * const self);
void NTS_Traction_Run(nts_traction_t * const self);

void NTS_Traction_LoadPoints(nts_points_t * const self,
    en_car_param_8bit count_param_base,
    en_car_param_24bit pos_param_base,
    en_car_param_16bit speed_param_base);
en_active_inactive NTS_Traction_CheckNTSPoints(nts_points_t  * const self,
    nts_check_t type);

#endif /* INC_NTS_TRACTION_H_ */
