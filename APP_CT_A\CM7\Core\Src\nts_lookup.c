#include "nts_lookup.h"

nts_lut_t NTS_LookupTable[NTS_TABLE_COLUMN_SIZE] =
{
    {.count = 1, .speed = 50,   .positions = {5,     0,   0,   0,   0,   0,  0}}, /* 50 fpm*/
    {.count = 1, .speed = 75,   .positions = {9,     0,   0,   0,   0,   0,  0}}, /* 75 fpm*/
    {.count = 1, .speed = 100,  .positions = {12,    0,   0,   0,   0,   0,  0}}, /* 100 fpm*/
    {.count = 1, .speed = 150,  .positions = {21,    0,   0,   0,   0,   0,  0}}, /* 150 fpm*/
    {.count = 1, .speed = 200,  .positions = {30,    0,   0,   0,   0,   0,  0}}, /* 200 fpm*/
    {.count = 1, .speed = 250,  .positions = {45,    0,   0,   0,   0,   0,  0}}, /* 250 fpm*/
    {.count = 2, .speed = 300,  .positions = {50,   25,   0,   0,   0,   0,  0}}, /* 300 fpm*/
    {.count = 2, .speed = 350,  .positions = {65,   33,   0,   0,   0,   0,  0}}, /* 350 fpm*/
    {.count = 2, .speed = 400,  .positions = {83,   41,   0,   0,   0,   0,  0}}, /* 400 fpm*/
    {.count = 2, .speed = 450,  .positions = {102,  51,   0,   0,   0,   0,  0}}, /* 450 fpm*/
    {.count = 2, .speed = 500,  .positions = {113,  56,   0,   0,   0,   0,  0}}, /* 500 fpm*/
    {.count = 3, .speed = 600,  .positions = {157, 105,  52,   0,   0,   0,  0}}, /* 600 fpm*/
    {.count = 3, .speed = 700,  .positions = {209, 140,  70,   0,   0,   0,  0}}, /* 700 fpm*/
    {.count = 4, .speed = 800,  .positions = {269, 202, 135,  67,   0,   0,  0}}, /* 800 fpm*/
    {.count = 4, .speed = 900,  .positions = {306, 230, 153,  77,   0,   0,  0}}, /* 900 fpm*/
    {.count = 5, .speed = 1000, .positions = {373, 299, 224, 149,  75,   0,  0}}, /* 1000 fpm*/
    {.count = 5, .speed = 1100, .positions = {447, 358, 268, 179,  89,   0,  0}}, /* 1100 fpm*/
    {.count = 6, .speed = 1200, .positions = {528, 440, 352, 264, 176,  88,  0}}, /* 1200 fpm*/
    {.count = 6, .speed = 1300, .positions = {564, 470, 376, 282, 188,  94,  0}}, /* 1300 fpm*/
    {.count = 7, .speed = 1400, .positions = {650, 557, 464, 371, 269, 186, 93}}, /* 1400 fpm*/
};


