/*******************************************************************************
* @Copyright (C) 2022 by Vantage Elevation
* @file           : sr_LED.c
* @version 		  : 1.0.0
* @brief          : LED subroutine
* @details		  : LED subroutine
********************************************************************************/

/*******************************************************************************
* Includes
********************************************************************************/
//#include "main.h"
#include "sr_fault_alarm.h"
#include "sr_LED.h"
#include "car_faults_def.h"
#include "app_gpio.h"
#include "car_alarms_def.h"
/*******************************************************************************
* Function Prototypes
********************************************************************************/

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/

#define LED_HEARTBEAT_UPDATE_RATE_100MS			10
#define LED_ALARM_UPDATE_RATE_100MS				5
#define LED_FAULT_UPDATE_RATE_RESET_100MS		10
#define LED_FAULT_UPDATE_RATE_OTHER_100MS		5
#define LED_FAULT_UPDATE_RATE_INVALID_100MS		20


/*******************************************************************************
* Preprocessor Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variable Definitions
********************************************************************************/

/*******************************************************************************
* Variable Definitions
********************************************************************************/


/*******************************************************************************
* Function Definitions
********************************************************************************/
/**
  * @fn  LED_UpdateHeartbeat(void)
  * @brief Updates LED heartbeat output
  * @param None
  * @return None
  */
static void LED_UpdateHeartbeat(void)
{
		static uint8_t ucCounter_100ms;
		if(++ucCounter_100ms >= LED_HEARTBEAT_UPDATE_RATE_100MS){
			ucCounter_100ms = 0;
			uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_HB);
			GPIO_WriteOutput(enLOCAL_OUT__LED_HB, !bLastLED);
		}
}
/* @fn static void LED_UpdateAlarm(void)
 * @brief Updates alarm LED output
 * @param None
 * @return None
 */
static void LED_UpdateAlarm(void)
{
   static uint8_t ucCounter_100ms;
   if( Alarms_GetAlarmFlag() )
   {
      if(++ucCounter_100ms >= LED_ALARM_UPDATE_RATE_100MS)
      {
         uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_ALM);
         GPIO_WriteOutput(enLOCAL_OUT__LED_ALM, !bLastLED);
         ucCounter_100ms = 0;
      }
   }
   else
   {
      GPIO_WriteOutput(enLOCAL_OUT__LED_ALM, INACTIVE);
   }
}
/**
  * @fn  LED_UpdateFaultCOP(void)
  * @brief Updates LED fault output for COP configured board
  * @param None
  * @return None
  */
static void LED_UpdateFaultCOP(void)
{
	static uint8_t ucCounter_100ms;
	if( Faults_GetFaultFlag() )
	{
	   en_car_fault eFault = Faults_GetFaultNumber_ByNode(System_GetNodeID());
	   if( ( eFault >= CFLT__COP_A4_POWER_RESET ) && ( eFault <= CFLT__COP_A4_BOD_RESET ) )
	   {
	      if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_RESET_100MS )
	      {
	         uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
	         GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
	         ucCounter_100ms = 0;
	      }
	   }
	   else if( ( eFault == CFLT__MR_OFFLINE_COP )
			 || ( eFault == CFLT__CT_OFFLINE_COP ) )
	   {
	      GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, ACTIVE);
	   }
	   else
	   {
	      if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_OTHER_100MS )
	      {
	         uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
	         GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
	         ucCounter_100ms = 0;
	      }
	   }
	}
	else
	{
	   GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, INACTIVE);
	}
}
/**
  * @fn  LED_UpdateFaultEXP(void)
  * @brief Updates LED fault output for EXP configured board
  * @param None
  * @return None
  */
static void LED_UpdateFaultEXP(void)
{
	static uint8_t ucCounter_100ms;
	en_exp_error eError = Error_GetActiveError();

    if( ( eError == EXP_ERROR__COMM_CAN1 )
     || ( eError == EXP_ERROR__COMM_CAN2 ) )
    {
       GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, ACTIVE);
       ucCounter_100ms = 0;
    }
    /* Power on / reset fault -> 1 second toggle */
    else if( ( eError == EXP_ERROR__POWER_ON_RESET )
          || ( eError == EXP_ERROR__WATCHDOG_RESET )
          || ( eError == EXP_ERROR__LOW_POWER_RESET ) )
    {
       if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_RESET_100MS)
       {
          uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
          GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
          ucCounter_100ms = 0;
       }
    }
    /* All other faults -> 0.5 second toggle */
    else if( eError != EXP_ERROR__NONE )
    {
       if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_OTHER_100MS)
       {
          uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
          GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
          ucCounter_100ms = 0;
       }
    }
    else
    {
       GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, INACTIVE);
       ucCounter_100ms = 0;
    }
}
/**
  * @fn  LED_UpdateFaultRiser(void)
  * @brief Updates LED fault output for Riser configured board
  * @param None
  * @return None
  */
static void LED_UpdateFaultRiser(void)
{
   static uint8_t ucCounter_100ms;
   en_ris_error eError = Error_GetActiveErrorRiser();

   if( ( eError == RIS_ERROR__COMM_CAN1 )
         || ( eError == RIS_ERROR__COMM_CAN2 )
         || ( eError == RIS_ERROR__COMM_CAN3 ) )
   {
      GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, ACTIVE);
      ucCounter_100ms = 0;
   }
   /* Power on / reset fault -> 1 second toggle */
   else if( ( eError == RIS_ERROR__POWER_ON_RESET )
         || ( eError == RIS_ERROR__WATCHDOG_RESET )
         || ( eError == RIS_ERROR__LOW_POWER_RESET ) )
   {
      if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_RESET_100MS)
      {
         uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
         GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
         ucCounter_100ms = 0;
      }
   }
   /* All other faults -> 0.5 second toggle */
   else if( eError != RIS_ERROR__NONE )
   {
      if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_OTHER_100MS)
      {
         uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
         GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
         ucCounter_100ms = 0;
      }
   }
   else
   {
      GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, INACTIVE);
      ucCounter_100ms = 0;
   }
}
/**
  * @fn  LED_UpdateFaultInvalid(void)
  * @brief Updates LED fault output for misconfigured board
  * @param None
  * @return None
  */
static void LED_UpdateFaultInvalid(void)
{
	static uint8_t ucCounter_100ms;

    if(++ucCounter_100ms >= LED_FAULT_UPDATE_RATE_INVALID_100MS)
    {
       uint8_t bLastLED = GPIO_ReadOutputCommand(enLOCAL_OUT__LED_FLT);
       GPIO_WriteOutput(enLOCAL_OUT__LED_FLT, !bLastLED);
       ucCounter_100ms = 0;
    }
}


/* @fn void vLED_Update_Task(void)
 * @brief Updates the LED status based on the board type and heartbeat
 * @param None
 * @return None
 */
void vLED_Update_Task(void)
{
    // Update the heartbeat LED to show system activity
    LED_UpdateHeartbeat();

    // Switch based on the local board type to handle different fault updates
    switch(Main_GetLocalBoardType())
    {
        case BOARD_COP:
            // Update fault indication LED for COP board
            LED_UpdateFaultCOP();
			LED_UpdateAlarm();
            break;

        case BOARD_EXP_MASTER:
        case BOARD_EXP_SLAVE:
            // Update fault indication LED for EXP board (Master or Slave)
            LED_UpdateFaultEXP();
            break;

        case BOARD_RISER:
            // Update fault indication LED for RISER board
            LED_UpdateFaultRiser();
            break;

        case BOARD_INVALID:
        default:
            // Update fault indication LED for invalid board type
            LED_UpdateFaultInvalid();
        break;
    }
}


