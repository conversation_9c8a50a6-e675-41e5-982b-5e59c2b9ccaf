/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_can.h
* @version 		  : 1.0.0
* @brief Functions for accessing and initializing CAN bus
* @details Functions for accessing and initializing CAN bus
*****************************************************************************/
#ifndef _HAL_CAN_H_
#define _HAL_CAN_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>

#include "network.h"
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/
/* Configured pin locations */
#define MCUA_FDCAN1_TXD_Pin GPIO_PIN_1
#define MCUA_FDCAN1_TXD_GPIO_Port GPIOD
#define MCUA_FDCAN1_RXD_Pin GPIO_PIN_0
#define MCUA_FDCAN1_RXD_GPIO_Port GPIOD

#define MCUA_FDCAN2_RXD_Pin GPIO_PIN_5
#define MCUA_FDCAN2_RXD_GPIO_Port GPIOB
#define MCUA_FDCAN2_TXD_Pin GPIO_PIN_6
#define MCUA_FDCAN2_TXD_GPIO_Port GPIOB

/* Memory space equally split between CAN1/CAN2 is 1280 words.
 * Each can buffer element of 64 bytes is 18 words.
 * Assuming only memory use is rx/tx buffers, limit is 64 total buffer elements */
#define CAN_RX_RING_BUFFER_SIZE                 (48)
#define CAN_TX_RING_BUFFER_SIZE                 (16)

/* Classic CAN 2.0 if 0, CAN FD if 1 */
#define CAN1_ENABLE_CANFD                       (1)
#define CAN2_ENABLE_CANFD                       (0)

/* Enable bit rate switching if 1, constant bit rate if 0. */
#if CAN1_ENABLE_CANFD
#define CAN1_ENABLE_BITRATE_SWITCHING           (0)
#endif
#if CAN2_ENABLE_CANFD
#define CAN2_ENABLE_BITRATE_SWITCHING           (0)
#endif

/* When bitrate switching is enabled, this is the higher baud rate used during the bus arbitration phase */
#define CAN1_BAUD_RATE                          (CAN_BAUD_RATE__125K)
#define CAN2_BAUD_RATE                          (CAN_BAUD_RATE__250K)

/* When bitrate switching is enabled, this is the higher baud rate used after bus arbitration phase */
#define CAN1_BAUD_BRS_RATE                      (CAN_BAUD_RATE__125K)
#define CAN2_BAUD_BRS_RATE                      (CAN_BAUD_RATE__250K)

/******************************************************************************
* Macros
*******************************************************************************/



/******************************************************************************
* Typedefs
*******************************************************************************/
/* Enumeration of different CAN status counters */
typedef enum
{
   CAN_COUNTER__RX_PACKET,   /* Number of successful packet RX */
   CAN_COUNTER__TX_PACKET,   /* Number of successful packet TX */
   CAN_COUNTER__RX_OVERFLOW, /* Data loss because RX ring buffer was full */
   CAN_COUNTER__TX_OVERFLOW, /* Transmit failed because TX ring buffer was full */
   CAN_COUNTER__BUS_ERROR,   /* Counts the number of time a hardware bus error has been detected */
   CAN_COUNTER__BUS_RESET,   /* Bus Reset Counter */

   NUM_CAN_COUNTERS
} en_can_counter;

/* Structure for baud rate lookup table */
typedef struct
{
   uint8_t ucSyncJumpWidth;
   uint8_t ucTimeSeg1;
   uint8_t ucTimeSeg2;
   uint8_t ucPrescale;
}st_can_bitrate_config;

/* Possible baud rate configurations generated in lookup table assuming 48 MHZ peripheral clock */
typedef enum {
   CAN_BAUD_RATE__50K,
   CAN_BAUD_RATE__75K,
   CAN_BAUD_RATE__100K,
   CAN_BAUD_RATE__125K,
   CAN_BAUD_RATE__150K,
   CAN_BAUD_RATE__200K,
   CAN_BAUD_RATE__250K,
   CAN_BAUD_RATE__500K,
   CAN_BAUD_RATE__1M,

   NUM_CAN_BAUD_RATES
} en_can_baud_rates;
/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void CAN1_Init(void);
void CAN2_Init(void);

en_pass_fail CAN1_UnloadFromRB(st_CAN_msg *pstRxMsg);
en_pass_fail CAN1_LoadToRB(st_CAN_msg *pstTxMsg);

en_pass_fail CAN2_UnloadFromRB(st_CAN_msg *pstRxMsg);
en_pass_fail CAN2_LoadToRB(st_CAN_msg_8B *pstTxMsg);

uint8_t CAN1_CheckForBusOffline(void);
uint8_t CAN2_CheckForBusOffline(void);

void CAN1_UpdateBusErrorCounter(void);
void CAN2_UpdateBusErrorCounter(void);

uint16_t CAN1_GetStatusCounter(en_can_counter eCounter);
uint16_t CAN2_GetStatusCounter(en_can_counter eCounter);
void CAN1_IncrementStatusCounter(en_can_counter eCounter);
void CAN2_IncrementStatusCounter(en_can_counter eCounter);
#endif /* _HAL_CAN_H_ */
