/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_Alarm.h
* @version 		  : 1.0.0
* @brief Subroutine updating processor's alarm status
* @details Subroutine updating processor's alarm status
*****************************************************************************/
#ifndef _SR_ALARM_H_
#define _SR_ALARM_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "scheduler_lite.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
extern st_subroutine_lite gstSR_Alarm;
/******************************************************************************
* Function Prototypes
*******************************************************************************/

#endif /* _SR_ALARM_H_ */
