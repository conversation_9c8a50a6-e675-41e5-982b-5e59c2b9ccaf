/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_crc.h
* @version 		  : 1.0.0
* @brief 		  :Functions for initializing the CRC engine
* @details 		  :Functions for initializing the CRC engine
*****************************************************************************/
#ifndef _CRC_H_
#define _CRC_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "stm32g4xx_hal.h"

/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/

/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void CRC_Init(void);
uint32_t CRC_Accumulate(uint32_t pBuffer[], uint32_t BufferLength);
uint32_t CRC_Calculate(uint32_t pBuffer[], uint32_t BufferLength);

#endif /* _CRC_H_ */
