/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN1.c
 * @version 		  : 1.0.0
 * @brief Subroutine handling CAN1 network
 * @details Subroutine handling CAN1 network
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "sr_fault_alarm.h"
#include <string.h>
#include "sr_CAN1.h"
#include "sr_CAN2.h"
#include "sr_CAN3.h"
#include "sr_SPI_AU.h"
#include "main.h"
#include "car_faults_def.h"
#include "car_alarms_def.h"
#include "app_can.h"
#include "dg_car_mr_a7.h"
#include "dg_car_mr_b4.h"
#include "dg_car_ct_a7.h"
#include "dg_car_ct_b4.h"
#include "dg_car_ui_a7.h"
#include "dg_car_exp.h"
#include "dg_hall_mr_b4.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/
// How long the bus can go without receiving a message before an offline flag is set
#define CAN_OFFLINE_TIMEOUT_1MS                    (6000)

/* How long this node can go without receiving a message from the MR board before an offline flag is set */
#define MR_OFFLINE_TIMEOUT_1MS                     (15000)
/* How long this node can go without receiving a message from the CT board before an offline flag is set */
#define CT_OFFLINE_TIMEOUT_1MS                     (4000)
#define UI_MR_OFFLINE_TIMEOUT_1MS                  (61000)
#define UI_CT_OFFLINE_TIMEOUT_1MS                  (61000)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static uint16_t uwOfflineTimer_1ms = CAN_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_MR_1ms = MR_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_CT_1ms = CT_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_UI_MR_1ms = UI_MR_OFFLINE_TIMEOUT_1MS;
static uint16_t uwOfflineTimer_UI_CT_1ms = UI_CT_OFFLINE_TIMEOUT_1MS;
static uint8_t bLastOfflineFlag_MR; /* 1 if the MR board was offline last routine run */
static uint8_t bLastOfflineFlag_CT; /* 1 if the CT board was offline last routine run */
static uint8_t bLastOfflineFlag_UI_MR; /* 1 if the UI MR board was offline last routine run */
static uint8_t bLastOfflineFlag_UI_CT; /* 1 if the UI CT board was offline last routine run */
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Returns 0 if the packet was successfully loaded for transmit
 * @param pstMsg, pointer to CAN message to transmit
 * @return Returns 0 if the packet was successfully loaded for transmit
 */
en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg)
{
    en_pass_fail eError = FAIL;
    if(pstMsg->bExtendedID == INACTIVE)
    {
        if(pstMsg->uiID & NETWORK_PACKET_ID_EXTENDED_BITMAP)
        {
      	  pstMsg->bExtendedID = ACTIVE;
        }
        else
        {
            pstMsg->bExtendedID = INACTIVE;
        }
    }
    pstMsg->uiID &= NETWORK_PACKET_ID_EXTENDED_MASK;
    if(CAN1_LoadToRB(pstMsg) == PASS)
    {
        eError = PASS;
    }
    return eError;
}

/* @fn static void CAN1_ResetOfflineTimers_Car(en_sys_node eSource, uint16_t uwDatagramID, uint16_t uwDatagramID)
 * @brief Resets offline timers for the car net
 * @param eSource, message source
 * @param uwDatagramID, message datagram ID
 * @return None
 */
static void CAN1_ResetOfflineTimers_Car(en_sys_node eSource, uint16_t uwDatagramID)
{
   en_local_board_type eLocalBoardType = Main_GetLocalBoardType();
   if( eSource == SYS_NODE__MR_A7 )
   {
       uwOfflineTimer_MR_1ms = 0;
   }
   else if( eSource == SYS_NODE__CT_A7 )
   {
       uwOfflineTimer_CT_1ms = 0;
   }
   else if( eSource == SYS_NODE__UI_MR_A7 )
   {
	   uwOfflineTimer_UI_MR_1ms = 0;
   }
   else if( eSource == SYS_NODE__UI_CT_A7 )
   {
	   uwOfflineTimer_UI_CT_1ms = 0;
   }

   if( ( eLocalBoardType == BOARD_COP ) && ( eSource == SYS_NODE__COP_A4 ) )
   {
       Faults_SetFault(CFLT__COP_DUPLICATE);
   }
   else if( ( eLocalBoardType == BOARD_EXP_MASTER ) && ( eSource == SYS_NODE__EXP ) )
   {
      if( uwDatagramID == ( DG_CAR_EXP__Status_01 + Main_GetLocalExpansionID() ) )
      {
          Error_SetActiveError(EXP_ERROR__DUPLICATE_ADDR);
      }
   }
   else if( eLocalBoardType == BOARD_EXP_SLAVE )
   {
       Error_SetActiveError(EXP_ERROR__INVALID_DEVICE_CAN1);
   }
}
/* @fn static void CAN1_ResetOfflineTimers_Inter(en_sys_node eSource, uint16_t uwDatagramID, uint16_t uwDatagramID)
 * @brief Resets offline timers for the car net
 * @param eSource, message source
 * @param uwDatagramID, message datagram ID
 * @return None
 */
static void CAN1_ResetOfflineTimers_Inter(en_sys_node eSource, uint16_t uwDatagramID)
{
   if( ( eSource >= SYS_NODE__MR_A7 ) && ( eSource <= SYS_NODE__MR_B4 ) )
   {
       uwOfflineTimer_MR_1ms = 0;
   }
   else if( ( eSource >= SYS_NODE__CT_A7 ) && ( eSource <= SYS_NODE__CT_B4 ) )
   {
       uwOfflineTimer_CT_1ms = 0;
   }
   switch( Main_GetLocalBoardType() )
   {
      case BOARD_COP:
         Faults_SetFault(CFLT__EXP_SLAVE_DETECTED_CAN1);
         break;
      case BOARD_EXP_MASTER:
         Error_SetActiveError(EXP_ERROR__INVALID_DEVICE_CAN1);
         break;
      case BOARD_EXP_SLAVE:
         if(eSource == SYS_NODE__EXP)
         {
             Error_SetActiveError(EXP_ERROR__INVALID_DEVICE_CAN1);
         }
         break;
      case BOARD_RISER:
         break;
      case BOARD_INVALID:
      default:
         break;
   }
}
/* @fn static void CAN1_ResetOfflineTimers_Group(en_sys_node eSource, uint16_t uwDatagramID)
* @brief Resets offline timers for the group net
* @param eSource, message source
* @param uwDatagramID, message datagram ID
* @return None
*/
static void CAN1_ResetOfflineTimers_Group(en_sys_node eSource)
{
   if( ( eSource == SYS_NODE__MR_B4 )
	|| ( ( eSource >= SYS_NODE__M_GROUP_CAR_1 ) && ( eSource <= SYS_NODE__M_GROUP_CAR_8 ) ) )
   {
       uwOfflineTimer_MR_1ms = 0;
   }
}
/* @fn static void UnloadDatagrams(void)
 * @brief Unloads messages from the CAN ring buffer
 * @param None
 * @return None
 */
static void UnloadDatagrams(void) {
   st_datagram_control *pstControl;
   st_datagram *pstDatagram;
   st_CAN_msg stRxMsg;
   en_sys_network eNetwork;
   en_sys_node eSource;
   uint16_t uwDatagramID;
   uint8_t ucNumBytes;
   for(uint8_t i = 0; i < CAN_RX_RING_BUFFER_SIZE; i++) {
      if(CAN1_UnloadFromRB(&stRxMsg) == PASS) {
         /* Packet encoding exception for high priority position packets */
         if((stRxMsg.uiID & NETWORK_PACKET_ID_EXTENDED_MASK)
            == ( UNIQUE_POSITION_EXTENDED_CAN_ID & NETWORK_PACKET_ID_EXTENDED_MASK)) {
            pstControl = DG_CAR_CT_A7_GetDatagramControlStruct();
            uwDatagramID = DG_CAR_CT_A7__Position;
            pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
            stRxMsg.uiID = Network_GetPacketID(pstDatagram->ucSize_Bytes,
                                               uwDatagramID,
                                               pstControl->ucNetwork,
                                               pstControl->ucSourceNode);
         }
         eNetwork = Network_GetNetworkID(stRxMsg.uiID);
         if(eNetwork == SYS_NETWORK__CAR) {
            eSource = Network_GetSourceID(stRxMsg.uiID);
            pstControl = Network_GetControlStructure_Car(eSource);
            if(pstControl != 0) {
               uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
               if(uwDatagramID < pstControl->uwNumberOfDG) {
                  pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                  ucNumBytes = pstDatagram->ucSize_Bytes;
                  /* 1. Copy the content of every datagram */
                  memcpy(pstDatagram->paucData, &stRxMsg.unData.auc8[0], ucNumBytes);
                  /* 2. Run the datagram's unload function */
                  if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID()))
                     pstDatagram->pfnUnload();

                  /* 3. Check if the packet should be forwarded */
                  if((Network_CheckDestination_Car(NET_FWD_PATH__UI,
                                                   pstDatagram->uiDestinationBitmap)
                      == PASS)
                     && (Param_GetState() != PARAM_STATE__STARTUP)) {
                     stRxMsg.ucDLC = ucNumBytes;
                     SPI_AU_TransmitDatagram(&stRxMsg);
                  }
                  if(!CAN2_CheckIfCommLoss()
                     && CAN2_CheckDestination(pstDatagram->uiDestinationBitmap)) {
                     if(Main_GetLocalBoardType() == BOARD_EXP_MASTER) {
                        stRxMsg.uiID = Network_GetPacketID(stRxMsg.ucDLC,
                                                           uwDatagramID,
                                                           SYS_NETWORK__INTERGROUP,
                                                           eSource);
                     }
                     CAN2_TransmitDatagram(&stRxMsg);
                  }
                  /* 4. Increment receive counter */
                  pstDatagram->ucPacketCounter++;

                  /* 5. Mark packet as received */
                  if(eSource != SYS_NODE__EXP) {
                     pstDatagram->bDataChanged = ACTIVE;
                  }

                  /* 6. Reset offline timers */
                  CAN1_ResetOfflineTimers_Car(eSource, uwDatagramID);
               }
            }
         } else if(eNetwork == SYS_NETWORK__INTERGROUP) {
            eSource = Network_GetSourceID(stRxMsg.uiID);
            pstControl = Network_GetControlStructure_Car(eSource);
            if(pstControl != 0) {
               uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
               if(uwDatagramID < pstControl->uwNumberOfDG) {
                  pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                  ucNumBytes = pstDatagram->ucSize_Bytes;
                  /* 1. Copy the content of every datagram */
                  memcpy(pstDatagram->paucData, &stRxMsg.unData.auc8[0], ucNumBytes);
                  /* 2. Run the datagram's unload function */
                  if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID()))
                     pstDatagram->pfnUnload();

                  /* 3. Check if the packet should be forwarded */
                  if(!CAN2_CheckIfCommLoss()
                     && CAN2_CheckDestination(pstDatagram->uiDestinationBitmap)) {
                     CAN2_TransmitDatagram(&stRxMsg);
                  }
                  /* 4. Increment receive counter */
                  pstDatagram->ucPacketCounter++;

                  /* 5. Mark packet as received */
                  if(eSource != SYS_NODE__EXP) {
                     pstDatagram->bDataChanged = ACTIVE;
                  }

                  /* 6. Reset offline timers */
                  CAN1_ResetOfflineTimers_Inter(eSource, uwDatagramID);
               }
            }
         } else if(eNetwork == SYS_NETWORK__GROUP) {
            eSource = Network_GetSourceID(stRxMsg.uiID);
            pstControl = Network_GetControlStructure_Group(eSource);
            if(pstControl != NULL) {
               uwDatagramID = Network_GetDatagramID(stRxMsg.uiID);
               if(uwDatagramID < pstControl->uwNumberOfDG) {
                  pstDatagram = &pstControl->pastDatagrams[uwDatagramID];
                  ucNumBytes = pstDatagram->ucSize_Bytes;
                  /* 1. Copy the content of every datagram */
                  memcpy(pstDatagram->paucData, &stRxMsg.unData.auc8[0], ucNumBytes);
                  /* 2. Run the datagram's unload function */
                  if(pstDatagram->uiDestinationBitmap & (1 << System_GetNodeID())){
                     pstDatagram->pfnUnload();
                  }

                  /* 3. Check if the packet should be forwarded */
                  if((pstDatagram->uiDestinationBitmap & DG_DEST_MAP__HALL) == DG_DEST_MAP__HALL) {
                     /* Hall boards are attached to CAN2&3. HB func settings can be derived from dg id,
                     and the func settings of the HBs attached to each bus are known. So forward them on 
                     bus if an HB with func setting is on bus. */
                     uint8_t ucFuncSetting = uwDatagramID-DG_HALL_MR_B4__HallBoard_Outputs_Func_0;
                     if(CAN2_HallBoardFuncOnBus(ucFuncSetting) == ACTIVE && !CAN2_CheckIfCommLoss()){
                        CAN2_TransmitDatagram(&stRxMsg);
                     }
                     if(CAN3_HallBoardFuncOnBus(ucFuncSetting) == ACTIVE && !CAN3_CheckIfCommLoss()){
                        CAN3_TransmitDatagram(&stRxMsg);
                     }
                  }
                  
                  /* 4. Increment receive counter */
                  pstDatagram->ucPacketCounter++;

                  /* 5. Mark packet as received */
                  if(eSource != SYS_NODE__EXP) {
                     pstDatagram->bDataChanged = ACTIVE;
                  }

                  /* 6. Reset offline timers */
                  CAN1_ResetOfflineTimers_Group(eSource);
               }
            }
         }
         CAN1_IncrementStatusCounter(CAN_COUNTER__RX_PACKET);
         uwOfflineTimer_1ms = 0;
      } else {
         break;
      }
   }
}
/* @fn uint8_t CAN1_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t CAN1_CheckIfCommLoss(void)
{
    return ( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS );
}
/* @fn static void UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void)
{
    uint8_t bOfflineFlag_MR;
    uint8_t bOfflineFlag_CT;
    uint8_t bOfflineFlag_UI_MR;
    uint8_t bOfflineFlag_UI_CT;
    if( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS )
    {

    }
    else
    {
        uwOfflineTimer_1ms += CAN1_TASK_PERIODIC_DELAY_MS;
    }

    /* MR */
    if( uwOfflineTimer_MR_1ms >= MR_OFFLINE_TIMEOUT_1MS )
    {
        bOfflineFlag_MR = ACTIVE;
        switch( Main_GetLocalBoardType() )
        {
           case BOARD_COP:
              Faults_SetFault(CFLT__MR_OFFLINE_COP);
              if( !bLastOfflineFlag_MR )
              {
                 ClearMRADatagrams();
                 ClearMRBDatagrams();
              }
           case BOARD_EXP_MASTER:
           case BOARD_EXP_SLAVE:
              Error_SetActiveError(EXP_ERROR__COMM_CAN1);
              break;
           case BOARD_RISER:
              Error_SetActiveErrorRiser(RIS_ERROR__COMM_CAN1);
              break;
           case BOARD_INVALID:
           default:
              break;
        }
    }
    else
    {
        bOfflineFlag_MR = INACTIVE;
        uwOfflineTimer_MR_1ms += CAN1_TASK_PERIODIC_DELAY_MS;
    }
    /* CT */
    if( uwOfflineTimer_CT_1ms >= CT_OFFLINE_TIMEOUT_1MS )
    {
        if( Main_GetLocalBoardType() == BOARD_COP )
        {
            bOfflineFlag_CT = ACTIVE;
            Faults_SetFault(CFLT__CT_OFFLINE_COP);
            if( !bLastOfflineFlag_CT )
            {
               ClearCTADatagrams();
               ClearCTBDatagrams();
            }
        }
    }
    else
    {
 	    bOfflineFlag_CT = ACTIVE;
        uwOfflineTimer_CT_1ms += CAN1_TASK_PERIODIC_DELAY_MS;
    }
    /* UI MR */
    if( uwOfflineTimer_UI_MR_1ms >= UI_MR_OFFLINE_TIMEOUT_1MS )
    {
        if( Main_GetLocalBoardType() == BOARD_COP )
        {
            bOfflineFlag_UI_MR = ACTIVE;
            /* TODO add alarm */
            if( !bLastOfflineFlag_UI_MR )
            {
               ClearUIDatagrams(SYS_NODE__UI_MR_A7);
            }
        }
    }
    else
    {
 	    bOfflineFlag_UI_MR = ACTIVE;
        uwOfflineTimer_UI_MR_1ms += CAN1_TASK_PERIODIC_DELAY_MS;
    }
    /* UI CT */
    if( uwOfflineTimer_UI_CT_1ms >= UI_CT_OFFLINE_TIMEOUT_1MS )
    {
        if( Main_GetLocalBoardType() == BOARD_COP )
        {
            bOfflineFlag_UI_CT = ACTIVE;
            /* TODO add alarm */
            if( !bLastOfflineFlag_UI_CT )
            {
               ClearUIDatagrams(SYS_NODE__UI_CT_A7);
            }
        }
    }
    else
    {
 	    bOfflineFlag_UI_CT = ACTIVE;
        uwOfflineTimer_UI_CT_1ms += CAN1_TASK_PERIODIC_DELAY_MS;
    }

    bLastOfflineFlag_MR = bOfflineFlag_MR;
    bLastOfflineFlag_CT = bOfflineFlag_CT;
    bLastOfflineFlag_UI_MR = bOfflineFlag_UI_MR;
    bLastOfflineFlag_UI_CT = bOfflineFlag_UI_CT;
}

/* @fn void vCAN1_Task(void *pvParams)
 * @brief Handles CAN1 communication, updates bus error counters, and manages errors based on board type
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCAN1_Task(void *pvParams)
{
    switch( Main_GetLocalBoardType() )
    {
        case BOARD_COP:
            vTaskDelay(CAN1_TASK_COP_FIRST_DELAY_MS);
            break;
        case BOARD_EXP_MASTER:
        case BOARD_EXP_SLAVE:
            vTaskDelay(CAN1_TASK_EXP_FIRST_DELAY_MS);
            break;
        case BOARD_RISER:
        	uwOfflineTimer_MR_1ms = 0;
            vTaskDelay(CAN1_TASK_RIS_FIRST_DELAY_MS);
			break;
        default:
            vTaskDelay(CAN1_TASK_COP_FIRST_DELAY_MS);
        	break;
    }
// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1)
    {
        UnloadDatagrams();
        // Update the offline timer, possibly for tracking CAN bus activity
        UpdateOfflineTimer();
       // Update the CAN1 bus error counter to monitor the bus health
        CAN1_UpdateBusErrorCounter();
        // Check if CAN1 is offline (communication failure on the bus)
        if( CAN1_CheckForBusOffline() )
        {
            // Handle bus offline scenario based on the local board type
            switch( Main_GetLocalBoardType() )
            {
                case BOARD_COP:
                    // For COP board, set an alarm for the bus reset condition
                    Alarms_SetAlarm(CALM__BUS_RESET_COP_N1);
                    break;
                case BOARD_EXP_MASTER:
                case BOARD_EXP_SLAVE:
                    // For EXP board (Master/Slave), set an active error for bus offline
                    Error_SetActiveError(EXP_ERROR__BUS_OFFLINE_CAN1);
                    break;
                case BOARD_RISER:
                    // For RISER board, set an active error for bus offline
                    Error_SetActiveErrorRiser(RIS_ERROR__BUS_OFFLINE_CAN1);
                    break;
                case BOARD_INVALID:
                default:
                    // No action for invalid or default board types
                    break;
            }
        }
            // Delay using the configured CAN1 task period if debugging is disabled
            vTaskDelay(CAN1_TASK_PERIODIC_DELAY_MS);
    }
}

