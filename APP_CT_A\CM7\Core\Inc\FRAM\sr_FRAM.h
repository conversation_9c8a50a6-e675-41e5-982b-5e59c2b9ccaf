/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_FRAM.h
* @version 		  : 1.0.0
* @brief Subroutine manages communication with the FM25V05-GTR 512Kb SPI FRAM chip
* @details Subroutine manages communication with the FM25V05-GTR 512Kb SPI FRAM chip
*****************************************************************************/
#ifndef _SR_FRAM_H_
#define _SR_FRAM_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "version.h"
#include "system.h"
#include "fram_def.h"
#include "operation_def.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/


/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_fram_state FRAM_GetState(void);

en_active_inactive FRAM_GetFaultLogChangedFlag(uint8_t ucIndex);
void FRAM_ClrFaultLogChangedFlag(uint8_t ucIndex);
en_active_inactive FRAM_GetAlarmLogChangedFlag(uint8_t ucIndex);
void FRAM_ClrAlarmLogChangedFlag(uint8_t ucIndex);

en_active_inactive FRAM_GetParamLogChangedFlag(uint8_t ucIndex);
void FRAM_ClrParamLogChangedFlag(uint8_t ucIndex);

uint8_t *FRAM_GetBackupTimeChangedFlag(void);
uint8_t *FRAM_GetDefaultTimeChangedFlag(void);

st_position_speed * FRAM_GetPositionSpeed(void);

en_fire_phase_1_trigger FRAM_GetFirePhase1Trigger(void);
void FRAM_SetFirePhase1Trigger(en_fire_phase_1_trigger eTrigger);


en_pass_fail FRAM_WriteTriplet(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes);
en_pass_fail FRAM_ReadTriplet(uint32_t uiAddress, void *pRxData, uint8_t ucNumRxBytes);

en_pass_fail FRAM_ReadData(uint32_t uiAddress, void *pRxData, uint8_t ucNumRxBytes);
en_pass_fail FRAM_WriteData(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes);

en_pass_fail FRAM_VerifyParamBlock(uint32_t uiAddressBlock, uint8_t ucNumRxBytes, en_fram_writes_type enType);
en_pass_fail FRAM_ReadParamBlock(uint32_t uiAddressBlock, char * buffer, uint8_t ucNumRxBytes);
en_pass_fail FRAM_WriteSingleBlock(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes);
en_pass_fail FRAM_ClearAllParamBlock(uint32_t uiAddressBlock);
uint8_t FRAM_CheckParamBlockEflashCRCNonZero(uint32_t uiAddressBlock);
en_pass_fail FRAM_InitializeAllParamBlockCRC(uint32_t uiAddressBlock);
en_pass_fail FRAM_WriteParamBlock(uint32_t uiAddressBlock);
en_pass_fail FRAM_WriteJobNameBlock(const uint8_t *pucName);

void vFRAM_Task(void *pvParameters);

#endif /* _SR_FRAM_H_ */
