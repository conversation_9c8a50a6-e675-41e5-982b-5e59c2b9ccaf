/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file sr_learn.c
* @version 1.0.0
* @brief This subroutine handles learn mode
* @details This subroutine handles learn mode
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_learn.h"
#include "learn_def.h"
#include "door_def.h"
#include "motion_def.h"
#include "operation_def.h"
#include "position_def.h"
#include "floor_def.h"
#include "param_def.h"
#include "car_alarms_def.h"
#include "learn_v2_def.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/


/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_RUN_INTERVAL_1MS     LRN_SR_RUN_INTERVAL_1MS

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static en_position_systems eInitPosSys = NUM_POSITION_SYSTEMS;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn void Learning_Init(void)
 * @brief Initialization function for the learning variables (place holder)
 * @param None
 * @return None
 */
void Learning_Init(void)
{

}
/* @fn void vLearning_Task(void *pvParameters)
 * @brief vLearning_Task called from RTOS scheduler
 * @param None
 * @return None
 */
void vLearning_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
//  Learn_Init(&LearnModule, &Learn_Wait_NTS_ETS_State);

  while(1)
  {
   en_position_systems ePosSys = NUM_POSITION_SYSTEMS;
   en_param_state eParamState = Param_GetState();
   if( ( eParamState != PARAM_STATE__STARTUP ) && ( eInitPosSys == NUM_POSITION_SYSTEMS ) )
   {
      eInitPosSys = Param_ReadValue_8Bit(CPARAM8__PositionSystemType);
   }
   if( eInitPosSys != NUM_POSITION_SYSTEMS )
   {
      ePosSys = Param_ReadValue_8Bit(CPARAM8__PositionSystemType);
   }

   en_semiauto_operations eSemiAutoOp = Operation_GetSemiAutoOperation();

   if( ( eParamState != PARAM_STATE__STARTUP ) && ( eSemiAutoOp == SEMIAUTO_OP__LEARN ) )
   {
      en_ct_learn_state eLrnState = Learn_GetState();
      if( eLrnState != CT_LEARN_STATE__INACTIVE
      && ( ( Operation_GetManualOperation() == MANUAL_OP__CONSTRUCTION ) || ( Operation_GetManualOperation() == MANUAL_OP__CAR_SAFETY_BYPASS) ) )
      {
         Learn_SetActiveError(LRN_RET__CONSTRUCTION_MODE);// AS TODO create different fault for car bypass mode?
      }
      else if( Learn_GetCmdFlag(LRN_CMD_ID__LRN_MAIN) == ACTIVE )
      {
         switch( eLrnState )
         {
            case CT_LEARN_STATE__INACTIVE:
               Learn_SetState(CT_LEARN_STATE__MAIN);
               break;
            case CT_LEARN_STATE__MAIN:
               if( Learn_GetCmdFlag(LRN_CMD_ID__LH) == ACTIVE )
               {
                  Learn_SetState(CT_LEARN_STATE__LH_INIT);
                  Learn_SetCmdFlag(LRN_CMD_ID__LH, INACTIVE);
               }
               break;
            case CT_LEARN_STATE__LH_INIT:
               if( Learn_GetCmdFlag(LRN_CMD_ID__PRCD_LH_CNFG) == ACTIVE )
               {
                  Learn_SetBottomTerminalLearned(INACTIVE);
                  if( Learn_GetLearnFromBot() == ACTIVE )
                  {
                     Learn_SetCurrentFloor(0);
                     if( Learn_GetLearnByLandingPos() == ACTIVE )
                     {
                        Learn_SetState(CT_LEARN_STATE__LH_UP_BY_POS);
                     }
                     else
                     {
                        Learn_SetState(CT_LEARN_STATE__LH_UP_BY_DIS);
                     }
                  }
                  else
                  {
                     Learn_SetCurrentFloor(Floor_GetNumberOfCarFloors()-1);
                     if( Learn_GetLearnByLandingPos() == ACTIVE )
                     {
                        Learn_SetState(CT_LEARN_STATE__LH_DN_BY_POS);
                     }
                     else
                     {
                        Learn_SetState(CT_LEARN_STATE__LH_DN_BY_DIS);
                     }
                  }
                  Learn_SetCmdFlag(LRN_CMD_ID__PRCD_LH_CNFG, INACTIVE);
               }
               else if( Learn_GetCmdFlag(LRN_CMD_ID__BACK_0) == ACTIVE )
               {
                  Learn_SetState(CT_LEARN_STATE__MAIN);
                  Learn_SetCmdFlag(LRN_CMD_ID__BACK_0, INACTIVE);
               }
               break;
            case CT_LEARN_STATE__LH_DN_BY_POS:
            case CT_LEARN_STATE__LH_DN_BY_DIS:
            case CT_LEARN_STATE__LH_UP_BY_POS:
            case CT_LEARN_STATE__LH_UP_BY_DIS:
               if( ePosSys != eInitPosSys || ePosSys != Learn_GetChosenPosSys())
               {
                  Learn_SetState(CT_LEARN_STATE__PARAM_CHNG_FLT);
               }
               else
               {
                  Learn_HoistwayLandings();
               }
               break;
            case CT_LEARN_STATE__LH_FIN_VALS:
               if( ePosSys != eInitPosSys || ePosSys != Learn_GetChosenPosSys())
               {
                  Learn_SetState(CT_LEARN_STATE__PARAM_CHNG_FLT);
               }
               else
               {
                  Learn_HoistwayFinVals();
               }
               break;
            case CT_LEARN_STATE__LH_SAVING:
               Learn_HoistwaySave();
               break;
            case CT_LEARN_STATE__ERROR:
            case CT_LEARN_STATE__LH_ERROR:
            case CT_LEARN_STATE__LH_XC_ERROR:
            case CT_LEARN_STATE__LH_SV_ERROR:
            case CT_LEARN_STATE__CAR_FAULT:
            case CT_LEARN_STATE__PARAM_CHNG_FLT:
            case CT_LEARN_STATE__LH_COMPLETE:
               Learn_SetCmdFlag(LRN_CMD_ID__DONE, ACTIVE);
               break;
            default:
               break;
         }
      }
      else
      {
         Learn_SetState(CT_LEARN_STATE__INACTIVE);
         Learn_ResetActiveUINode();
         Learn_SetCmdFlag(LRN_CMD_ID__DONE, INACTIVE);
      }
   }
   else
   {
      Learn_SetState(CT_LEARN_STATE__INACTIVE);
      Learn_ResetActiveUINode();
      Learn_SetActiveError(LRN_RET__NO_ERROR);
      // Deactivate all Learn commands.
      for(int16_t i = 0; i < NUM_LRN_CMD_IDS; i++)
      {
         Learn_SetCmdFlag(i, INACTIVE);
      }
   }

   // If Learn mode set but state is inactive, and no manual mode is set,
   // then raise alarm saying Learn mode is blocking auto-mode.
   en_manual_operations eManualOp = Operation_GetManualOperation();
   if( eSemiAutoOp == SEMIAUTO_OP__LEARN && Learn_GetState() == CT_LEARN_STATE__INACTIVE
         && (eManualOp == MANUAL_OP__OFF || eManualOp == MANUAL_OP__UNKNOWN))
   {
      Alarms_SetAlarm(CALM__LEARN_BLOCKING_AUTO);
   }

   vTaskDelay(LEARNING_TASK_DELAY);
  }
}
