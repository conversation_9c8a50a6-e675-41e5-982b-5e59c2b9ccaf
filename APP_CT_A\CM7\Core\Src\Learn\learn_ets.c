#include "Learn/learn_ets.h"
#include "ets_lookup.h"
#include "param_def.h"
#include "position_def.h"
#include "floor_def.h"
#include "dg_car_mr_a7.h"
#include "LearnDef/learn_usr_evt.h"

#include "ulog.h"

#include <stdlib.h>
#include <string.h>

ets_lut_t *ETS_entry;
ets_xcheck_t ETS_xcheck;
bool learn_complete;

static void Learn_ETS_WriteParams(learn_usr_t type,
    uint32_t relative_dist);
static en_active_inactive Learn_ETS_CheckParamSync(learn_usr_t type,
    uint32_t relative_dist);

learn_status_t Learn_ETS_Init(void)
{
  // Step 1. Select NTS positions from list
  learn_status_t status;
  uint16_t contract_speed = Param_ReadValue_16Bit(CPARAM16__Contract_Speed_fpm);
  for(uint32_t i = 0; i < ETS_TABLE_COLUMN_SIZE; i++)
  {
    ets_lut_t *ets = &ETS_LookupTable[i];
    if(contract_speed >= ets->speed)
    {
      ETS_entry = ets;
    }
    else
    {
      break;
    }
  }

  if(ETS_entry != NULL)
  {
    // Reset velocity and learn complete
    ETS_entry->velocity = 0;
    learn_complete  = false;

    memset(&ETS_xcheck, 0, sizeof(ets_xcheck_t));

    ULOG_DEBUG("ETS entry");
    ULOG_DEBUG("Speed = %i",
        ETS_entry->speed);

    status = LEARN_STATUS__OK;
  }
  else
  {
    status = LEARN_ETS_STATUS__SPEED_NOT_FOUND;
  }

  return status;
}

learn_status_t Learn_ETS_Record(void)
{
  learn_status_t status = LEARN_STATUS__BUSY;

  if(Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE)
  {
    return status;
  }

  // TODO: pre-compute everything into 05mm distances
  if(learn_complete == false)
  {
    uint32_t ETS_position   = (uint32_t)(ETS_entry->position * ONE_INCH__05MM);
    uint32_t Dist_To_Target = (uint32_t)abs(Motion_GetDestination_05mm_MRA() -
                                            Position_GetPosition_05mm());
    if(Dist_To_Target <= ETS_position)
    {
      /* TODO: There isn't a nice way to do cross check. CTA does not have access
       * to channel B speed.
       */
      ETS_entry->velocity = Position_GetSpeed_fpm();
      learn_complete      = true;

      ULOG_DEBUG("Dist_To_Target = %i, ETS_position = %i, ETS_speed = %i",
          Dist_To_Target,
          ETS_position,
          ETS_entry->velocity);
    }
  }
  else
  {
    status = LEARN_STATUS__OK;
  }

  return status;
}

learn_status_t Learn_ETS_SaveParams(learn_usr_t type)
{
  uint8_t floor;
  uint32_t floor_dist_05mm;
  if(type == LEARN_UNTS_UETS)
  {
    ULOG_DEBUG("Saving UETS parameters");
    floor           = Floor_GetNumberOfCarFloors() - 1;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);
  }
  else if(type == LEARN_DNTS_DETS)
  {
    ULOG_DEBUG("Saving DETS parameters");
    floor           = 0;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);
  }

  // Save default xcheck values if they are zero
  if(Param_ReadValue_8Bit(CPARAM8__Traction_ETS_Pos_Xcheck) == 0)
  {
    Param_WriteValue_8Bit(CPARAM8__Traction_ETS_Pos_Xcheck,
        DEFAULT_ETS_POS_XCHECK_VALUE);
  }

  if(Param_ReadValue_8Bit(CPARAM8__Traction_ETS_Speed_Xcheck) == 0)
  {
    Param_WriteValue_8Bit(CPARAM8__Traction_ETS_Speed_Xcheck,
        DEFAULT_ETS_SPEED_XCHECK_VALUE);
  }

  Learn_ETS_WriteParams(type, floor_dist_05mm);

  return LEARN_STATUS__OK;
}

learn_status_t Learn_ETS_ParamSync(learn_usr_t type)
{
  uint8_t floor;
  uint32_t floor_dist_05mm;
  if(type == LEARN_UNTS_UETS)
  {
    floor         = Floor_GetNumberOfCarFloors() - 1;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);
  }
  else if(type == LEARN_DNTS_DETS)
  {
    floor         = 0;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);
  }

  return (Learn_ETS_CheckParamSync(type, floor_dist_05mm) == ACTIVE) ?
      LEARN_STATUS__OK : LEARN_STATUS__BUSY;
}

learn_status_t Learn_ETS_SaveXCheck(uint8_t *msg_data)
{
  memcpy(&ETS_xcheck.position,    // ETS position
      &msg_data[0],
      sizeof(uint32_t));
  memcpy(&ETS_xcheck.velocity,    // ETS velocity
      &msg_data[sizeof(uint32_t)],
      sizeof(int16_t));

  return LEARN_STATUS__OK;
}

learn_status_t Learn_ETS_XCheck(void)
{
  learn_status_t status = LEARN_STATUS__OK;

  uint32_t pos_diff   = abs(ETS_entry->position - ETS_xcheck.position);
  uint32_t speed_diff = abs(ETS_entry->velocity - ETS_xcheck.velocity);

  // If xcheck thresholds are zero, use the default, otherwise use the specified
  // value from the parameters

  uint8_t xcheck_pos_max   = Param_ReadValue_8Bit(CPARAM8__Traction_ETS_Pos_Xcheck);
  uint8_t xcheck_speed_max = Param_ReadValue_8Bit(CPARAM8__Traction_ETS_Speed_Xcheck);

  xcheck_pos_max   = (xcheck_pos_max == 0) ?
      DEFAULT_ETS_POS_XCHECK_VALUE : xcheck_pos_max;
  xcheck_speed_max = (xcheck_speed_max == 0) ?
      DEFAULT_ETS_SPEED_XCHECK_VALUE : xcheck_speed_max;

  // TODO: make new ETS position and velocity xcheck parameters
  if((pos_diff   > xcheck_pos_max) ||
     (speed_diff > xcheck_speed_max))
  {
    status = LEARN_ETS_STATUS__XCHECK_FAIL;
  }

  return status;
}

static void Learn_ETS_WriteParams(learn_usr_t type,
    uint32_t relative_dist)
{
  uint32_t uiPosition_05mm;
  en_car_param_24bit pos_param_base;
  en_car_param_16bit speed_param_base;

  // ETS position
  if(type == LEARN_DNTS_DETS)
  {
    uiPosition_05mm  = (uint32_t)(relative_dist +
        (ETS_entry->position * ONE_INCH__05MM));

    pos_param_base   = CPARAM24__Traction_DETS_Pos;
    speed_param_base = CPARAM16__Traction_DETS_Speed;
  }
  else if(type == LEARN_UNTS_UETS)
  {
    uiPosition_05mm = (uint32_t)(relative_dist -
        (ETS_entry->position * ONE_INCH__05MM));

    pos_param_base   = CPARAM24__Traction_UETS_Pos;
    speed_param_base = CPARAM16__Traction_UETS_Speed;
  }

  Param_WriteValue_24Bit(pos_param_base, uiPosition_05mm);
  Param_WriteValue_16Bit(speed_param_base, ETS_entry->velocity);
}

static en_active_inactive Learn_ETS_CheckParamSync(learn_usr_t type,
    uint32_t relative_dist)
{
  en_active_inactive status = ACTIVE;
  uint32_t uiPosition_05mm;
  en_car_param_24bit pos_param_base;
  en_car_param_16bit speed_param_base;

  if(type == LEARN_UNTS_UETS)
  {
    uiPosition_05mm = (uint32_t)(relative_dist -
            (ETS_entry->position * ONE_INCH__05MM));

    pos_param_base   = CPARAM24__Traction_UETS_Pos;
    speed_param_base = CPARAM16__Traction_UETS_Speed;
  }
  else if(type == LEARN_DNTS_DETS)
  {
    pos_param_base   = CPARAM24__Traction_DETS_Pos;
    speed_param_base = CPARAM16__Traction_DETS_Speed;

    uiPosition_05mm = (uint32_t)(relative_dist +
            (ETS_entry->position * ONE_INCH__05MM));
  }

  uint32_t uiPosParam_05mm = Param_ReadValue_24Bit(pos_param_base);
  int16_t iSpeedParam_fpm  = (int16_t)Param_ReadValue_16Bit(speed_param_base);
  if( (uiPosParam_05mm != uiPosition_05mm) ||
      (iSpeedParam_fpm != ETS_entry->velocity) )
  {
    status = INACTIVE;
  }

  return status;
}


