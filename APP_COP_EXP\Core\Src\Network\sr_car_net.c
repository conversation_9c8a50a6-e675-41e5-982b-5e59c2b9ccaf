/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_car_net.c
* @version 		  : 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_fault_alarm.h"
#include <string.h>
#include "sr_car_net.h"
#include "sr_CAN1.h"
#include "sr_local_inputs.h"
#include "sr_local_outputs.h"
#include "sr_SPI_AU.h"
#include "car_output_def.h"
#include "main.h"
#include "car_faults_def.h"
#include "car_alarms_def.h"
#include "motion_def.h"
#include "app_can.h"
#include "app_gpio.h"
#include "app_spi.h"
#include "dg_car_mr_a7.h"
#include "dg_car_cop_a4.h"
#include "dg_car_exp.h"
#include "dg_group_riser.h"
#include "bootloader.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

#define MAX_PACKETS_TRANSMIT_PER_CYCLE                            (5)

#define PARAM_REQUEST_STARTUP_RESEND_RATE_1MS                     (100)
#define PARAM_REQUEST_BLOCK_RESEND_RATE_1MS                       (1000)
#define PARAM_REQUEST_OTHER_RESEND_RATE_1MS                       (1000)

#define EXPANSION_STATUS_RESEND_RATE_1MS                          (3000)

#define DEBUG_INFO_RESEND_INTERVAL_50MS							  (250)

#define NETWORK_INFO_RESEND_INTERVAL_1S						      (5)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
uint16_t gCarNet_Task_Delay = 0;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_datagram_control *pstControl = NULL;
static en_sys_network eLocalNetwork = SYS_NETWORK__CAR;

static en_active_inactive bDebugHBLedBlink = INACTIVE;
static uint32_t uiHBAbnormConfigAlmTimer_1ms = HALL_BOARD_ABNORM_CONFIG_ALARM_TIMEOUT_1MS;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/*******************************************************************************/
/*							COP Board Start							   		   */
/*******************************************************************************/
/* __weak START */
en_active_inactive Operation_CheckIfPreopening(en_door eDoor)
{
	return Operation_CheckIfPreopening_MRA(eDoor);
}
en_motion_state Motion_GetMotionState(void)
{
    return Motion_GetMotionState_MRA();
}
/* __weak END */
en_active_inactive GetTransmitFaultDatagramCheck(void)
{
	return bDebugHBLedBlink;
}

void SetTransmitFaultDatagramCheck(en_active_inactive bActive)
{
	bDebugHBLedBlink = bActive;
}

/* Load functions */
void DG_CAR_COP_A4_LoadData_Fault(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_COP_A4__Fault];
   st_fault_data *pstData = Faults_GetFaultData_ByNode(System_GetNodeID());
   if( pstData != NULL )
   {
	   pstDatagram->uwMinResendInterval_1ms = ( pstData->eFault == CFLT__NONE ) ? 3000 : 500;
	   DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Fault, (uint8_t *) pstData);
   }
}
void DG_CAR_COP_A4_LoadData_Alarm(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_COP_A4__Alarm];
   st_alarm_data *pstData = Alarms_GetAlarmData_ByNode(System_GetNodeID());
   if( pstData != NULL )
   {
	   pstDatagram->uwMinResendInterval_1ms = ( pstData->eAlarm == CALM__NONE ) ? 3000 : 500;
	   DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Alarm, (uint8_t *) pstData);
   }
}
void DG_CAR_COP_A4_LoadData_Param_Req(void)
{
   static en_param_blocks eLastBlock;
   static uint16_t uwResendCountdown_1ms;
   static st_param_req stReq;
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_CAR_COP_A4__Param_Req];
   if( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
   {
      en_param_blocks eBlock = Param_GetBlockToUpdate();
      if( pstDatagram->bDataChanged == INACTIVE )
      {
         if( Param_GetState() == PARAM_STATE__STARTUP )
         {
            if( uwResendCountdown_1ms >= PARAM_REQUEST_STARTUP_RESEND_RATE_1MS )
            {
               uwResendCountdown_1ms = 0;
               stReq.eType = 0;
               stReq.uiValue = 0;
               stReq.uwParamIndex = 0;
               stReq.eCommand = PARAM_COMMAND__STARTUP;
               DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Param_Req, (uint8_t *) &stReq);
               pstDatagram->bDataChanged = ACTIVE;
               stReq.eCommand = PARAM_COMMAND__NONE; /* Mark that a new packet can be unloaded */
            }
            else uwResendCountdown_1ms += pstDatagram->uwLoadInterval_1ms;
         }
         else if( eBlock < NUM_PARAM_BLOCKS )
         {
            if( ( eLastBlock != eBlock )
             || ( uwResendCountdown_1ms >= PARAM_REQUEST_BLOCK_RESEND_RATE_1MS ) )
            {
               uwResendCountdown_1ms = 0;
               stReq.eType = 0;
               stReq.uiValue = 0;
               stReq.uwParamIndex = 0;
               stReq.eCommand = PARAM_COMMAND__REQ_BLOCK_START + eBlock;
               DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Param_Req, (uint8_t *) &stReq);
               pstDatagram->bDataChanged = ACTIVE;
               stReq.eCommand = PARAM_COMMAND__NONE; /* Mark that a new packet can be unloaded */
            }
            else uwResendCountdown_1ms += pstDatagram->uwLoadInterval_1ms;
         }
         else if( ( stReq.eCommand != PARAM_COMMAND__NONE )
               || ( Param_GetRequest(&stReq) ) )
         {
            if( uwResendCountdown_1ms >= PARAM_REQUEST_OTHER_RESEND_RATE_1MS )
            {
               uwResendCountdown_1ms = 0;
               DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Param_Req, (uint8_t *) &stReq);
               pstDatagram->bDataChanged = ACTIVE;
               stReq.eCommand = PARAM_COMMAND__NONE; /* Mark that a new packet can be unloaded */
            }
            else uwResendCountdown_1ms += pstDatagram->uwLoadInterval_1ms;
         }
         else uwResendCountdown_1ms = 0xFFFF;
      }
      eLastBlock = eBlock;
   }
   else pstDatagram->bDataChanged = INACTIVE;
}
void DG_CAR_COP_A4_LoadData_CAN_Status(void)
{
	static uint8_t ucDelay_1s;
   un_datagram unData;
   if( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) && 0 ) //AS TODO enable with dip or parameter
   {
	   if( ucDelay_1s < NETWORK_INFO_RESEND_INTERVAL_1S )
	   {
		   ucDelay_1s++;
	   }
	   if( pstControl->pastDatagrams[DG_CAR_COP_A4__CAN_Status].bDataChanged == INACTIVE )
	   {
		   if( ucDelay_1s >= NETWORK_INFO_RESEND_INTERVAL_1S )
		   {
			   unData.auc8[0] = CAN1_GetStatusCounter(CAN_COUNTER__RX_PACKET);
			   unData.auc8[1] = CAN1_GetStatusCounter(CAN_COUNTER__TX_PACKET);
			   unData.auc8[2] = CAN1_GetStatusCounter(CAN_COUNTER__RX_OVERFLOW);
			   unData.auc8[3] = CAN1_GetStatusCounter(CAN_COUNTER__TX_OVERFLOW);
			   unData.auc8[4] = CAN1_GetStatusCounter(CAN_COUNTER__BUS_ERROR);
			   unData.auc8[5] = CAN1_GetStatusCounter(CAN_COUNTER__BUS_RESET);

			   unData.auc8[6] = CAN2_GetStatusCounter(CAN_COUNTER__RX_PACKET);
			   unData.auc8[7] = CAN2_GetStatusCounter(CAN_COUNTER__TX_PACKET);
			   unData.auc8[8] = CAN2_GetStatusCounter(CAN_COUNTER__RX_OVERFLOW);
			   unData.auc8[9] = CAN2_GetStatusCounter(CAN_COUNTER__TX_OVERFLOW);
			   unData.auc8[10] = CAN2_GetStatusCounter(CAN_COUNTER__BUS_ERROR);
			   unData.auc8[11] = CAN2_GetStatusCounter(CAN_COUNTER__BUS_RESET);

			   unData.auc8[12] = CAN3_GetStatusCounter(CAN_COUNTER__RX_PACKET);
			   unData.auc8[13] = CAN3_GetStatusCounter(CAN_COUNTER__TX_PACKET);
			   unData.auc8[14] = CAN3_GetStatusCounter(CAN_COUNTER__RX_OVERFLOW);
			   unData.auc8[15] = CAN3_GetStatusCounter(CAN_COUNTER__TX_OVERFLOW);
			   unData.auc8[16] = CAN3_GetStatusCounter(CAN_COUNTER__BUS_ERROR);
			   unData.auc8[17] = CAN3_GetStatusCounter(CAN_COUNTER__BUS_RESET);

			   DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__CAN_Status, &unData.auc8[0]);
			   pstControl->pastDatagrams[DG_CAR_COP_A4__CAN_Status].bDataChanged = ACTIVE;
			   ucDelay_1s = 0;
		   }
	   }
   }
}
void DG_CAR_COP_A4_LoadData_SPI_Status(void)
{
	static uint8_t ucDelay_1s;
   un_datagram unData;
   if( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) && 0 ) //AS TODO enable with dip or parameter
   {
	   if( ucDelay_1s < NETWORK_INFO_RESEND_INTERVAL_1S )
	   {
		   ucDelay_1s++;
	   }
	   if( pstControl->pastDatagrams[DG_CAR_COP_A4__SPI_Status].bDataChanged == INACTIVE )
	   {
		   if( ucDelay_1s >= NETWORK_INFO_RESEND_INTERVAL_1S )
		   {
			   unData.auc8[0] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__TX);
			   unData.auc8[1] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__RX);
			   unData.auc8[2] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__UNDERRUN);
			   unData.auc8[3] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__MODE);
			   unData.auc8[4] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__FRAME);
			   unData.auc8[5] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__CRC);
			   unData.auc8[6] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__TX_OVERFLOW);
			   unData.auc8[7] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__RX_OVERFLOW);
			   unData.auc8[8] = SPI_TX_GetCounter(SPI_AU, SPI_COUNTER__UNKNOWN);

			   unData.auc8[9] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__TX);
			   unData.auc8[10] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__RX);
			   unData.auc8[11] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__UNDERRUN);
			   unData.auc8[12] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__MODE);
			   unData.auc8[13] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__FRAME);
			   unData.auc8[14] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__CRC);
			   unData.auc8[15] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__TX_OVERFLOW);
			   unData.auc8[16] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__RX_OVERFLOW);
			   unData.auc8[17] = SPI_RX_GetCounter(SPI_AU, SPI_COUNTER__UNKNOWN);

			   DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__SPI_Status, &unData.auc8[0]);
			   pstControl->pastDatagrams[DG_CAR_COP_A4__SPI_Status].bDataChanged = ACTIVE;
			   ucDelay_1s = 0;
		   }
	   }
   }
}
void DG_CAR_COP_A4_LoadData_Version(void)
{
  // CT A7 application header address = 0x08020000
  const uint32_t ulAppHeaderAddress = 0x08020000;
  const app_header_t *xAppHeader = (const app_header_t*)ulAppHeaderAddress;

   un_datagram unData;
   unData.auw16[0] = xAppHeader->version.auwVers[0];
   unData.auw16[1] = xAppHeader->version.auwVers[1];
   unData.auw16[2] = xAppHeader->version.auwVers[2];
   unData.auw16[3] = xAppHeader->version.auwVers[3];
   DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Version, &unData.auc8[0]);
}
void DG_CAR_COP_A4_LoadData_Local_IO(void)
{
   un_datagram unData;
//   uint32_t input = LocalInputs_GetInputBitmap(0);

	unData.auc8[0] = LocalInputs_GetInputBitmap(0);
	unData.auc8[1] = LocalInputs_GetInputBitmap(1);
	unData.auc8[2] = LocalInputs_GetInputBitmap(2);
	unData.auc8[3] = LocalInputs_GetInputBitmap(3);
	unData.auc8[4] = LocalOutputs_GetOutputBitmap(0);
	unData.auc8[5] = LocalOutputs_GetOutputBitmap(1);
//	unData.auc8[5] = LocalOutputs_GetOutputBitmap(2);

   DGS_WriteDatagram(pstControl, DG_CAR_COP_A4__Local_IO, &unData.auc8[0]);
}
/*******************************************************************************/
/*							Expansion Board Start							   */
/*******************************************************************************/
/* @brief This load function is repurposed for all expansion status datagrams */
void DG_CAR_EXP_LoadData_Status_01(void)
{
   static uint16_t auwCounter_1ms[NUM_EXPANSION_ID];
   un_datagram unData;
   en_expansion_id eLocalExpansionIndex = Main_GetLocalExpansionID();

   for(uint8_t i = 0; i < NUM_EXPANSION_ID; i++)
   {
      if( eLocalExpansionIndex == i )
      {
         unData.aui32[0] = ( *( (uint32_t *)LocalInputs_GetInputBitmapPointer() ) & 0xFFFF00 );
         unData.auc8[0] = Error_GetActiveError() & 0x0F;
         unData.auc8[0] |= (LocalInputs_GetInputValue(enLOCAL_IN__DIP_SW_08) << 4);
         unData.auc8[0] |= (LocalInputs_GetInputValue(enLOCAL_IN__DIP_SW_07) << 5);
         unData.auc8[0] |= (LocalInputs_GetInputValue(enLOCAL_IN__DIP_SW_06) << 6);
         DGS_WriteDatagram(pstControl, DG_CAR_EXP__Status_01+i, &unData.auc8[0]);
         if( pstControl->pastDatagrams[DG_CAR_EXP__Status_01+i].bDataChanged == ACTIVE )
         {
            auwCounter_1ms[i] = 0;
         }
         else if( auwCounter_1ms[i] >= EXPANSION_STATUS_RESEND_RATE_1MS )
         {
            auwCounter_1ms[i] = 0;
            pstControl->pastDatagrams[DG_CAR_EXP__Status_01+i].bDataChanged = ACTIVE;
         }
         else
         {
            auwCounter_1ms[i] += CAR_NET_TASK_PERIODIC_DELAY_MS;
         }
         break;
      }
   }
}
/*******************************************************************************/
/*                   Riser Board Start                                         */
/*******************************************************************************/
/* @brief This load function is repurposed for all riser status datagrams */
void DG_GROUP_RISER_LoadData_Status_01(void)
{
   static uint16_t auwCounter_1ms[NUM_RISER_ID];
   un_datagram unData;
   en_riser_id eLocalRiserIndex = Main_GetLocalRiserID();

   for(uint8_t i = 0; i < NUM_RISER_ID; i++)
   {
      if( eLocalRiserIndex == i )
      {
         unData.aui32[0] = ( *( (uint32_t *)LocalInputs_GetInputBitmapPointer() ) & 0xFFFF00 );
         unData.auc8[0] = Error_GetActiveErrorRiser() & 0x0F;
         unData.auc8[0] |= (LocalInputs_GetInputValue(enLOCAL_IN__DIP_SW_08) << 4);
         unData.auc8[0] |= (LocalInputs_GetInputValue(enLOCAL_IN__DIP_SW_07) << 5);
         unData.auc8[0] |= (LocalInputs_GetInputValue(enLOCAL_IN__DIP_SW_06) << 6);
         DGS_WriteDatagram(pstControl, DG_GROUP_RISER__Status_01+i, &unData.auc8[0]);
         if( pstControl->pastDatagrams[DG_GROUP_RISER__Status_01+i].bDataChanged == ACTIVE )
         {
            auwCounter_1ms[i] = 0;
         }
         else if( auwCounter_1ms[i] >= RISER_STATUS_RESEND_RATE_1MS )
         {
            auwCounter_1ms[i] = 0;
            pstControl->pastDatagrams[DG_GROUP_RISER__Status_01+i].bDataChanged = ACTIVE;
         }
         else
         {
            auwCounter_1ms[i] += CAR_NET_TASK_PERIODIC_DELAY_MS;
         }
         break;
      }
   }
}

void DG_GROUP_RISER_LoadData_HallBoard_Active_Inputs(void)
{
   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_GROUP_RISER__HallBoard_Active_Inputs];
   un_datagram unData;
   en_active_inactive eNewDat = INACTIVE;
   memset(&unData.auc8[0], 0, pstDatagram->ucSize_Bytes);
   for( uint8_t ucI = 0; ucI < 32; ucI++ ) // max 32 uint16s in 64 byte dg
   {
      unData.auw16[ucI] = Hall_Board_DequeueActiveInput();
      if( unData.auw16[ucI] )
      {
         //printf("Load HB input: %x %s fileLn%d\n", unData.auw16[ucI], __FUNCTION__, __LINE__);
         eNewDat = ACTIVE;
      }
      else
      {
         // queue is empty
         break;
      }
   }
   if( eNewDat == ACTIVE )
   {
      DGS_WriteDatagram(pstControl, DG_GROUP_RISER__HallBoard_Active_Inputs, &unData.auc8[0]);
      pstDatagram->bDataChanged = ACTIVE;
   }
}

static en_car_alarm GetHBAlarm(uint8_t ucFunc, en_door eDoor, uint8_t ucFloor)
{
   en_car_alarm eAlarm = CALM__NONE;
   if(Hall_Board_GetConnection(HB_CON_TYPE__OFFLINE, ucFunc, eDoor, ucFloor) == ACTIVE)
   {
      eAlarm = CALM__HB_OFFLINE_FUNC_0_FRONT+ucFunc+(eDoor*MAX_NUM_HB_FUNC_SW_SETTINGS);/* AS todo review references to alarms, may cause desync if alarms change*/
   }
   else if(Hall_Board_GetConnection(HB_CON_TYPE__DETECTED, ucFunc, eDoor, ucFloor) == ACTIVE)
   {
      en_hb_error eErr = (Hall_Board_GetLatestStatus(ucFunc, eDoor, ucFloor) >> HB_DATA_SHIFT__FAULT) & HB_DATA_MASK__FAULT;
      if(eErr != HB_ERROR__NONE)
      {
         eAlarm = Hall_Board_GetAlarm(ucFunc, eDoor, eErr);
      }
   }
   return eAlarm;
}

void DG_GROUP_RISER_LoadData_HallBoard_Alarm(void)
{
   static uint8_t ucFunc;
   static en_door eDoor;
   static uint8_t ucLastAlmFunc;
   static en_door eLastAlmDoor;
   static uint8_t ucLastAlmFloor;
   en_car_alarm eAlarm = GetHBAlarm(ucLastAlmFunc, eLastAlmDoor, ucLastAlmFloor);
   if( eAlarm == CALM__NONE )
   {
      // Search for new hall board errors
      // Static door and func vars select for a different set of those with each module run.
      eDoor++;
      if( eDoor == NUM_DOORS )
      {
         eDoor = 0;
         ucFunc++;
         if( ucFunc == MAX_NUM_HB_FUNC_SW_SETTINGS ) ucFunc = 0;
      }
      for(uint8_t ucFloor = 0; ucFloor < MAX_NUM_CAR_FLOORS; ucFloor++)
      {
         eAlarm = GetHBAlarm(ucFunc, eDoor, ucFloor);
         if( eAlarm != CALM__NONE )
         {
            ucLastAlmFunc = ucFunc;
            eLastAlmDoor = eDoor;
            ucLastAlmFloor = ucFloor;
            break;
         }
      }
   }
   // Check for abnormallly configured hall boards
   if( Hall_Board_GetAbnormalConfigFlag() == ACTIVE )
   {
      uiHBAbnormConfigAlmTimer_1ms = 0;
      Hall_Board_SetAbnormalConfigFlag(INACTIVE);
   }
   else
   {
      uiHBAbnormConfigAlmTimer_1ms += HALL_NET_TASK_PERIODIC_DELAY_MS;
   }
   if(uiHBAbnormConfigAlmTimer_1ms >= HALL_BOARD_ABNORM_CONFIG_ALARM_TIMEOUT_1MS)
   {
      uiHBAbnormConfigAlmTimer_1ms = HALL_BOARD_ABNORM_CONFIG_ALARM_TIMEOUT_1MS;
   }
   else if( eAlarm == CALM__NONE || eAlarm == CALM__HB_ABNORMAL_FLOOR_SETTING )
   {
      st_hb_settings stHB = Hall_Board_GetAbnormalConfig();
      if( stHB.ucFloor >= MAX_NUM_CAR_FLOORS )
      {
         eAlarm = CALM__HB_ABNORMAL_FLOOR_SETTING;
         ucLastAlmFunc = stHB.ucFunc;
         eLastAlmDoor = stHB.ucDoor;
         ucLastAlmFloor = stHB.ucFloor;
      }
   }

   st_datagram *pstDatagram = &pstControl->pastDatagrams[DG_GROUP_RISER__HallBoard_Alarm];
   if( eAlarm == CALM__NONE )
   {
      pstDatagram->uwMinResendInterval_1ms = DG_MIN_RESEND_DISA;
      return;
   }
   pstDatagram->uwMinResendInterval_1ms = 1000;
   un_datagram unData;

   // Supress hb comm loss alarms during riser init
   static uint16_t uwInitTimer_1ms;
   if( (uwInitTimer_1ms < 20000) && (eAlarm >= CALM__HB_ERROR_COM_LOSS_FUNC_0_FRONT)
         && (eAlarm <= CALM__HB_ERROR_COM_LOSS_FUNC_9_REAR) )
   {
      eAlarm = CALM__NONE;
      uwInitTimer_1ms += pstDatagram->uwLoadInterval_1ms;
   }

   unData.auw16[0] = eAlarm;
   unData.auc8[2] = ucLastAlmFloor;
   DGS_WriteDatagram(pstControl, DG_GROUP_RISER__HallBoard_Alarm, &unData.auc8[0]);
}

static void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(uint8_t ucFunc)
{
   if( ucFunc >= MAX_NUM_HB_FUNC_SW_SETTINGS ) return;
   un_datagram unData;

   // copy 2 bitmaps with 3 uint32s per bitmap
   memcpy(&unData.aui32[0], Hall_Board_GetConnectionPointer(HB_CON_TYPE__DETECTED, ucFunc, DOOR_FRONT), 12);
   memcpy(&unData.aui32[3], Hall_Board_GetConnectionPointer(HB_CON_TYPE__DETECTED, ucFunc, DOOR_REAR), 12);
   unData.auc8[24] = Main_GetLocalRiserID();

   DGS_WriteDatagram(pstControl, DG_GROUP_RISER__HallBoard_Detected_Func_0+ucFunc, &unData.auc8[0]);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_0(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(0);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_1(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(1);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_2(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(2);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_3(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(3);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_4(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(4);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_5(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(5);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_6(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(6);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_7(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(7);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_8(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(8);
}
void DG_GROUP_RISER_LoadData_HallBoard_Detected_Func_9(void)
{
   DG_GROUP_RISER_LoadData_HallBoard_Detected_Func(9);
}

void DG_GROUP_RISER_LoadData_HallBoard_Data_Res(void)
{
   st_ui_request stRequest = { 0 };
   en_active_inactive eActive = UI_Request_GetRequest(&stRequest);
   if(eActive == INACTIVE || stRequest.eType != UI_REQUEST_TYPE__HALL_SCAN) return;
   un_datagram unData;

   if(stRequest.uwDetails_Ext == UI_HALL_SCAN_TYPE__HB)
   {
      UI_Request_HallScanLoadHBDG(&stRequest, &unData);
      DGS_WriteDatagram(pstControl, DG_GROUP_RISER__HallBoard_Data_Res, &unData.auc8[0]);
      pstControl->pastDatagrams[DG_GROUP_RISER__HallBoard_Data_Res].bDataChanged = ACTIVE;
   }
}

/* @fn static void TransmitHandler(void)
 * @brief Checks for datagrams to transmit
 * @param None
 * @return None
 */
static void TransmitHandler(void)
{
   st_CAN_msg stTxMsg;
   en_pass_fail eError;
   uint16_t uwDatagramIndex;
   st_datagram *pstDatagram;
   for(uint8_t i = 0; i < MAX_PACKETS_TRANSMIT_PER_CYCLE; i++)
   {
      if( DGS_GetNextDatagram(pstControl, &uwDatagramIndex) == PASS )
      {
         eError = PASS;
         pstDatagram = &pstControl->pastDatagrams[uwDatagramIndex];
         stTxMsg.bCAN_FD = 1;
         stTxMsg.bExtendedID = 1;
         stTxMsg.uiID = Network_GetPacketID(pstDatagram->ucSize_Bytes, uwDatagramIndex, eLocalNetwork, System_GetNodeID());
         stTxMsg.ucDLC = pstDatagram->ucSize_Bytes;
         memcpy(&stTxMsg.unData.auc8[0], pstDatagram->paucData, pstDatagram->ucSize_Bytes);
         if( Network_CheckDestination_Car(NET_FWD_PATH__CAR, pstDatagram->uiDestinationBitmap) == PASS
               || Main_GetLocalBoardType() == BOARD_RISER )
         {
            eError |= CAN1_TransmitDatagram(&stTxMsg);
         }

         if( Network_CheckDestination_Car(NET_FWD_PATH__UI, pstDatagram->uiDestinationBitmap) == PASS )
         {
        	 SPI_AU_TransmitDatagram(&stTxMsg);
         }

         /* If message has failed to send, remark it as the next packet due to send */
         if( eError == FAIL )
         {
            if( pstControl->uwLastSentDG ) pstControl->uwLastSentDG--;
            else pstControl->uwLastSentDG = pstControl->uwNumberOfDG-1;
            pstDatagram->bDataChanged = ACTIVE;
            break;
         }
         else
         {
#if ENABLE_CAR_TX_DEBUG
            printf("SC: %d\n", uwDatagramIndex);
#endif
            pstDatagram->ucPacketCounter++;
            if( uwDatagramIndex == DG_CAR_COP_A4__Fault )
            {
            	bDebugHBLedBlink = ACTIVE;
            }
         }
      }
      else
      {
         break;
      }
   }
}

/* @fn void CarNet_Task_Init(void)
 * @brief Initializes the CarNet task, selects the appropriate datagram control structure,
 *        and configures CAN interfaces based on the local board type
 * @param None
 * @return None
 */
void CarNet_Task_Init(void)
{
    // Get the local board type
    en_local_board_type eLocalBoardType = Main_GetLocalBoardType();

    // Select datagram control structure and local network based on board type
    switch(eLocalBoardType)
    {
        case BOARD_COP:
            pstControl = DG_CAR_COP_A4_GetDatagramControlStruct();
            eLocalNetwork = SYS_NETWORK__CAR;
            break;
        case BOARD_EXP_MASTER:
            eLocalNetwork = SYS_NETWORK__CAR;
            pstControl = DG_CAR_EXP_GetDatagramControlStruct();
            break;
        case BOARD_EXP_SLAVE:
            eLocalNetwork = SYS_NETWORK__INTERGROUP;
            pstControl = DG_CAR_EXP_GetDatagramControlStruct();
            break;
        case BOARD_RISER:
            eLocalNetwork = SYS_NETWORK__GROUP;
            pstControl = DG_GROUP_RISER_GetDatagramControlStruct();
            break;
        case BOARD_INVALID:
        default:
            break;
    }
    // If pstControl is valid, initialize CAN interfaces and datagram system
    if(pstControl != NULL)
    {
        switch(eLocalBoardType)
        {
            case BOARD_COP:
                CAN1_Init();
                break;
            case BOARD_EXP_MASTER:
            case BOARD_EXP_SLAVE:
                CAN1_Init();
                CAN2_Init();
                break;
            case BOARD_RISER:
				CAN1_Init_RISER_50K();
				CAN2_Init_RISER_50K();
				CAN3_Init_RISER_50K();
				break;
            case BOARD_INVALID:
            default:
                break;
        }
        // Set CarNet task delay and initialize Datagram System
        gCarNet_Task_Delay = CAR_NET_TASK_PERIODIC_DELAY_MS;
        DGS_Init(pstControl);
    }
    else
    {
        // If pstControl is NULL, set the task delay to inactive period
        gCarNet_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;//AS TODO disable task
    }
}


/* @fn void vCarNet_Task(void *pvParams)
 * @brief Handles CarNet tasks, including stack usage monitoring and datagram scheduling for transmission
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCarNet_Task(void *pvParams)
{
    switch( Main_GetLocalBoardType() )
    {
        case BOARD_COP:
        	vTaskDelay(CAR_NET_TASK_COP_FIRST_DELAY_MS);
            break;
        case BOARD_EXP_MASTER:
        case BOARD_EXP_SLAVE:
        	vTaskDelay(CAR_NET_TASK_EXP_FIRST_DELAY_MS);
            break;
        case BOARD_RISER:
        	vTaskDelay(CAR_NET_TASK_RIS_FIRST_DELAY_MS);
			break;
        default:
        	vTaskDelay(CAR_NET_TASK_COP_FIRST_DELAY_MS);
        	break;
    }
   // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1) {
	__disable_irq();
        // Load the Datagram scheduler
        DGS_LoadHandler(pstControl);

        // Transmit the scheduled datagrams
        TransmitHandler();
	__enable_irq();
		vTaskDelay(CAR_NET_TASK_PERIODIC_DELAY_MS); // Delay using the configured CarNet task period
    }
}
