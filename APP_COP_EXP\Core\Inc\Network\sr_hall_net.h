/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file           : sr_hall_net.h
* @version        : 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/
#ifndef _SR_HALL_H_
#define _SR_HALL_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
void HallNet_Task_Init(void);
void vHallNet_Task(void *pvParams);
#endif /* _SR_HALL_H_ */
