/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_FRAM.h
* @version 		  : 1.0.0
* @brief Subroutine manages communication with the FM25V05-GTR 512Kb SPI FRAM chip
* @details Subroutine manages communication with the FM25V05-GTR 512Kb SPI FRAM chip
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdio.h>
#include <string.h>
#include "app.h"
#include "sr_FRAM.h"
#include "fram_def.h"
#include "ui_request_def.h"
#include "van_stock_parameters.h"
#include "temp_eflash_loader.h"
#include "stm32h7xx_hal.h"
#include "app_spi.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

static en_pass_fail FRAM_WriteEnable(void);
static en_pass_fail FRAM_WriteArray(void *pTxBuffer, uint8_t ucTxBytes);
static en_pass_fail FRAM_ReadArray(void *pTxBuffer, void *pRxBuffer, uint8_t ucTxBytes, uint8_t ucRxBytes);
/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS             (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                (10)

#ifdef ENABLE_CT_OLD_FRAM
#define FRAM_OP_CODE__WREN                         (0x06) /* Set write enable latch */
#define FRAM_OP_CODE__WRDI                         (0x04) /* Reset write enable latch */
#define FRAM_OP_CODE__RDSR                         (0x05) /* Read status register */
#define FRAM_OP_CODE__WRSR                         (0x01) /* Write status register */
#define FRAM_OP_CODE__READ                         (0x03) /* Read memory code */
#define FRAM_OP_CODE__WRITE                        (0x02) /* Write memory code */
#define FRAM_OP_CODE__RDID                         (0x9F) /* Read device ID */
#define FRAM_OP_CODE__FSTRD                        (0x0B) /* Fast read memory code */
#define FRAM_OP_CODE__SLEEP                        (0xB9) /* Sleep mode */

#define FRAM_RW_HEADER_SIZE__BYTES                 (3)
#define TRANSMIT_TIMEOUT__1MS                      (10)
#else // ENABLE_CT_OLD_FRAM

#define FRAM_OP_CODE__WREN                         (0x06) /* Set write enable latch */
#define FRAM_OP_CODE__WRDI                         (0x04) /* Reset write enable latch */
#define FRAM_OP_CODE__RDSR                         (0x05) /* Read status register */
#define FRAM_OP_CODE__WRSR                         (0x01) /* Write status register */
#define FRAM_OP_CODE__READ                         (0x03) /* Read memory code */
#define FRAM_OP_CODE__WRITE                        (0x02) /* Write memory code */
#define FRAM_OP_CODE__RDID                         (0x9F) /* Read device ID */
#define FRAM_OP_CODE__FSTRD                        (0x0B) /* Fast read memory code */
#define FRAM_OP_CODE__SLEEP                        (0xB9) /* Sleep mode */
#define FRAM_OP_CODE__DPD						   (0XBA) /* Deep Power Down Mode */
#define FRAM_OP_CODE__RUID						   (0X4C) /* Read Unique ID */
#define FRAM_OP_CODE__WRSN						   (0XC2) /* Write Serial Number */
#define FRAM_OP_CODE__RDSN						   (0XC3) /* Read Serial Number */
#define FRAM_OP_CODE__SSWR						   (0X42) /* Write Special Sector */
#define FRAM_OP_CODE__SSRD						   (0X4B) /* Read Special Sector */
#define FRAM_OP_CODE__FSSRD						   (0X49) /* Fast Read Special Sector */

#define FRAM_RW_HEADER_SIZE__BYTES                 (4)
#define TRANSMIT_TIMEOUT__1MS                      (10)
#endif

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static en_fram_state eFRAM_State;
static en_fram_error eFRAM_Error;
static uint32_t uiDataLossAddress;
static const char * const psCheckTag = "VAN"; /* Indicator that FRAM has been defaulted */
static uint32_t uiLastAddress; /* Logs the last FRAM address accessed */

/* Repeatedly log the current position and speed of the car to ensure the car can recover at startup */
static st_position_speed stPositionSpeed;


/* Logged recall floor for emergency medical operation. MSB is the opening, LSB is the floor index plus 1 */
static uint16_t uwStoredMedicalOpening;

/* Flags for tracking emergency mode operations through a power cycle */
static uint32_t uiStoredEmergencyModeBitmap;

/* Flags for tracking faults that must survive a power cycle */
static uint16_t uwStoredLatchingFaultBitmap;

/* Tracking latched fire phase 1 trigger through power cycle */
static en_fire_phase_1_trigger eFirePhase1Trigger_Edited;
static en_fire_phase_1_trigger eFirePhase1Trigger_Stored;

static uint8_t bBackupTimeChange;
static uint8_t bDefaultTimeChange;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn en_pass_fail FRAM_WriteSingle(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes)
 * @brief Writes 1 copy of the given data array to a continuous FRAM address range starting with uiAddress
 * @param uiAddress, starting byte address on the FRAM chip to begin writing at
 * @param pTxData, pointer to array of bytes to write to the FRAM
 * @param ucNumTxBytes, number of data bytes to write to the FRAM
 * @return eError, returns 1 if the request has failed
 */
en_pass_fail FRAM_WriteSingleBlock(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes)
{
   uint8_t aucTempBuffer[FRAM_TX_BUFFER_SIZE__BYTES] = {0};
   memcpy(&aucTempBuffer[0], pTxData, ucNumTxBytes);
   en_pass_fail eError = FRAM_WriteData(uiAddress, aucTempBuffer, ucNumTxBytes);
   if( ( eFRAM_Error == FRAM_ERROR__NONE ) && ( eError == FAIL ) ) eFRAM_Error = FRAM_ERROR__RW_TIMEOUT;
   return eError;
}
en_pass_fail FRAM_ClearAllParamBlock(uint32_t uiAddressBlock)
{
	uint8_t aucTempTxBuffer[FRAM_TX_BUFFER_SIZE__BYTES] = {0};
	uint32_t tempAddressBlock = uiAddressBlock;
	en_pass_fail eError = PASS;
	for(uint8_t i = 0; i < NUM_PARAM_BLOCKS; i++)
	{
	   eError |= FRAM_WriteEnable();
	   aucTempTxBuffer[0] = FRAM_OP_CODE__WRITE;
	   aucTempTxBuffer[1] = ( tempAddressBlock >> 16 ) & 0xFF;
	   aucTempTxBuffer[2] = ( tempAddressBlock >> 8 ) & 0xFF;
	   aucTempTxBuffer[3] = ( tempAddressBlock >> 0 ) & 0xFF;

	   eError |= FRAM_WriteArray(aucTempTxBuffer, EFLASH_PARAM_BLOCK_SIZE__BYTES + FRAM_RW_HEADER_SIZE__BYTES);
	   tempAddressBlock += EFLASH_PARAM_BLOCK_SIZE__BYTES;
	   memset(&aucTempTxBuffer[0], 0, FRAM_TX_BUFFER_SIZE__BYTES);
	}
	return eError;
}

en_pass_fail FRAM_WriteParamBlock(uint32_t uiAddressBlock)
{
	uint8_t aucTempTxBuffer[FRAM_TX_BUFFER_SIZE__BYTES] = {0};
	uint32_t tempAddressBlock = uiAddressBlock;
#if TEMP_EFLASH_LOADER_PARAM_INFO
	   const uint8_t * tempPtr = Temp_EFlash_Loader_GetArrayPtr();
#else
	   const uint8_t * tempPtr = Van_Stock_Parameters_GetArrayPtr();
#endif
	uint32_t ptrCounter = 0;
	en_pass_fail eError = PASS;
	for(uint8_t i = 0; i < NUM_PARAM_BLOCKS; i++)
	{
	   eError |= FRAM_WriteEnable();
	   aucTempTxBuffer[0] = FRAM_OP_CODE__WRITE;
	   aucTempTxBuffer[1] = ( tempAddressBlock >> 16 ) & 0xFF;
	   aucTempTxBuffer[2] = ( tempAddressBlock >> 8 ) & 0xFF;
	   aucTempTxBuffer[3] = ( tempAddressBlock >> 0 ) & 0xFF;

	   memcpy(&aucTempTxBuffer[FRAM_RW_HEADER_SIZE__BYTES], &tempPtr[ptrCounter] , EFLASH_PARAM_BLOCK_SIZE__BYTES);
	   eError |= FRAM_WriteArray(aucTempTxBuffer, EFLASH_PARAM_BLOCK_SIZE__BYTES + FRAM_RW_HEADER_SIZE__BYTES);
	   tempAddressBlock += EFLASH_PARAM_BLOCK_SIZE__BYTES;
	   ptrCounter += EFLASH_PARAM_BLOCK_SIZE__BYTES;
	   memset(&aucTempTxBuffer[0], 0, FRAM_TX_BUFFER_SIZE__BYTES);
	}
	return eError;
}
en_pass_fail FRAM_WriteJobNameBlock(const uint8_t *pucName)
{
	uint8_t aucTempTxBuffer[FRAM_TX_BUFFER_SIZE__BYTES] = {0};
	uint32_t tempAddressBlock = FRAM_START_ADDRESS__JOB_NAME;
	const uint8_t * tempPtr = pucName;
	en_pass_fail eError = PASS;

	eError |= FRAM_WriteEnable();
	aucTempTxBuffer[0] = FRAM_OP_CODE__WRITE;
	aucTempTxBuffer[1] = ( tempAddressBlock >> 16 ) & 0xFF;
	aucTempTxBuffer[2] = ( tempAddressBlock >> 8 ) & 0xFF;
	aucTempTxBuffer[3] = ( tempAddressBlock >> 0 ) & 0xFF;

	memcpy(&aucTempTxBuffer[FRAM_RW_HEADER_SIZE__BYTES], &tempPtr[0] , FRAM_JOB_NAME_SIZE__BYTES);
	eError |= FRAM_WriteArray(aucTempTxBuffer, FRAM_JOB_NAME_SIZE__BYTES + FRAM_RW_HEADER_SIZE__BYTES);
	return eError;
}

en_pass_fail FRAM_VerifyParamBlock(uint32_t uiAddressBlock, uint8_t ucNumRxBytes, en_fram_writes_type enType)
{
   uint8_t aucTempBuffer[FRAM_RX_BUFFER_SIZE__BYTES] = {0};
   st_param_block paramValue[3];
   en_pass_fail crcError[3] = {SUCCESS};
   uint32_t startAddress = uiAddressBlock;
   en_pass_fail eError = PASS;
   for(uint8_t i = 0; i < enType; i++)
   {
	   eError |= FRAM_ReadData(startAddress, aucTempBuffer, ucNumRxBytes);
	   if( ( eFRAM_Error == FRAM_ERROR__NONE ) && ( eError == FAIL ) ) eFRAM_Error = FRAM_ERROR__RW_TIMEOUT;
	   if(eError != FAIL)
	   {
		   memcpy(&paramValue[i].uwBlockIndex, &aucTempBuffer[0], EFLASH_SIZE_OF_SECTOR_HEADER__BYTES);
		   memcpy(&paramValue[i].uiCRC, &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES], EFLASH_SIZE_OF_SECTOR_CRC__BYTES);
		   memcpy(&paramValue[i].aucData[0], &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES + EFLASH_SIZE_OF_SECTOR_CRC__BYTES], NUM_DATA_BYTES_PER_PARAM_BLOCK);

		   uint32_t tempCRC = CRC_Calculate((uint32_t *)&paramValue[i].aucData[0], NUM_DATA_U32_PER_PARAM_BLOCK);
		   if(tempCRC != paramValue[i].uiCRC)
		   {
			   crcError[i] |= FAIL;
		   }
		   startAddress += FRAM_PARAMETERS_MAIN_SIZE__BYTES;
		   memset(&aucTempBuffer[0], 0, FRAM_RX_BUFFER_SIZE__BYTES);
	   }
	   else
	   {
		   break;
	   }
   }

   if((enType == FRAM_WRITES_TYPE__TRIPLET) && (eError != FAIL))
   {
	   if(crcError[0] == PASS &&
		  crcError[1] == PASS &&
		  crcError[2] == PASS)
	   {

	   }
	   else if(crcError[0] == PASS &&
			   crcError[1] == PASS)
	   {
		   eError |= FRAM_WriteData(uiAddressBlock+(2*uiAddressBlock), &paramValue[0], ucNumRxBytes); // Overwrite the corrupted third copy
		   if( eFRAM_Error == FRAM_ERROR__NONE ) eFRAM_Error = FRAM_ERROR__DATA_CORRUPTION;
	   }
	   else if(crcError[1] == PASS &&
			   crcError[2] == PASS)
	   {
		   eError |= FRAM_WriteData(uiAddressBlock, &aucTempBuffer[1], ucNumRxBytes); // Overwrite the corrupted first copy
		   if( eFRAM_Error == FRAM_ERROR__NONE ) eFRAM_Error = FRAM_ERROR__DATA_CORRUPTION;
	   }
	   else if(crcError[0] == PASS &&
			   crcError[2] == PASS)
	   {
		   eError |= FRAM_WriteData(uiAddressBlock+uiAddressBlock, &aucTempBuffer[2], ucNumRxBytes); // Overwrite the corrupted second copy
		   if( eFRAM_Error == FRAM_ERROR__NONE ) eFRAM_Error = FRAM_ERROR__DATA_CORRUPTION;
	   }
	   else
	   {
		   eFRAM_Error = FRAM_ERROR__DATA_LOSS;
		   eError = FAIL;
	   }
   }

   return eError;
}
uint8_t FRAM_CheckParamBlockEflashCRCNonZero(uint32_t uiAddressBlock)
{
	uint8_t aucTempBuffer[FRAM_RX_BUFFER_SIZE__BYTES] = {0};
	st_param_block paramValue[3];
	en_active_inactive aeCRC[3] = {INACTIVE};
	uint32_t startAddress = uiAddressBlock;
	en_pass_fail eError = PASS;
	for(uint8_t ucSection = 0; ucSection < 3; ucSection++)
	{
	   eError |= FRAM_ReadData(startAddress, aucTempBuffer, EFLASH_PARAM_BLOCK_SIZE__BYTES);
	   if(eError != FAIL)
	   {
		   memcpy(&paramValue[ucSection].uiCRC, &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES], EFLASH_SIZE_OF_SECTOR_CRC__BYTES);

		   if( paramValue[ucSection].uiCRC )
		   {
			   aeCRC[ucSection] = ACTIVE;
		   }
		   startAddress += FRAM_PARAMETERS_MAIN_SIZE__BYTES;
		   memset(&aucTempBuffer[0], 0, FRAM_RX_BUFFER_SIZE__BYTES);
	   }
	   else
	   {
		   break;
	   }
	}
	return ( aeCRC[0] || aeCRC[1] || aeCRC[2] );
}
en_pass_fail FRAM_InitializeAllParamBlockCRC(uint32_t uiAddressBlock)
{
   uint8_t aucTempBuffer[FRAM_TX_BUFFER_SIZE__BYTES] = {0};
   st_param_block paramValue[NUM_PARAM_BLOCKS];
   uint32_t tempAddressBlock = uiAddressBlock;
   en_pass_fail eError = PASS;
   for(uint8_t i = 0; i < NUM_PARAM_BLOCKS; i++)
   {
	   /* Read param block */
	   eError |= FRAM_ReadData(tempAddressBlock, aucTempBuffer, EFLASH_PARAM_BLOCK_SIZE__BYTES);
	   /* Save temporary copy */
	   memcpy(&paramValue[i].uwBlockIndex, &aucTempBuffer[0], EFLASH_SIZE_OF_SECTOR_HEADER__BYTES);
	   memcpy(&paramValue[i].uiCRC, &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES], EFLASH_SIZE_OF_SECTOR_CRC__BYTES);
	   memcpy(&paramValue[i].aucData[0], &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES + EFLASH_SIZE_OF_SECTOR_CRC__BYTES], NUM_DATA_BYTES_PER_PARAM_BLOCK);
	   /* Recalculate CRC */
	   paramValue[i].uiCRC = CRC_Calculate((uint32_t *)&paramValue[i].aucData[0], NUM_DATA_U32_PER_PARAM_BLOCK);
	   /* Copy content back to buffer */
	   memset(&aucTempBuffer[0], 0, FRAM_TX_BUFFER_SIZE__BYTES);
	   memcpy(&aucTempBuffer[FRAM_RW_HEADER_SIZE__BYTES], &paramValue[i].uwBlockIndex, EFLASH_SIZE_OF_SECTOR_HEADER__BYTES);
	   memcpy(&aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES+FRAM_RW_HEADER_SIZE__BYTES], &paramValue[i].uiCRC, EFLASH_SIZE_OF_SECTOR_CRC__BYTES);
	   memcpy(&aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES + EFLASH_SIZE_OF_SECTOR_CRC__BYTES + FRAM_RW_HEADER_SIZE__BYTES], &paramValue[i].aucData[0], NUM_DATA_BYTES_PER_PARAM_BLOCK);

	   eError |= FRAM_WriteEnable();
	   aucTempBuffer[0] = FRAM_OP_CODE__WRITE;
	   aucTempBuffer[1] = ( tempAddressBlock >> 16 ) & 0xFF;
	   aucTempBuffer[2] = ( tempAddressBlock >> 8 ) & 0xFF;
	   aucTempBuffer[3] = ( tempAddressBlock >> 0 ) & 0xFF;

	   eError |= FRAM_WriteArray(aucTempBuffer, EFLASH_PARAM_BLOCK_SIZE__BYTES + FRAM_RW_HEADER_SIZE__BYTES);
	   tempAddressBlock += EFLASH_PARAM_BLOCK_SIZE__BYTES;
	   memset(&aucTempBuffer[0], 0, FRAM_TX_BUFFER_SIZE__BYTES);
   }
   return eError;
}
en_pass_fail FRAM_ReadParamBlock(uint32_t uiAddressBlock, char * buffer, uint8_t ucNumRxBytes)
{
   uint8_t aucTempBuffer[FRAM_RX_BUFFER_SIZE__BYTES] = {0};
   en_pass_fail eError = FRAM_ReadData(uiAddressBlock, aucTempBuffer, ucNumRxBytes);

   memcpy(&buffer[0], &aucTempBuffer[0], EFLASH_SIZE_OF_SECTOR_HEADER__BYTES);
   memcpy(&buffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES], &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES], EFLASH_SIZE_OF_SECTOR_CRC__BYTES);
   memcpy(&buffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES + EFLASH_SIZE_OF_SECTOR_CRC__BYTES], &aucTempBuffer[EFLASH_SIZE_OF_SECTOR_HEADER__BYTES + EFLASH_SIZE_OF_SECTOR_CRC__BYTES], NUM_DATA_BYTES_PER_PARAM_BLOCK);

   return eError;
}

/* @fn en_pass_fail FRAM_ReadData(uint32_t uiAddress, void *pucRxData, uint8_t ucNumRxBytes)
 * @brief Command to read bytes from the FRAM chip starting with a specified address
 * @param uiAddress, starting byte address on the FRAM chip to begin reading from
 * @param pucRxData, pointer to receiving array
 * @param ucNumRxBytes, size of receiving array in bytes
 * @return eError, returns 1 if the request has failed
 */
en_pass_fail FRAM_ReadData(uint32_t uiAddress, void *pucRxData, uint8_t ucNumRxBytes)
{
   uint8_t aucTempTxBuffer[FRAM_RW_HEADER_SIZE__BYTES] = {0};
   aucTempTxBuffer[0] = FRAM_OP_CODE__READ;
//   aucTempTxBuffer[1] = uiAddress & 0xFF;
//   aucTempTxBuffer[2] = ( uiAddress >> 8 ) & 0xFF;
//   aucTempTxBuffer[3] = ( uiAddress >> 16 ) & 0xFF;
#ifdef ENABLE_CT_OLD_FRAM
   aucTempTxBuffer[1] = ( uiAddress >> 8 ) & 0xFF;
   aucTempTxBuffer[2] = ( uiAddress >> 0 ) & 0xFF;
#else
   aucTempTxBuffer[1] = ( uiAddress >> 16 ) & 0xFF;
   aucTempTxBuffer[2] = ( uiAddress >> 8 ) & 0xFF;
   aucTempTxBuffer[3] = ( uiAddress >> 0 ) & 0xFF;
#endif
   return FRAM_ReadArray(aucTempTxBuffer, pucRxData, FRAM_RW_HEADER_SIZE__BYTES, ucNumRxBytes);
}

/* @fn en_pass_fail FRAM_ReadData(uint32_t uiAddress, void *pucTxData, uint8_t ucNumTxBytes)
 * @brief Command to write bytes to the FRAM chip starting with a specified address
 * @param uiAddress, starting byte address on the FRAM chip to begin writing at
 * @param pucTxData, pointer to array of bytes to write to the FRAM
 * @param ucNumTxBytes, number of data bytes to write to the FRAM
 * @return eError, returns 1 if the request has failed
 */
en_pass_fail FRAM_WriteData(uint32_t uiAddress, void *pucTxData, uint8_t ucNumTxBytes)
{
   uint8_t aucTempTxBuffer[FRAM_TX_BUFFER_SIZE__BYTES] = {0};
   en_pass_fail eError = FRAM_WriteEnable();
   aucTempTxBuffer[0] = FRAM_OP_CODE__WRITE;
#ifdef ENABLE_CT_OLD_FRAM
   aucTempTxBuffer[1] = ( uiAddress >> 8 ) & 0xFF;
   aucTempTxBuffer[2] = ( uiAddress >> 0 ) & 0xFF;
#else
   aucTempTxBuffer[1] = ( uiAddress >> 16 ) & 0xFF;
   aucTempTxBuffer[2] = ( uiAddress >> 8 ) & 0xFF;
   aucTempTxBuffer[3] = ( uiAddress >> 0 ) & 0xFF;
#endif
   memcpy(&aucTempTxBuffer[FRAM_RW_HEADER_SIZE__BYTES], pucTxData, ucNumTxBytes);
   eError |= FRAM_WriteArray(aucTempTxBuffer, ucNumTxBytes + FRAM_RW_HEADER_SIZE__BYTES);
   return eError;
}


/* @fn static en_pass_fail FRAM_WriteEnable(void)
 * @brief This command must preceed any write request to the FRAM
 * @param None
 * @return None
 */
static en_pass_fail FRAM_WriteEnable(void)
{
   uint8_t ucCommand = FRAM_OP_CODE__WREN;
   __disable_irq();
   en_pass_fail eError = FRAM_WriteArray(&ucCommand, 1);
   __enable_irq();
   return eError;
}

/* @fn static en_pass_fail FRAM_WriteArray(uint8_t *pTxBuffer, uint8_t ucTxBytes)
 * @brief Base write command to transmit an byte array to the FRAM chip
 * @param pTxBuffer, pointer to array of bytes to send over SPI
 * @param ucTxBytes, size of pTxBuffer in bytes
 * @return eError, returns 1 if the request has failed
 */
static en_pass_fail FRAM_WriteArray(void *pTxBuffer, uint8_t ucTxBytes)
{
   en_pass_fail eError;
   // todo simplify HAL_SPI_Transmit with split header field & test with ISR disabling
      __disable_irq();
   HAL_StatusTypeDef eStatus = HAL_SPI_Transmit(&hspi4, pTxBuffer, ucTxBytes, TRANSMIT_TIMEOUT__1MS);
      __enable_irq();
   if( eStatus == HAL_OK )
   {
      eError = PASS;
   }
   else
   {
      printf("WR: %u\n", eStatus);
      eError = FAIL;
   }
   return eError;
}
/* @fn static en_pass_fail FRAM_ReadArray(uint8_t *pTxBuffer, uint8_t *pRxBuffer, uint8_t ucTxBytes, uint8_t ucRxBytes)
 * @brief Base read command to transmit an byte array to the FRAM chip and read back a specified number of bytes
 * @param pTxBuffer, pointer to array of bytes to send over SPI
 * @param pRxBuffer, pointer to array of of bytes to receive over SPI
 * @param ucTxBytes, size of pTxBuffer in bytes
 * @param ucRxBytes, size of pRxBuffer in bytes
 * @return eError, returns 1 if the request has failed
 */
static en_pass_fail FRAM_ReadArray(void *pTxBuffer, void *pRxBuffer, uint8_t ucTxBytes, uint8_t ucRxBytes)
{
   /* Default STM API returns the garbage RX bytes while the transmit is in progress. Buffer this data and process it before return todo improve garbage driver */
   uint8_t aucTempRxBuffer[FRAM_RX_BUFFER_SIZE__BYTES] = {0};
   en_pass_fail eError;
   // todo simplify HAL_SPI_Transmit with split header field and ignoring bytes received during transmission & test with ISR disabling
      __disable_irq();
   HAL_StatusTypeDef eStatus = HAL_SPI_TransmitReceive(&hspi4, pTxBuffer, aucTempRxBuffer, ucTxBytes+ucRxBytes, TRANSMIT_TIMEOUT__1MS);
      __enable_irq();
   if( eStatus == HAL_OK )
   {
      memcpy(pRxBuffer, &aucTempRxBuffer[ucTxBytes], ucRxBytes); /* Discard garbage rx bytes */
      eError = PASS;
   }
   else
   {
      printf("RD: %u\n", eStatus);
      eError = FAIL;
   }
   return eError;
}
/* @fn en_fram_state FRAM_GetState(void)
 * @brief Returns the state of the FRAM routine
 * @param None
 * @return eFRAM_State, returns the state of the FRAM routine
 */
en_fram_state FRAM_GetState(void)
{
   return eFRAM_State;
}

/* @brief Returns pointer to backup time change flag, 1 when value should be resent  */
uint8_t *FRAM_GetBackupTimeChangedFlag(void)
{
   return &bBackupTimeChange;
}
/* @brief Returns pointer to default time change flag, 1 when value should be resent  */
uint8_t *FRAM_GetDefaultTimeChangedFlag(void)
{
   return &bDefaultTimeChange;
}
/* @fn st_position_speed * FRAM_GetPositionSpeed(void)
 * @brief Returns pointer to the last car position/speed captured before power off
 * @param None
 * @return Returns pointer to the last car position/speed captured before power off
 */
st_position_speed * FRAM_GetPositionSpeed(void)
{
   return &stPositionSpeed;
}
/* @brief Retrieves the latched fire phase 1 trigger state saved to FRAM */
en_fire_phase_1_trigger FRAM_GetFirePhase1Trigger(void)
{
   return eFirePhase1Trigger_Stored;
}
/* @brief Sets the fire phase 1 trigger state saved to FRAM */
void FRAM_SetFirePhase1Trigger(en_fire_phase_1_trigger eTrigger)
{
   eFirePhase1Trigger_Edited = eTrigger;
}

/* @fn static void FRAM_UpdatePositionSpeed(void)
 * @brief Update the last captured speed and position of the car (10^13 WR endurance, 10ms interval -> 3,170 yr)
 * @param None
 * @return None
 */
static void FRAM_UpdatePositionSpeed(void)
{
   if( ( stPositionSpeed.uiPosition_05mm != Position_GetPosition_05mm() )
    || ( stPositionSpeed.wSpeed_fpm != Position_GetSpeed_fpm() ) )
   {
      stPositionSpeed.uiPosition_05mm = Position_GetPosition_05mm();
      stPositionSpeed.wSpeed_fpm = Position_GetSpeed_fpm();
      FRAM_WriteTriplet(FRAM_START_ADDRESS__POSITION_SPEED, &stPositionSpeed, FRAM_POSITION_SPEED_SIZE__BYTES);
   }
}

/* @fn static void FRAM_UpdateDirectionChangeCount(void)
 * @brief Update the direction change counter on the chip
 * @param None
 * @return None
 */
static void FRAM_UpdateDirectionChangeCount(void)
{
   static en_motion_dir eLastDir = MOTION_DIR__UP;
   en_motion_dir eDir = Motion_GetMotionDirection();
   if( ( eDir != MOTION_DIR__STOPPED )
    && ( eDir != eLastDir ) )
   {
      uint32_t uiDirectionChangeCount = FRAM_GetDirectionChangeCount()+1;
      FRAM_WriteTriplet(FRAM_START_ADDRESS__DIRECTION_CHANGE_COUNT, &uiDirectionChangeCount, FRAM_DIRECTION_CHANGE_COUNT_SIZE__BYTES);
      FRAM_SetDirectionChangeCount(uiDirectionChangeCount);
      eLastDir = eDir;
   }
}

/* @fn static void FRAM_UpdateRunCount(void)
 * @brief Update the run counter on the chip
 * @param None
 * @return None
 */
static void FRAM_UpdateRunCount(void)
{
   static en_active_inactive bLastRunningFlag;
   en_active_inactive bRunningFlag = Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING);
   if( !bLastRunningFlag && bRunningFlag )
   {
      uint32_t uiRunCount = FRAM_GetRunCount()+1;
      FRAM_WriteTriplet(FRAM_START_ADDRESS__RUN_COUNT, &uiRunCount, FRAM_RUN_COUNT_SIZE__BYTES);
      FRAM_SetRunCount(uiRunCount);
   }
   bLastRunningFlag = bRunningFlag;
}

/* @fn static void FRAM_UpdateMedicalOpening(void)
 * @brief Update the medical opening on the FRAM
 * @param None
 * @return None
 */
static void FRAM_UpdateMedicalOpening(void)
{
   uint16_t uwEditedMedicalOpening = FRAM_GetMedicalOpening();
   if( uwEditedMedicalOpening != uwStoredMedicalOpening )
   {
      FRAM_WriteTriplet(FRAM_START_ADDRESS__MEDICAL_OPENING, &uwEditedMedicalOpening, FRAM_MEDICAL_OPENING_SIZE__BYTES);
      FRAM_ReadTriplet(FRAM_START_ADDRESS__MEDICAL_OPENING, &uwStoredMedicalOpening, FRAM_MEDICAL_OPENING_SIZE__BYTES);
   }
}

/* @fn static void FRAM_UpdateEmergencyModeBitmap(void)
 * @brief Update the emergency mode bitmap on the FRAM
 * @param None
 * @return None
 */
static void FRAM_UpdateEmergencyModeBitmap(void)
{
   uint32_t uiEditedEmergencyModeBitmap = FRAM_GetEmergencyModeBitMap();
   if( uiEditedEmergencyModeBitmap != uiStoredEmergencyModeBitmap )
   {
      FRAM_WriteTriplet(FRAM_START_ADDRESS__EMERGENCY_MODE_BITMAP, &uiEditedEmergencyModeBitmap, FRAM_EMERGENCY_MODE_BITMAP_SIZE__BYTES);
      FRAM_ReadTriplet(FRAM_START_ADDRESS__EMERGENCY_MODE_BITMAP, &uiStoredEmergencyModeBitmap, FRAM_EMERGENCY_MODE_BITMAP_SIZE__BYTES);
   }
}
/* @fn static void FRAM_UpdateLatchingFaultBitmap(void)
 * @brief Update the latched fault bitmap on the FRAM
 * @param None
 * @return None
 */
static void FRAM_UpdateLatchingFaultBitmap(void)
{
   uint16_t uwEditedLatchingFaultBitmap = FRAM_GetLatchingFaultBitMap();
   if( uwEditedLatchingFaultBitmap != uwStoredLatchingFaultBitmap )
   {
      FRAM_WriteTriplet(FRAM_START_ADDRESS__LATCHING_FAULT_BITMAP, &uwEditedLatchingFaultBitmap, FRAM_EMERGENCY_MODE_BITMAP_SIZE__BYTES);
      FRAM_ReadTriplet(FRAM_START_ADDRESS__LATCHING_FAULT_BITMAP, &uwStoredLatchingFaultBitmap, FRAM_EMERGENCY_MODE_BITMAP_SIZE__BYTES);
   }
}

/* @brief Updates the latched fire phase 1 trigger status stored on FRAM when changed */
static void FRAM_UpdateFirePhase1Trigger(void)
{
   if( eFirePhase1Trigger_Edited != eFirePhase1Trigger_Stored )
   {
      FRAM_WriteTriplet(FRAM_START_ADDRESS__FIRE_PHASE_1_TRIGGER, &eFirePhase1Trigger_Edited, FRAM_FIRE_PHASE_1_TRIGGER_SIZE__BYTES);
      FRAM_ReadTriplet(FRAM_START_ADDRESS__FIRE_PHASE_1_TRIGGER, &eFirePhase1Trigger_Stored, FRAM_FIRE_PHASE_1_TRIGGER_SIZE__BYTES);
   }
}

/* @fn static void FRAM_State_CheckTag(void)
 * @brief Check for marker that this FRAM has been defaulted already, if not found, the device is either bad or must be defaulted
 * @param None
 * @return None
 */
static void FRAM_State_CheckTag(void)
{
   //FRAM_STATE__CHECK_TAG
   uint8_t aucTag[FRAM_STARTING_TAG_SIZE__BYTES];
   FRAM_ReadTriplet(FRAM_START_ADDRESS__STARTING_TAG, aucTag, FRAM_STARTING_TAG_SIZE__BYTES);
   if( memcmp(aucTag, psCheckTag, FRAM_STARTING_TAG_SIZE__BYTES) == 0 ) /* If tag found, skip defaulting */
   {
      eFRAM_State = FRAM_STATE__STARTUP_READ;
   }
   else
   {
      eFRAM_State = FRAM_STATE__DEFAULTING;
#if ENABLE_FRAM_DEBUG
   printf("FRAM TAG\n");
#endif
   }
}
/* @fn static void FRAM_State_Defaulting(void)
 * @brief FRAM memory is being cleared
 * @param None
 * @return None
 */
static void FRAM_State_Defaulting(void)
{
   //FRAM_STATE__DEFAULTING
#if ENABLE_FRAM_DEBUG
   int32_t iStartTime_1us = Timer_GetCount_1us();
#endif
   uint8_t aucClearBuffer[FRAM_MAX_BYTES_TO_WRITE_PER_CYCLE] = {0};
   uint32_t uiNewAddress = uiLastAddress;
   uint32_t uiEndAddress = uiLastAddress + FRAM_MAX_BYTES_TO_WRITE_PER_CYCLE;
   eFRAM_Error = FRAM_ERROR__CLEAR_ALL;
   if( ( uiNewAddress > FRAM_START_ADDRESS__ALARM_LOG )
    || ( uiEndAddress > FRAM_START_ADDRESS__ALARM_LOG ) )
   {
      FRAM_WriteTriplet(FRAM_START_ADDRESS__STARTING_TAG, (char *)psCheckTag, FRAM_STARTING_TAG_SIZE__BYTES);
      uint8_t aucTag[FRAM_STARTING_TAG_SIZE__BYTES];
      FRAM_ReadTriplet(FRAM_START_ADDRESS__STARTING_TAG, aucTag, FRAM_STARTING_TAG_SIZE__BYTES);
      if( memcmp(aucTag, psCheckTag, FRAM_STARTING_TAG_SIZE__BYTES) == 0 ) /* If tag found, initialize */
      {
         eFRAM_State = FRAM_STATE__STARTUP_READ;
      }
      else
      {
         uiLastAddress = 0;
      }
   }
   else if( FRAM_WriteData(uiNewAddress, aucClearBuffer, FRAM_MAX_BYTES_TO_WRITE_PER_CYCLE) == PASS )
   {
      /* Reuse this variable as a marker of the next starting address that needs to be written */
      uiLastAddress += FRAM_MAX_BYTES_TO_WRITE_PER_CYCLE;
   }
#if ENABLE_FRAM_DEBUG
   // 31 usec per 64B
   int32_t iDiffTime_1us = Timer_GetCount_1us() - iStartTime_1us;
   printf("FRAM CLR: %dus\n", (int)iDiffTime_1us);
#endif
}
/* @fn static void FRAM_State_StartupRead(void)
 * @brief Read out initial values on the FRAM chip
 * @param None
 * @return None
 */
static void FRAM_State_StartupRead(void)
{
   //FRAM_STATE__STARTUP_READ
#if ENABLE_FRAM_DEBUG
   int32_t iStartTime_1us = Timer_GetCount_1us();
#endif
   if( System_GetNodeID() == SYS_NODE__CT_A7 )
   {
      FRAM_ReadTriplet(FRAM_START_ADDRESS__POSITION_SPEED, &stPositionSpeed, FRAM_POSITION_SPEED_SIZE__BYTES);
   }

   FRAM_ReadTriplet(FRAM_START_ADDRESS__BACKUP_TIME, FRAM_GetBackupTimestamp(), FRAM_BACKUP_TIME_SIZE__BYTES);
   FRAM_ReadTriplet(FRAM_START_ADDRESS__DEFAULT_TIME, FRAM_GetDefaultTimestamp(), FRAM_DEFAULT_TIME_SIZE__BYTES);

   uint32_t uiDirectionChangeCount;
   FRAM_ReadTriplet(FRAM_START_ADDRESS__DIRECTION_CHANGE_COUNT, &uiDirectionChangeCount, FRAM_DIRECTION_CHANGE_COUNT_SIZE__BYTES);
   FRAM_SetDirectionChangeCount(uiDirectionChangeCount);

   uint32_t uiRunCount;
   FRAM_ReadTriplet(FRAM_START_ADDRESS__RUN_COUNT, &uiRunCount, FRAM_RUN_COUNT_SIZE__BYTES);
   FRAM_SetRunCount(uiRunCount);

   if( System_GetNodeID() == SYS_NODE__MR_A7 )
   {
      FRAM_ReadTriplet(FRAM_START_ADDRESS__MEDICAL_OPENING, &uwStoredMedicalOpening, FRAM_MEDICAL_OPENING_SIZE__BYTES);
      FRAM_SetMedicalOpening(uwStoredMedicalOpening);

      FRAM_ReadTriplet(FRAM_START_ADDRESS__EMERGENCY_MODE_BITMAP, &uiStoredEmergencyModeBitmap, FRAM_EMERGENCY_MODE_BITMAP_SIZE__BYTES);
      FRAM_SetEmergencyModeBitMap(uiStoredEmergencyModeBitmap);

      FRAM_ReadTriplet(FRAM_START_ADDRESS__LATCHING_FAULT_BITMAP, &uwStoredLatchingFaultBitmap, FRAM_LATCHING_FAULT_BITMAP_SIZE__BYTES);
      FRAM_SetLatchingFaultBitMap(uwStoredLatchingFaultBitmap);

      FRAM_ReadTriplet(FRAM_START_ADDRESS__FIRE_PHASE_1_TRIGGER, &eFirePhase1Trigger_Stored, FRAM_FIRE_PHASE_1_TRIGGER_SIZE__BYTES);
      eFirePhase1Trigger_Edited = eFirePhase1Trigger_Stored;
   }

   eFRAM_State = FRAM_STATE__RUNNING;
#if ENABLE_FRAM_DEBUG
   int32_t iDiffTime_1us = Timer_GetCount_1us() - iStartTime_1us;
   printf("FRAM ST: %dus\n", (int)iDiffTime_1us);
   printf("FRAM USED: %dB\n", (int)FRAM_SPACE_IN_USE__BYTES);
   // Measure ~4391 usec at 60mhz/4 = 15mhz
   // Measure ~3666 usec at 60mhz/2 = 30mhz
#endif
}
/* @fn static void FRAM_State_Running(void)
 * @brief Regular run behavior of the routine. Update FRAM data as needed.
 * @param None
 * @return None
 */
static void FRAM_State_Running(void)
{
   //FRAM_STATE__RUNNING
   if( System_GetNodeID() == SYS_NODE__CT_A7 ) FRAM_UpdatePositionSpeed();

   FRAM_UpdateDirectionChangeCount();
   FRAM_UpdateRunCount();
   /* Certain FRAM sections will only be recorded on the MR_A7, others on both MR_A7 and CT_A7 for ease of UI access*/
   if( System_GetNodeID() == SYS_NODE__MR_A7 )
   {
      FRAM_UpdateMedicalOpening();
      FRAM_UpdateEmergencyModeBitmap();
      FRAM_UpdateLatchingFaultBitmap();
      FRAM_UpdateFirePhase1Trigger();
   }
}
/* @fn static void FRAM_CheckForError(void)
 * @brief Sets system fault/alarm if a write/read error is detected
 * @param None
 * @return None
 */
static void FRAM_CheckForError(void)
{
   if( !Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) )
   {
      if( eFRAM_Error == FRAM_ERROR__CLEAR_ALL )
      {
         en_car_fault eFault = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CFLT__RAM_FORMATTING_MR : CFLT__RAM_FORMATTING_CT;
         Faults_SetFault(eFault);
      }
      else if( eFRAM_Error == FRAM_ERROR__DATA_CORRUPTION )
      {
         en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CALM__RAM_CORRUPTION_MR : CALM__RAM_CORRUPTION_CT;
         Alarms_SetAlarm(eAlarm);
      }
      else if( eFRAM_Error == FRAM_ERROR__DATA_LOSS )
      {
         en_car_fault eFault = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CFLT__RAM_DATA_LOSS_MR : CFLT__RAM_DATA_LOSS_CT;
         Faults_SetFault(eFault);
      }
      else if( eFRAM_Error == FRAM_ERROR__RW_TIMEOUT )
      {
         en_car_fault eFault = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CFLT__RAM_TIMEOUT_MR : CFLT__RAM_TIMEOUT_CT;
         Faults_SetFault(eFault);
      }
      eFRAM_Error = FRAM_ERROR__NONE;
   }
}
/* @fn en_pass_fail FRAM_WriteTriplet(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes)
 * @brief Writes 3 copies of the given data array to a continuous FRAM address range starting with uiAddress
 * @param uiAddress, starting byte address on the FRAM chip to begin writing at
 * @param pTxData, pointer to array of bytes to write to the FRAM
 * @param ucNumTxBytes, number of data bytes to write to the FRAM
 * @return eError, returns 1 if the request has failed
 */
en_pass_fail FRAM_WriteTriplet(uint32_t uiAddress, void *pTxData, uint8_t ucNumTxBytes)
{
   uint8_t aucTempBuffer[FRAM_TX_BUFFER_SIZE__BYTES];
   uint8_t ucStartingIndex = 0;
   memcpy(&aucTempBuffer[ucStartingIndex], pTxData, ucNumTxBytes);
   ucStartingIndex += ucNumTxBytes;
   memcpy(&aucTempBuffer[ucStartingIndex], pTxData, ucNumTxBytes);
   ucStartingIndex += ucNumTxBytes;
   memcpy(&aucTempBuffer[ucStartingIndex], pTxData, ucNumTxBytes);
   uiLastAddress = uiAddress;
   en_pass_fail eError = FRAM_WriteData(uiAddress, aucTempBuffer, ucNumTxBytes*FRAM_NUM_REDUNDANT_DATA_COPIES);
   if( ( eFRAM_Error == FRAM_ERROR__NONE ) && ( eError == FAIL ) ) eFRAM_Error = FRAM_ERROR__RW_TIMEOUT;
   return eError;
}
/* @fn en_pass_fail FRAM_ReadTriplet(uint32_t uiAddress, void *pRxData, uint8_t ucNumRxBytes)
 * @brief Reads 3 copies of the given data array from a continuous FRAM address range starting with uiAddress.
 *        Checks for and corrects inconsistencies in the 3 copies
 * @param uiAddress, starting byte address on the FRAM chip to begin reading from
 * @param pRxData, pointer to receiving array
 * @param ucNumRxBytes, size of receiving array in bytes
 * @return eError, returns 1 if the request has failed
 */
en_pass_fail FRAM_ReadTriplet(uint32_t uiAddress, void *pRxData, uint8_t ucNumRxBytes)
{
   uint8_t aucTempBuffer[FRAM_RX_BUFFER_SIZE__BYTES];
   en_pass_fail eError = FRAM_ReadData(uiAddress, aucTempBuffer, ucNumRxBytes*FRAM_NUM_REDUNDANT_DATA_COPIES);
   if( ( eFRAM_Error == FRAM_ERROR__NONE ) && ( eError == FAIL ) ) eFRAM_Error = FRAM_ERROR__RW_TIMEOUT;
   /* Check the duplicates. If two are matching */
   uint8_t bDiff_C1_C2 = (uint8_t) memcmp(&aucTempBuffer[0], &aucTempBuffer[ucNumRxBytes], ucNumRxBytes);
   uint8_t bDiff_C1_C3 = (uint8_t) memcmp(&aucTempBuffer[0], &aucTempBuffer[2*ucNumRxBytes], ucNumRxBytes);
   uint8_t bDiff_C2_C3 = (uint8_t) memcmp(&aucTempBuffer[ucNumRxBytes], &aucTempBuffer[2*ucNumRxBytes], ucNumRxBytes);
   /* If all 3 copies match, read was successful, update pointer and return */
   if( ( bDiff_C1_C2 == 0 )
    && ( bDiff_C1_C3 == 0 )
    && ( bDiff_C2_C3 == 0 ) )
   {
      memcpy(pRxData, &aucTempBuffer[0], ucNumRxBytes);
   }
   /* If only first and second match, read was successful but corruption detected.
    * Recover overwriting the mismatched copy. */
   else if( bDiff_C1_C2 == 0 )
   {
      eError |= FRAM_WriteData(uiAddress+(2*ucNumRxBytes), &aucTempBuffer[0], ucNumRxBytes); // Overwrite the corrupted third copy
      memcpy(pRxData, &aucTempBuffer[0], ucNumRxBytes);
      if( eFRAM_Error == FRAM_ERROR__NONE ) eFRAM_Error = FRAM_ERROR__DATA_CORRUPTION;
   }
   /* If only second and third match, read was successful but corruption detected.
    * Recover overwriting the mismatched copy. */
   else if( bDiff_C2_C3 == 0 )
   {
      eError |= FRAM_WriteData(uiAddress, &aucTempBuffer[ucNumRxBytes], ucNumRxBytes); // Overwrite the corrupted first copy
      memcpy(pRxData, &aucTempBuffer[ucNumRxBytes], ucNumRxBytes);
      if( eFRAM_Error == FRAM_ERROR__NONE ) eFRAM_Error = FRAM_ERROR__DATA_CORRUPTION;
   }
   /* If only the first and third match, read was successful but corruption detected.
    * Recover overwriting the mismatched copy. */
   else if( bDiff_C1_C3 == 0 )
   {
      eError |= FRAM_WriteData(uiAddress+(ucNumRxBytes), &aucTempBuffer[0], ucNumRxBytes); // Overwrite the corrupted second copy
      memcpy(pRxData, &aucTempBuffer[0], ucNumRxBytes);
      if( eFRAM_Error == FRAM_ERROR__NONE ) eFRAM_Error = FRAM_ERROR__DATA_CORRUPTION;
   }
   /* No copies match, flag an error */
   else
   {
      eFRAM_Error = FRAM_ERROR__DATA_LOSS;
      uiDataLossAddress = uiAddress;
      eError = FAIL;
   }

   uiLastAddress = uiAddress;
   return eError;
}

/* @fn void vFRAM_Task(void *pvParameters)
 * @brief Run function vFRAM_Task that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
void vFRAM_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
	   switch(eFRAM_State)
	   {
		  case FRAM_STATE__CHECK_TAG: FRAM_State_CheckTag(); break; /* Check for marker that this FRAM has been defaulted already. */
		  case FRAM_STATE__DEFAULTING: FRAM_State_Defaulting(); break; /* Zero out chip and mark it as having been defaulted */
		  case FRAM_STATE__STARTUP_READ: FRAM_State_StartupRead(); break; /* Read out startup values */
		  case FRAM_STATE__RUNNING: FRAM_State_Running(); break; /* Check for runtime requests to read/write FRAM */
		  default: eFRAM_State = FRAM_STATE__CHECK_TAG; break;
	   }
	   FRAM_CheckForError();
		vTaskDelay(FRAM_TASK_DELAY);
	}
}
