/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN1.h
 * @version 		  : 1.0.0
 * @brief Subroutine handling CAN1 network
 * @details Subroutine handling CAN1 network
 *****************************************************************************/
#ifndef _SR_CAN1_
#define _SR_CAN1_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>
#include "system.h"
#include "network.h"
/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/

/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg);
uint8_t CAN1_CheckIfCommLoss(void);
void vCAN1_Task(void *pvParameters);
#endif /* _SR_CAN1_ */
