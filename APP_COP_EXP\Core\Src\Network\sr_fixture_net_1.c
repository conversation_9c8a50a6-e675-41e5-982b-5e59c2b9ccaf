/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file sr_fixture_net_1.c
* @version 1.0.0
* @brief This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
* @details This subroutine executes load functions at the scheduled interval and transmits datagrams at the scheduled interval
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "sr_fixture_net_1.h"
#include "motion_def.h"
#include "fixture_def.h"
#include "car_input_def.h"
#include "car_output_def.h"
#include "param_def.h"
#include "hall_call_def.h"
#include "app_can.h"
#include "floor_def.h"
#include "operation_def.h"
#include "dg_car_ct_a7.h"
#include "dg_car_mr_a7.h"
#include "main.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/

#define MAD_RUN_INTERVAL_1MS							  		  (100)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef struct
{
	uint16_t uwRunDelay_1ms;
	en_fixture_net_1_state eState;
} st_fixture_net_1_control;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/
uint16_t gFixtureNet1_Task_Delay = 0;
/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_fixture_net_1_control stControl;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @brief returns the state of the fixture_net subroutine */
en_fixture_net_1_state Fixture_Net_1_GetState(void)
{
	return stControl.eState;
}

/* @fn void FixtureNet1_Task_Init(void)
 * @brief Initializes the FixtureNet1 task by setting the task delay based on the local board type.
 *        If the local board is of type BOARD_COP, it sets the delay to the appropriate periodic delay for FixtureNet1 tasks.
 *        For other board types, it sets the task delay to inactive.
 * @param None
 * @return None
 */
void FixtureNet1_Task_Init(void)
{
	switch(Main_GetLocalBoardType())
	{
		case BOARD_COP:
			gFixtureNet1_Task_Delay = FIXTURE_NET1_TASK_PERIODIC_DELAY_MS;
			break;
		default:
			gFixtureNet1_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;
			break;
	}
}

/* @fn void vFixtureNet1_Task(void *pvParams)
 * @brief Task that handles the communication and state management for FixtureNet1.
 *        It periodically updates passing chimes, lantern data, and message numbers,
 *        while handling different fixture network states (e.g., initialization, running).
 *        The task also initializes CAN2 communication if required and sends messages based
 *        on the state of the system.
 * @param pvParams - Task parameters (not used in this function).
 * @return None
 */
void vFixtureNet1_Task(void *pvParams)
{
    // KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
  /*
    * do
    * {
    *  vTaskDelay(10);
    * }while(Param_GetState() == PARAM_STATE__STARTUP);
    */

    while(1)
    {
        // If not in startup state, update fixture-related tasks
        if( Param_GetState() != PARAM_STATE__STARTUP )
        {
            // Update passing chime, lantern data, and CE_MC message number
            Fixture_UpdatePassingChime(gFixtureNet1_Task_Delay);
            Fixture_UpdateLanternData();
            Fixture_Update_CE_MC_Message_Number();

            // Handle different fixture network states
            switch(stControl.eState)
            {
                case FIXTURE_NET_1_STATE__INIT:
                    // Initialize CAN2 communication for MAD if the parameter matches
                    if( Param_ReadValue_8Bit(CPARAM8__Fixture_COP_Aux_CAN2) == CAN_AUX__MAD )
                    {
                        CAN2_Init_FIXTURE_125K();
                        stControl.eState = FIXTURE_NET_1_STATE__RUNNING_MAD;
                    }
                    break;
                case FIXTURE_NET_1_STATE__RUNNING_MAD:
                    // If the running delay exceeds the interval, build and send a CAN message
                    if(stControl.uwRunDelay_1ms >= MAD_RUN_INTERVAL_1MS)
                    {
                        st_CAN_msg_8B stTxMsg;
                        stTxMsg.uiID = 0x594;
                        stTxMsg.ucDLC = 8;
                        stTxMsg.bExtendedID = 0;
                        stTxMsg.bCAN_FD = 0;
                        // Build and send the CAN message for MAD
                        Fixture_Build_Message_MAD_CAN(&stTxMsg);
                        CAN2_LoadToRB_FIXTURE_125K(&stTxMsg);
                        stControl.uwRunDelay_1ms = 0;
                    }
                    else
                    {
                        // Increment the running delay timer
                        stControl.uwRunDelay_1ms += gFixtureNet1_Task_Delay;
                    }
                    break;
                default:
                    break;
            }
        }
        vTaskDelay(gFixtureNet1_Task_Delay);
    }
}

