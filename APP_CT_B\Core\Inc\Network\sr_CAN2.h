/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_CAN2.h
* @version 		  : 1.0.0
* @brief Subroutine handling CAN2 network
* @details Subroutine handling CAN2 network
*****************************************************************************/
#ifndef _SR_CAN2_H_
#define _SR_CAN2_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "app.h"
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/
typedef enum
{														//All CANopen defaults
	DINACELL_LWD_TOTAL_WEIGHT = 0x181,					//TX_PDO1
	DINACELL_LWD_SINGLE_SENSOR_WEIGHT = 0x281,			//TX_PDO2
	DINACELL_LWD_REQUEST_SINGLE_SENSOR_WEIGHT = 0x301,	//RX_PDO2
	DINACELL_LWD_READ_PARAMETER = 0x381,				//TX_PDO3
	DINACELL_LWD_WRITE_PARAMETER = 0x401,				//RX_PDO3
	DINACELL_LWD_HEARTBEAT = 0x701
} en_LWD_CAN_message;

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/

uint8_t CAN2_CheckIfCommLoss(void);

void vCAN2_Task(void *pvParameters);
#endif /* _SR_CAN2_H_ */
