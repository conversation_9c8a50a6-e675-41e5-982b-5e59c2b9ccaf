/*******************************************************************************
* @Copyright (C) 2022 by Vantage Elevation
* @file           : app_gpio.h
* @version 		  : 1.0.0
* @brief          : GPIO accessing and initialization
* @details		  : GPIO accessing and initialization
********************************************************************************/
#ifndef _APP_GPIO_H_
#define _APP_GPIO_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
#include "app.h"
#include "stm32g4xx_hal.h"
/*******************************************************************************
* Preprocessor Constants
********************************************************************************/
/* When DIP switches are all ON at startup, device will startup with watchdog disabled */


/*******************************************************************************
* Configuration Constants
********************************************************************************/
/* Note: falling edge interrupt setting is actually rising edge due to input inversion */
//				(NAME,					PORT,	PIN,			MODE,			PULL,		SPEED, 			INVERT)


//				(NAME,					PORT,	PIN,			MODE,			PULL,			SPEED, 			INVERT)
#define LOCAL_OUTPUT_TABLE \
	LOCAL_GP_OUTPUT(PIO_01		, GPIOA, GPIO_PIN_0 , GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(PIO_02		, GPIOA, GPIO_PIN_3 , GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(PIO_03		, GPIOA, GPIO_PIN_6 , GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(PIO_04		, GPIOC, GPIO_PIN_5 , GPIO_MODE_OUTPUT_PP, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_HB		, GPIOC, GPIO_PIN_8 , GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_FLT		, GPIOC, GPIO_PIN_9 , GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) \
	LOCAL_GP_OUTPUT(LED_ALM		, GPIOA, GPIO_PIN_10 , GPIO_MODE_OUTPUT_OD, GPIO_NOPULL, GPIO_SPEED_FREQ_LOW, 1) 
/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/
typedef struct
{
	GPIO_TypeDef *pGPIO_Port;
	uint32_t uiMode;
	uint16_t uwPin;
	uint8_t ucPull;
	uint8_t ucSpeed;
	uint8_t bInvert;
} st_gpio_def;



typedef enum
{
#define LOCAL_GP_OUTPUT(NAME, PORT, PIN, MODE, PULL, SPEED, INVERT) enLOCAL_OUT__##NAME,
	LOCAL_OUTPUT_TABLE
#undef LOCAL_GP_OUTPUT
	NUM_LOCAL_OUTPUTS
} en_local_outputs;

/*******************************************************************************
* Global Variables
********************************************************************************/


/*******************************************************************************
* Function Prototypes
********************************************************************************/
void GPIO_Init(void);
//uint8_t GPIO_ReadInput(en_local_inputs eInput);
uint8_t GPIO_ReadOutput(en_local_outputs eOutput);
uint8_t GPIO_ReadOutputCommand(en_local_outputs eOutput);
uint8_t *GPIO_GetOutputCommandPointer(void);
void GPIO_WriteOutput(en_local_outputs eOutput, uint8_t bActive);
//uint8_t GPIO_ReadDIPBank(void);


#endif /* _APP_GPIO_H_ */
