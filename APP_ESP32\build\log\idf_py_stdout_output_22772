ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 22 2025 18:47:07[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18168h ( 98664) map[0m
[0;32mI (125) esp_image: segment 1: paddr=00028190 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002cc1c vaddr=40374000 size=033fch ( 13308) load[0m
[0;32mI (135) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=6087ch (395388) map[0m
[0;32mI (229) esp_image: segment 4: paddr=000908a4 vaddr=403773fc size=144e0h ( 83168) load[0m
[0;32mI (252) esp_image: segment 5: paddr=000a4d8c vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (262) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (262) boot: Disabling RNG early entropy source...[0m
[0;32mI (272) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (272) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (273) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (275) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (279) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (283) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (288) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (293) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (297) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (302) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (307) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (312) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (316) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (320) esp_psram: Speed: 80MHz[0m
[0;32mI (323) cpu_start: Multicore app[0m
[0;32mI (1177) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1194) cpu_start: Pro cpu start user code[0m
[0;32mI (1194) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1194) app_init: Application information:[0m
[0;32mI (1194) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_0_0[0m
[0;32mI (1200) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1206) app_init: Compile time:     Jul 22 2025 18:45:02[0m
[0;32mI (1211) app_init: ELF file SHA256:  47227b329...[0m
[0;32mI (1216) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1220) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1224) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1232) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1238) heap_init: At 3FCAA4C0 len 0003F250 (252 KiB): RAM[0m
[0;32mI (1243) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1248) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1254) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1259) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1266) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1276) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1280) spi_flash: flash io: opi_str[0m
[0;32mI (1283) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1289) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1296) coexist: coex firmware version: e727207[0m
[0;32mI (1309) coexist: coexist rom version e7ae62f[0m
[0;32mI (1310) main_task: Started on CPU0[0m
[0;32mI (1320) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1320) main_task: Calling app_main()[0m
[0;32mI (1340) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1340) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1340) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1370) uart: queue free spaces: 80[0m
[0;32mI (1370) FLASH: : 0[0m
[0;32mI (1370) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1370) HEAP: Free heap: 16688180[0m
[0;32mI (1380) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1380) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1390) NimBLE: Device Address: [0m
[0;32mI (1390) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1390) NimBLE: [0m

[0;32mI (1380) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: false[0m
[0;32mI (1400) main_task: Returned from app_main()[0m
[0;32mI (6900) UART: uart[0] event[0m
[0;32mI (6900) UART_READ: : 0[0m
[0;32mI (6900) UART_READ: : UART_READ_START[0m
[0;32mI (6910) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: true[0m
[0;32mI (6910) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (6910) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (6910) UART: BLE advertising started successfully[0m
[0;32mI (6920) UART READ: [LEN: ]: 10[0m
[0;32mI (275020) UART: uart[0] event[0m
[0;32mI (275020) UART_READ: : 0[0m
[0;32mI (275020) UART_READ: : UART_READ_START[0m
[0;32mI (275020) UART: Received BLE_STOP_ADV command[0m
[0;32mI (275020) NimBLE_BLE_PRPH: BLE advertising is not active.[0m
[0;32mI (275030) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (275030) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (275030) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (275040) UART: BLE advertising stopped successfully[0m
[0;32mI (275040) UART READ: [LEN: ]: 20[0m
[0;32mI (345120) UART: uart[0] event[0m
[0;32mI (345120) UART_READ: : 0[0m
[0;32mI (345120) UART_READ: : UART_READ_START[0m
[0;32mI (345130) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: true[0m
[0;32mI (345130) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (345130) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (345130) UART: BLE advertising started successfully[0m
[0;32mI (345130) UART READ: [LEN: ]: 30[0m
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
Octal Flash Mode Enabled
For OPI Flash, Use Default Flash Boot Mode
mode:SLOW_RD, clock div:1
load:0x3fce2810,len:0x15a0
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0;32mI (33) boot: ESP-IDF v5.4.1 2nd stage bootloader[0m
[0;32mI (33) boot: compile time Jul 22 2025 18:47:07[0m
[0;32mI (33) boot: Multicore bootloader[0m
[0;32mI (34) boot: chip revision: v0.2[0m
[0;32mI (36) boot: efuse block revision: v1.3[0m
[0;32mI (40) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0;32mI (44) boot.esp32s3: SPI Mode       : SLOW READ[0m
[0;32mI (48) boot.esp32s3: SPI Flash Size : 32MB[0m
[0;32mI (52) boot: Enabling RNG early entropy source...[0m
[0;32mI (56) boot: Partition Table:[0m
[0;32mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (65) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (72) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (78) boot:  2 factory          factory app      00 00 00010000 00100000[0m
[0;32mI (85) boot:  3 storage          Unknown data     01 82 00110000 00a00000[0m
[0;32mI (91) boot: End of partition table[0m
[0;32mI (95) esp_image: segment 0: paddr=00010020 vaddr=3c070020 size=18168h ( 98664) map[0m
[0;32mI (125) esp_image: segment 1: paddr=00028190 vaddr=3fc9b900 size=04a84h ( 19076) load[0m
[0;32mI (131) esp_image: segment 2: paddr=0002cc1c vaddr=40374000 size=033fch ( 13308) load[0m
[0;32mI (135) esp_image: segment 3: paddr=00030020 vaddr=42000020 size=6087ch (395388) map[0m
[0;32mI (229) esp_image: segment 4: paddr=000908a4 vaddr=403773fc size=144e0h ( 83168) load[0m
[0;32mI (252) esp_image: segment 5: paddr=000a4d8c vaddr=600fe100 size=0001ch (    28) load[0m
[0;32mI (262) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (262) boot: Disabling RNG early entropy source...[0m
[0;32mI (272) octal_psram: vendor id    : 0x0d (AP)[0m
[0;32mI (272) octal_psram: dev id       : 0x03 (generation 4)[0m
[0;32mI (273) octal_psram: density      : 0x05 (128 Mbit)[0m
[0;32mI (275) octal_psram: good-die     : 0x01 (Pass)[0m
[0;32mI (279) octal_psram: Latency      : 0x01 (Fixed)[0m
[0;32mI (283) octal_psram: VCC          : 0x00 (1.8V)[0m
[0;32mI (288) octal_psram: SRF          : 0x01 (Fast Refresh)[0m
[0;32mI (293) octal_psram: BurstType    : 0x01 (Hybrid Wrap)[0m
[0;32mI (297) octal_psram: BurstLen     : 0x01 (32 Byte)[0m
[0;32mI (302) octal_psram: Readlatency  : 0x02 (10 cycles@Fixed)[0m
[0;32mI (307) octal_psram: DriveStrength: 0x00 (1/1)[0m
[0;32mI (312) MSPI Timing: PSRAM timing tuning index: 6[0m
[0;32mI (316) esp_psram: Found 16MB PSRAM device[0m
[0;32mI (320) esp_psram: Speed: 80MHz[0m
[0;32mI (323) cpu_start: Multicore app[0m
[0;32mI (1177) esp_psram: SPI SRAM memory test OK[0m
[0;32mI (1194) cpu_start: Pro cpu start user code[0m
[0;32mI (1194) cpu_start: cpu freq: 240000000 Hz[0m
[0;32mI (1194) app_init: Application information:[0m
[0;32mI (1194) app_init: Project name:     Vantage_nxESP32_Int_Rel_1_2_0_0[0m
[0;32mI (1200) app_init: App version:      Vantage_Bin_nxESP32_1_2_0_0[0m
[0;32mI (1206) app_init: Compile time:     Jul 22 2025 18:45:02[0m
[0;32mI (1211) app_init: ELF file SHA256:  47227b329...[0m
[0;32mI (1216) app_init: ESP-IDF:          v5.4.1[0m
[0;32mI (1220) efuse_init: Min chip rev:     v0.0[0m
[0;32mI (1224) efuse_init: Max chip rev:     v0.99 [0m
[0;32mI (1228) efuse_init: Chip rev:         v0.2[0m
[0;32mI (1232) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (1238) heap_init: At 3FCAA4C0 len 0003F250 (252 KiB): RAM[0m
[0;32mI (1243) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0;32mI (1248) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0;32mI (1254) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0;32mI (1259) esp_psram: Adding pool of 16121K of PSRAM memory to heap allocator[0m
[0;33mW (1266) spi_flash: Octal flash chip is using but dio mode is selected, will automatically switch to Octal mode[0m
[0;32mI (1276) spi_flash: detected chip: mxic (opi)[0m
[0;32mI (1280) spi_flash: flash io: opi_str[0m
[0;32mI (1283) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0;32mI (1289) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0;32mI (1296) coexist: coex firmware version: e727207[0m
[0;32mI (1309) coexist: coexist rom version e7ae62f[0m
[0;32mI (1310) main_task: Started on CPU0[0m
[0;32mI (1320) esp_psram: Reserving pool of 32K of internal memory for DMA/internal allocations[0m
[0;32mI (1320) main_task: Calling app_main()[0m
[0;32mI (1340) BLE_INIT: BT controller compile version [d74042a][0m
[0;32mI (1340) BLE_INIT: Bluetooth MAC: b4:3a:45:f7:39:0a[0m
[0;32mI (1340) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1380) uart: queue free spaces: 80[0m
[0;32mI (1380) FLASH: : 0[0m
[0;32mI (1380) FLASH: : LittleFS mounted successfully at /storage[0m
[0;32mI (1380) HEAP: Free heap: 16688180[0m
[0;32mI (1380) FILE_HANDLE: : TASK CREATED[0m
[0;32mI (1390) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (1390) NimBLE: Device Address: [0m
[0;32mI (1390) NimBLE: b4:3a:45:f7:39:0a[0m
[0;32mI (1400) NimBLE: [0m

[0;32mI (1390) NimBLE_BLE_PRPH: BLE advertising state loaded from NVS: true[0m
[0;32mI (1410) MAIN: Resuming BLE advertising from NVS...[0m
[0;32mI (1410) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (1420) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (1420) NimBLE: disc_mode=2[0m
[0;32mI (1420) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (1430) NimBLE: [0m

[0;32mI (1430) main_task: Returned from app_main()[0m
BLE CONNECTION ESTABLISHED
[0;32mI (449410) NimBLE: connection established; status=0 [0m
[0;32mI (449410) UART: Sent UART command: 10 bytes[0m
[0;32mI (449410) NimBLE: [0m

[0;32mI (449410) NimBLE: GAP procedure initiated: [0m
[0;32mI (449410) NimBLE: connection parameter update; conn_handle=1 itvl_min=6 itvl_max=8 latency=0 supervision_timeout=400 min_ce_len=0 max_ce_len=0[0m
[0;32mI (449430) NimBLE: [0m

[0;32mI (449510) UART: uart[0] event[0m
[0;32mI (449510) UART_READ: : 0[0m
[0;32mI (449510) UART_READ: : UART_READ_START[0m
[0;32mI (449510) UART: Received BLE_STOP_ADV command[0m
[0;32mI (449520) NimBLE_BLE_PRPH: BLE advertising is not active.[0m
[0;32mI (449530) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (449530) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (449530) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (449540) UART: BLE advertising stopped successfully[0m
[0;32mI (449540) UART READ: [LEN: ]: 10[0m
[0;32mI (449820) NimBLE: connection updated; status=0 [0m
[0;32mI (449820) NimBLE: [0m

[0;32mI (449830) NimBLE: connection updated; status=547 [0m
[0;32mI (449830) NimBLE: [0m

[0;32mI (450060) NimBLE: mtu update event; conn_handle=1 cid=4 mtu=517[0m

[0;32mI (450170) NimBLE: connection updated; status=0 [0m
[0;32mI (450170) NimBLE: [0m

[0;32mI (456250) NimBLE: disconnect; reason=531 [0m
[0;32mI (456250) NimBLE: [0m

[0;32mI (456250) UART: Sent UART command: 10 bytes[0m
[0;32mI (456250) NimBLE_BLE_PRPH: Starting BLE advertising...[0m
[0;32mI (456250) NimBLE: GAP procedure initiated: advertise; [0m
[0;32mI (456260) NimBLE: disc_mode=2[0m
[0;32mI (456260) NimBLE:  adv_channel_map=0 own_addr_type=0 adv_filter_policy=0 adv_itvl_min=0 adv_itvl_max=0[0m
[0;32mI (456270) NimBLE: [0m

[0;32mI (456510) UART: uart[0] event[0m
[0;32mI (456510) UART_READ: : 0[0m
[0;32mI (456510) UART_READ: : UART_READ_START[0m
[0;32mI (456510) UART: Received BLE_STOP_ADV command[0m
[0;32mI (456520) NimBLE_BLE_PRPH: Stopping BLE advertising...[0m
[0;32mI (456520) NimBLE: GAP procedure initiated: stop advertising.[0m

[0;32mI (456530) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (456530) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (456540) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (456540) UART: BLE advertising stopped successfully[0m
[0;32mI (456550) UART READ: [LEN: ]: 20[0m
[0;32mI (464400) UART: uart[0] event[0m
[0;32mI (464400) UART_READ: : 0[0m
[0;32mI (464400) UART_READ: : UART_READ_START[0m
[0;32mI (464400) UART: Received BLE_STOP_ADV command[0m
[0;32mI (464400) NimBLE_BLE_PRPH: BLE advertising is not active.[0m
[0;32mI (464410) NimBLE_BLE_PRPH: BLE advertising state saved to NVS: false[0m
[0;32mI (464410) UART: Attempting to send ACK/NACK: 0x1[0m
[0;32mI (464420) UART: Sent 10 bytes for ACK/NACK[0m
[0;32mI (464420) UART: BLE advertising stopped successfully[0m
[0;32mI (464420) UART READ: [LEN: ]: 30[0m
[0;32mI (469900) UART: uart[0] event[0m
[0;32mI (469900) UART_READ: : 0[0m
[0;32mI (469900) UART