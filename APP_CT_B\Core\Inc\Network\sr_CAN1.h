/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_CAN1.h
* @version 		  : 1.0.0
* @brief Subroutine handling CAN1 network
* @details Subroutine handling CAN1 network
*****************************************************************************/
#ifndef _SR_CAN1_H_
#define _SR_CAN1_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "app.h"
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
uint32_t getPositionRaw(void);
uint32_t getErrorRaw(void);
uint16_t getSpeedRaw(void);

uint8_t CAN1_CheckIfCommLoss(void);
void vCAN1_Task(void *pvParameters);
#endif /* _SR_CAN1_H_ */
