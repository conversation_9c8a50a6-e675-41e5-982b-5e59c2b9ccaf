/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_CAN2.h
* @version 		  : 1.0.0
* @brief          : CAN1 subroutine
* @details		  : CAN1 subroutine
********************************************************************************/
#ifndef _SR_CAN2_H_
#define _SR_CAN2_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include "network.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/
/******************************************************************************
* Function Prototypes
*******************************************************************************/
en_pass_fail CAN2_TransmitDatagram(st_CAN_msg *pstMsg);
en_active_inactive CAN2_CheckDestination(uint32_t uiDestinationBitmap);
uint8_t CAN2_CheckIfCommLoss(void);
en_active_inactive CAN2_HallBoardFuncOnBus(uint8_t ucFunc);
void CAN2_Task_Init(void);
void vCAN2_Task(void *pvParams);
#endif /* _SR_CAN2_H_ */
