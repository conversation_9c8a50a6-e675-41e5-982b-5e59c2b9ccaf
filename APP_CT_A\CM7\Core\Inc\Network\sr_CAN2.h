/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_CAN2.h
* @version 		  : 1.0.0
* @brief Subroutine handling CAN2 network
* @details Subroutine handling CAN2 network
*****************************************************************************/
#ifndef _SR_CAN2_H_
#define _SR_CAN2_H_

/******************************************************************************
* Includes
*******************************************************************************/
#include <stdint.h>
#include "system.h"
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
uint32_t getPositionRaw(void);
uint32_t getErrorRaw(void);
uint16_t getSpeedRaw(void);

uint8_t CAN2_CheckIfCommLoss(void);
void vCAN2_Task(void *pvParameters);
#endif /* _SR_CAN2_H_ */
