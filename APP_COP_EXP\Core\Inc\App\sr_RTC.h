/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : sr_RTC.h
* @version 		  : 1.0.0
* @brief Subroutine updating and accessing real time clock
* @details Subroutine updating and accessing real time clock
*****************************************************************************/
#ifndef _SR_RTC_H_
#define _SR_RTC_H_

/******************************************************************************
* Includes
*******************************************************************************/
/******************************************************************************
* Preprocessor Constants
*******************************************************************************/

/******************************************************************************
* Configuration Constants
*******************************************************************************/


/******************************************************************************
* Macros
*******************************************************************************/


/******************************************************************************
* Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variables
*******************************************************************************/

/******************************************************************************
* Function Prototypes
*******************************************************************************/
void vRTC_Update(void);
void RTC_Init(void);

#endif /* _SR_RTC_H_ */
