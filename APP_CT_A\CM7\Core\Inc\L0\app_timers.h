/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_timers.h
* @version 		  : 1.0.0
* @brief          : Hardware timers initialization and access
* @details		  : Hardware timers initialization and access
********************************************************************************/
#ifndef _APP_TIMERS_H_
#define _APP_TIMERS_H_
/*******************************************************************************
* Includes
********************************************************************************/
#include <stdint.h>
#include "stm32h7xx_hal.h"

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Configuration Constants
********************************************************************************/
#define TIMER_CLOCK_RATE			(240000000)

#define TIMER2_PRESCALE_VALUE		(240) // 1us
#define TIMER2_PERIOD_VALUE			(4294967295) // 1.193 hr

#define TIMER3_PRESCALE_VALUE		(60000) // 0.25MS prescale
#define TIMER3_PERIOD_VALUE			(3) // 1 ms overflow

#define TIMER5_PRESCALE_VALUE		(0) // 1 ms
#define TIMER5_PERIOD_VALUE			(4294967295) // 49.71 days

/*******************************************************************************
* Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variables
********************************************************************************/


/*******************************************************************************
* Function Prototypes
********************************************************************************/
void Timer2_Init(void);
void Timer3_Init(void);
void Timer5_Init(void);

int32_t Timer_GetCount_1ms(void);
int32_t Timer_GetCount_1us(void);

int32_t Timer_CompareCount_1ms(int32_t iCount_1ms);
int32_t Timer_CompareCount_1us(int32_t iCount_1us);

void HAL_TIM_Base_MspInit(TIM_HandleTypeDef* tim_baseHandle);
void HAL_TIM_Base_MspDeInit(TIM_HandleTypeDef* tim_baseHandle);

#endif /* _APP_TIMERS_H_ */
