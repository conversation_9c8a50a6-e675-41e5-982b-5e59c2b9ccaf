/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : app_timers.c
* @version 		  : 1.0.0
* @brief          : Hardware timers initialization and access
* @details		  : Hardware timers initialization and access
********************************************************************************/

/*******************************************************************************
* Includes
********************************************************************************/
#include <string.h>
#include "app_timers.h"
#include "main.h"
/*******************************************************************************
* Function Prototypes
********************************************************************************/

/*******************************************************************************
* Preprocessor Constants
********************************************************************************/


/*******************************************************************************
* Preprocessor Macros
********************************************************************************/


/*******************************************************************************
* Typedefs
********************************************************************************/


/*******************************************************************************
* Global Variable Definitions
********************************************************************************/
TIM_HandleTypeDef htim2;
TIM_HandleTypeDef htim3;
TIM_HandleTypeDef htim5;

/*******************************************************************************
* Variable Definitions
********************************************************************************/


/*******************************************************************************
* Function Definitions
********************************************************************************/
/**
  * @fn  int32_t Timer_CompareCount_1us(int32_t iCount_1us)
  * @brief Returns a negative value in 1us counts if the argument time has passed. Returns a positive value in 1us counts if the time is in the future
  * @param None
  * @return iTimeDiff_1us, Returns a negative value in 1us counts if the argument time has passed. Returns a positive value in 1us counts if the time is in the future
  */
int32_t Timer_CompareCount_1us(int32_t iCount_1us)
{
	int32_t iTimeDiff_1us = iCount_1us - Timer_GetCount_1us();
	return iTimeDiff_1us;
}
/**
  * @fn  int32_t Timer_CompareCount_1ms(int32_t iCount_1ms)
  * @brief Returns a negative value in 1ms counts if the argument time has passed. Returns a positive value in 1ms counts if the time is in the future
  * @param None
  * @return iTimeDiff_1ms, Returns a negative value in 1ms counts if the argument time has passed. Returns a positive value in 1ms counts if the time is in the future
  */
int32_t Timer_CompareCount_1ms(int32_t iCount_1ms)
{
	int32_t iTimeDiff_1ms = iCount_1ms - Timer_GetCount_1ms();
	return iTimeDiff_1ms;
}
/**
  * @fn  int32_t Timer_GetCount_1us(void)
  * @brief Get the current timer count in microseconds
  * @param None
  * @return iCurrentCount, the current timer count in microseconds returned as a signed 32-bit integer
  */
int32_t Timer_GetCount_1us(void)
{
	int32_t iCurrentCount = TIM2->CNT;
	return iCurrentCount;
}
/**
  * @fn  int32_t Timer_GetCount_1ms(void)
  * @brief Get the current timer count in milliseconds
  * @param None
  * @return iCurrentCount, the current timer count in milliseconds returned as a signed 32-bit integer
  */
int32_t Timer_GetCount_1ms(void)
{
	int32_t iCurrentCount = TIM5->CNT;
	return iCurrentCount;
}
/**
  * @fn  void Timer2_Init(void)
  * @brief Initializes 32-bit hardware timer 2 for 1 usec tracking
  * @param None
  * @return None
  */
void Timer2_Init(void)
{
	TIM_ClockConfigTypeDef sClockSourceConfig = {0};
	TIM_MasterConfigTypeDef sMasterConfig = {0};

	htim2.Instance = TIM2;
	htim2.Init.Prescaler = TIMER2_PRESCALE_VALUE;
	htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
	htim2.Init.Period = TIMER2_PERIOD_VALUE;
	htim2.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
	htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
	if(HAL_TIM_Base_Init(&htim2) != HAL_OK)
	{
		Error_Handler();
	}
	sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
	if(HAL_TIM_ConfigClockSource(&htim2, &sClockSourceConfig) != HAL_OK)
	{
		Error_Handler();
	}
	sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
	sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
	if(HAL_TIMEx_MasterConfigSynchronization(&htim2, &sMasterConfig) != HAL_OK)
	{
		Error_Handler();
	}

	/* Start operation of Timer 2 */
	HAL_TIM_Base_Start(&htim2);
}
/**
  * @fn  void Timer3_Init(void)
  * @brief Initializes 16-bit hardware timer 3 as a prescale timer that will clock timer 5 upon overflow
  * @param None
  * @return None
  */
void Timer3_Init(void)
{
	/* Initialize timer 3 in master mode */
	TIM_ClockConfigTypeDef sClockSourceConfig = {0};
	TIM_MasterConfigTypeDef sMasterConfig = {0};

	htim3.Instance = TIM3;
	htim3.Init.Prescaler = TIMER3_PRESCALE_VALUE;
	htim3.Init.CounterMode = TIM_COUNTERMODE_UP;
	htim3.Init.Period = TIMER3_PERIOD_VALUE;
	htim3.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
	htim3.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
	if(HAL_TIM_Base_Init(&htim3) != HAL_OK)
	{
		Error_Handler();
	}
	sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
	if(HAL_TIM_ConfigClockSource(&htim3, &sClockSourceConfig) != HAL_OK)
	{
		Error_Handler();
	}
	sMasterConfig.MasterOutputTrigger = TIM_TRGO_UPDATE;
	sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_ENABLE;
	if(HAL_TIMEx_MasterConfigSynchronization(&htim3, &sMasterConfig) != HAL_OK)
	{
		Error_Handler();
	}

	/* Start operation of Timer 3 */
	HAL_TIM_Base_Start(&htim3);
}
/**
  * @fn  void Timer5_Init(void)
  * @brief Initializes 32-bit hardwar timer 5 to be clocked by the overflow of prescale timer 3 that occures every 1ms
  * @param None
  * @return None
  */
void Timer5_Init(void)
{
	/* Initialize timer 5 in minion mode */
	TIM_MasterConfigTypeDef sMasterConfig = {0};
	TIM_SlaveConfigTypeDef sSlaveConfig = {0};

	htim5.Instance = TIM5;
	htim5.Init.Prescaler = TIMER5_PRESCALE_VALUE;
	htim5.Init.CounterMode = TIM_COUNTERMODE_UP;
	htim5.Init.Period = TIMER5_PERIOD_VALUE;
	htim5.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
	htim5.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
	if(HAL_TIM_Base_Init(&htim5) != HAL_OK)
	{
		Error_Handler();
	}
	sSlaveConfig.SlaveMode = TIM_SLAVEMODE_EXTERNAL1;
	sSlaveConfig.InputTrigger = TIM_TS_ITR2;
	sSlaveConfig.TriggerPolarity = TIM_TRIGGERPOLARITY_RISING;
	if(HAL_TIM_SlaveConfigSynchro(&htim5, &sSlaveConfig) != HAL_OK)
	{
		Error_Handler();
	}
	sMasterConfig.MasterOutputTrigger = TIM_TRGO_UPDATE;
	sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_ENABLE;
	if(HAL_TIMEx_MasterConfigSynchronization(&htim5, &sMasterConfig) != HAL_OK)
	{
		Error_Handler();
	}

	/* Start operation of Timer 5/3 */
	HAL_TIM_Base_Start(&htim5);
}
/**
  * @fn void HAL_TIM_Base_MspInit(TIM_HandleTypeDef* tim_baseHandle)
  * @brief Initializes hardware timer clocks
  * @param tim_baseHandle pointer to timer configuration structure
  * @return None
  */
void HAL_TIM_Base_MspInit(TIM_HandleTypeDef* tim_baseHandle)
{
	if(tim_baseHandle->Instance==TIM2)
	{
	/* USER CODE BEGIN TIM2_MspInit 0 */

	/* USER CODE END TIM2_MspInit 0 */
		/* TIM2 clock enable */
		__HAL_RCC_TIM2_CLK_ENABLE();
	/* USER CODE BEGIN TIM2_MspInit 1 */

	/* USER CODE END TIM2_MspInit 1 */
	}
	else if(tim_baseHandle->Instance==TIM3)
	{
	/* USER CODE BEGIN TIM3_MspInit 0 */

	/* USER CODE END TIM3_MspInit 0 */
		/* TIM3 clock enable */
		__HAL_RCC_TIM3_CLK_ENABLE();
	/* USER CODE BEGIN TIM3_MspInit 1 */

	/* USER CODE END TIM3_MspInit 1 */
	}
	else if(tim_baseHandle->Instance==TIM5)
	{
	/* USER CODE BEGIN TIM5_MspInit 0 */

	/* USER CODE END TIM5_MspInit 0 */
		/* TIM5 clock enable */
		__HAL_RCC_TIM5_CLK_ENABLE();
	/* USER CODE BEGIN TIM5_MspInit 1 */

	/* USER CODE END TIM5_MspInit 1 */
	}
}
/**
  * @fn void HAL_TIM_Base_MspDeInit(TIM_HandleTypeDef* tim_baseHandle)
  * @brief De-Initializes hardware timer clocks
  * @param tim_baseHandle pointer to timer configuration structure
  * @return None
  */
void HAL_TIM_Base_MspDeInit(TIM_HandleTypeDef* tim_baseHandle)
{
	if(tim_baseHandle->Instance==TIM2)
	{
	/* USER CODE BEGIN TIM2_MspDeInit 0 */

	/* USER CODE END TIM2_MspDeInit 0 */
		/* TIM2 clock disable */
		__HAL_RCC_TIM2_CLK_DISABLE();
	/* USER CODE BEGIN TIM2_MspDeInit 1 */

	/* USER CODE END TIM2_MspDeInit 1 */
	}
	else if(tim_baseHandle->Instance==TIM3)
	{
	/* USER CODE BEGIN TIM3_MspDeInit 0 */

	/* USER CODE END TIM3_MspDeInit 0 */
		/* TIM3 clock disable */
		__HAL_RCC_TIM2_CLK_DISABLE();
	/* USER CODE BEGIN TIM3_MspDeInit 1 */

	/* USER CODE END TIM3_MspDeInit 1 */
	}
	else if(tim_baseHandle->Instance==TIM5)
	{
	/* USER CODE BEGIN TIM5_MspDeInit 0 */

	/* USER CODE END TIM5_MspDeInit 0 */
		/* TIM5 clock disable */
		__HAL_RCC_TIM5_CLK_DISABLE();
	/* USER CODE BEGIN TIM5_MspDeInit 1 */

	/* USER CODE END TIM5_MspDeInit 1 */
	}
}
