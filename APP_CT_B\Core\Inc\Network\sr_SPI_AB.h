/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_SPI_AB.h
 * @version 		  : 1.0.0
 * @brief This subroutine loads/unloads data for transmission on SPI which connects the MCUA to MCUB
 * @details This subroutine loads/unloads data for transmission on SPI which connects the MCUA to MCUB
 *****************************************************************************/
#ifndef _SR_SPI_AB_
#define _SR_SPI_AB_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include <stdint.h>
#include "app.h"
#include "system.h"
#include "network.h"
/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/

/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
en_pass_fail SPI_AB_TransmitDatagram(st_CAN_msg *pstMsg);
uint8_t SPI_AB_CheckIfCommLoss(void);

void vSPIAB_Task(void *pvParameters);

#endif /* _SR_SPI_AB_ */
