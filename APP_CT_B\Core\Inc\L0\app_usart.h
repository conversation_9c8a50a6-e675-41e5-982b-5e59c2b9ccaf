/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.h
  * @brief   This file contains all the function prototypes for
  *          the usart.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __USART_H__
#define __USART_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* USER CODE BEGIN Includes */
#define UART1_TX_RING_BUFFER_SIZE                        (128)
#define UART1_RX_RING_BUFFER_SIZE                        (128)
#define UART1_TEMP_RX_BUFFER_SIZE                        (1)
/* CE Microcom */
#define UART1_BAUD_RATE__CE_MC                           (9600)
#define UART1_WORD_LENGTH__CE_MC                         (UART_WORDLENGTH_8B)
#define UART1_STOP_BITS__CE_MC                           (UART_STOPBITS_1)
#define UART1_PARITY__CE_MC                           	 (UART_PARITY_NONE)
#define UART1_MODE__CE_MC                           	 (UART_MODE_TX)
#define UART1_HW_FLOW_CTL__CE_MC                         (UART_HWCONTROL_NONE)
#define UART1_OVERSAMPLING__CE_MC                        (UART_OVERSAMPLING_16)
#define UART1_ONE_BIT_SAMPLING__CE_MC                    (UART_ONE_BIT_SAMPLE_DISABLE)
#define UART1_CLOCK_PRESCALER__CE_MC                     (UART_PRESCALER_DIV1)
#define UART1_ADVANCED_INIT__CE_MC                       (UART_ADVFEATURE_NO_INIT)
/* USER CODE END Includes */

extern UART_HandleTypeDef huart1;

extern UART_HandleTypeDef huart2;

/* Enumberation of different UART status counters */
typedef enum
{
   UART_COUNTER__RX_PACKET,   /* Number of successful packet RX */
   UART_COUNTER__TX_PACKET,   /* Number of successful packet TX */
   UART_COUNTER__RX_OVERFLOW, /* Data loss because RX ring buffer was full */
   UART_COUNTER__TX_OVERFLOW, /* Transmit failed because TX ring buffer was full */
   UART_COUNTER__ERROR_ISR,   /* Number of times UART hardware peripheral error detected */
   UART_COUNTER__CRC,         /* Received packet failed CRC check */
   UART_COUNTER__OVERRUN,     /* Overrun of RX buffer */

   NUM_UART_COUNTERS
} en_uart_counter;

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

/* USER CODE BEGIN Prototypes */
void UART1_Init_CEMicrocomm(void);
ErrorStatus UART1_LoadToTxRB(uint8_t *pucData, uint16_t uwNumBytes);
ErrorStatus UART1_UnloadFromRxRB(uint8_t *pucData);
uint16_t UART1_GetStatusCounter(en_uart_counter eCounter);
void UART1_IncrementStatusCounter(en_uart_counter eCounter);

void MX_USART2_UART_Init(void);
/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __USART_H__ */

