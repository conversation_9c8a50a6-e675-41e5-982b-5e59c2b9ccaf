#include "Learn/learn_nts.h"
#include "nts_lookup.h"
#include "param_def.h"
#include "position_def.h"
#include "sr_position.h"
#include "floor_def.h"
#include "dg_car_mr_a7.h"
#include "LearnDef/learn_usr_evt.h"

#include "ulog.h"

#include <string.h>
#include <stdlib.h>

nts_lut_t *NTS_entry;
nts_xcheck_t NTS_xcheck;

static void Learn_NTS_WriteParams(learn_usr_t type,
    uint32_t relative_dist);
static en_active_inactive Learn_NTS_CheckParamSync(learn_usr_t type,
    uint32_t relative_dist);

learn_status_t Learn_NTS_Init(void)
{
  // Step 1. Select NTS positions from list
  learn_status_t status;
  uint16_t contract_speed = Param_ReadValue_16Bit(CPARAM16__Contract_Speed_fpm);
  for(uint32_t i = 0; i < NTS_TABLE_COLUMN_SIZE; i++)
  {
    nts_lut_t *nts = &NTS_LookupTable[i];
    if(contract_speed >= nts->speed)
    {
      NTS_entry = nts;
    }
    else
    {
      break;
    }
  }

  if(NTS_entry != NULL)
  {
    // Reset velocities
    memset(&NTS_entry->velocities[0], 0, sizeof(int16_t)*NTS_TABLE_ROW_SIZE);
    // Reset NTS record index
    NTS_entry->index    = 0;

    // Reset xcheck
    memset(&NTS_xcheck, 0, sizeof(nts_xcheck_t));

    ULOG_DEBUG("NTS entry");
    ULOG_DEBUG("Speed = %i, Count = %i",
        NTS_entry->speed,
        NTS_entry->count);

    status = LEARN_STATUS__OK;
  }
  else
  {
    status = LEARN_NTS_STATUS__SPEED_NOT_FOUND;
  }

  return status;
}

learn_status_t Learn_NTS_Record(void)
{
  learn_status_t status = LEARN_STATUS__BUSY;
  uint32_t index        = NTS_entry->index;

  if(Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE)
  {
    return status;
  }

  // TODO: pre-compute everything into 05mm distances
  // TODO: Implement a timeout, we could potentially be waiting forever here
  if(index < NTS_entry->count)
  {
    uint32_t NTS_position   = (uint32_t)(NTS_entry->positions[index] * ONE_INCH__05MM);
    uint32_t Dist_To_Target = (uint32_t)abs(Motion_GetDestination_05mm_MRA() -
                                            Position_GetPosition_05mm());
    if(Dist_To_Target <= NTS_position)
    {
        NTS_entry->velocities[index] = Position_GetSpeed_fpm();
        NTS_entry->index             = ++index;

        ULOG_DEBUG("Dist_To_Target = %i, NTS_position = %i, NTS_Speed = %i",
            Dist_To_Target,
            NTS_position,
            NTS_entry->velocities[index-1]);
    }
  }
  else
  {
    status = LEARN_STATUS__OK;
  }

  return status;
}

learn_status_t Learn_NTS_SaveParams(learn_usr_t type)
{
  // Step 3. Save UNTS or DNTS speeds depending to the data value provided
  uint8_t floor;
  uint32_t floor_dist_05mm;
  if(type == LEARN_UNTS_UETS)
  {
    ULOG_DEBUG("Saving UNTS parameters");

    floor           = Floor_GetNumberOfCarFloors() - 1;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);

    Param_WriteValue_8Bit(CPARAM8__Traction_UNTS_Count, NTS_entry->count);
  }
  else if(type == LEARN_DNTS_DETS)
  {
    ULOG_DEBUG("Saving DNTS parameters");

    floor           = 0;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);

    Param_WriteValue_8Bit(CPARAM8__Traction_DNTS_Count, NTS_entry->count);
  }

  // Save default xcheck values if they are zero
  if(Param_ReadValue_8Bit(CPARAM8__Traction_NTS_Pos_XCheck) == 0)
  {
    Param_WriteValue_8Bit(CPARAM8__Traction_NTS_Pos_XCheck,
        DEFAULT_NTS_POS_XCHECK_VALUE);
  }

  if(Param_ReadValue_8Bit(CPARAM8__Traction_NTS_Speed_Xcheck) == 0)
  {
    Param_WriteValue_8Bit(CPARAM8__Traction_NTS_Speed_Xcheck,
        DEFAULT_NTS_SPEED_XCHECK_VALUE);
  }

  // Save NTS points
  Learn_NTS_WriteParams(type, floor_dist_05mm);

  return LEARN_STATUS__OK;
}

learn_status_t Learn_NTS_ParamSync(learn_usr_t type)
{
  uint8_t floor;
  uint32_t floor_dist_05mm;
  if(type == LEARN_UNTS_UETS)
  {
    floor           = Floor_GetNumberOfCarFloors() - 1;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);
  }
  else if(type == LEARN_DNTS_DETS)
  {
    floor           = 0;
    floor_dist_05mm = Floor_GetFloorPosition_05mm(floor);
  }

  return (Learn_NTS_CheckParamSync(type, floor_dist_05mm) == ACTIVE) ?
      LEARN_STATUS__OK : LEARN_STATUS__BUSY;
}

learn_status_t Learn_NTS_SaveXCheck(uint8_t *msg_data)
{
  uint8_t pos_size   = NTS_TABLE_ROW_SIZE * sizeof(uint32_t);
  uint8_t speed_size = NTS_TABLE_ROW_SIZE * sizeof(int16_t);

  NTS_xcheck.count = msg_data[0];
  memcpy(&NTS_xcheck.positions[0],
      &msg_data[1],
      pos_size);     // NTS positions
  memcpy(&NTS_xcheck.velocities[0],
      &msg_data[1 + pos_size],
      speed_size);   // NTS velocities

  return LEARN_STATUS__OK;
}


learn_status_t Learn_NTS_XCheck(void)
{
  learn_status_t status = LEARN_STATUS__OK;

  uint8_t xcheck_pos_max   = Param_ReadValue_8Bit(
      CPARAM8__Traction_NTS_Pos_XCheck);
  uint8_t xcheck_speed_max = Param_ReadValue_8Bit(
      CPARAM8__Traction_NTS_Speed_Xcheck);

  xcheck_pos_max   = (xcheck_pos_max == 0) ?
      DEFAULT_NTS_POS_XCHECK_VALUE : xcheck_pos_max;
  xcheck_speed_max = (xcheck_speed_max == 0) ?
      DEFAULT_NTS_SPEED_XCHECK_VALUE : xcheck_speed_max;

  // Cross check points
  for(uint8_t i = 0; i < NTS_entry->count; i++)
  {
    uint32_t pos_diff   = abs(NTS_entry->positions[i] -
                              NTS_xcheck.positions[i]);
    uint32_t speed_diff = abs(NTS_entry->velocities[i] -
                              NTS_xcheck.velocities[i]);

    // TODO: make new NTS position and velocity xcheck parameters
    if( (pos_diff   > xcheck_pos_max) ||
        (speed_diff > xcheck_speed_max) )
    {
      status = LEARN_NTS_STATUS__XCHECK_FAIL;
      break;
    }
  }

  if( (NTS_entry->count != NTS_xcheck.count) )
  {
    status = LEARN_NTS_STATUS__XCHECK_FAIL;
  }

  return status;
}

static void Learn_NTS_WriteParams(learn_usr_t type,
    uint32_t relative_dist)
{
  for(uint32_t i = 0; i < NTS_xcheck.count; i++)
  {
    uint32_t uiPosition_05mm;
    en_car_param_24bit pos_param_base;
    en_car_param_16bit speed_param_base;

    // NTS positions
    if(type == LEARN_DNTS_DETS)
    {
      uiPosition_05mm  = (uint32_t)(relative_dist +
          (NTS_xcheck.positions[i] * ONE_INCH__05MM));

      pos_param_base   = CPARAM24__Traction_DNTS_Prof_1_Pos_1   + i;
      speed_param_base = CPARAM16__Traction_DNTS_Prof_1_Speed_1 + i;
    }
    else if(type == LEARN_UNTS_UETS)
    {
      uiPosition_05mm  = (uint32_t)(relative_dist -
          (NTS_xcheck.positions[i] * ONE_INCH__05MM));

      pos_param_base   = CPARAM24__Traction_UNTS_Prof_1_Pos_1   + i;
      speed_param_base = CPARAM16__Traction_UNTS_Prof_1_Speed_1 + i;
    }

    Param_WriteValue_24Bit(pos_param_base, uiPosition_05mm);
    Param_WriteValue_16Bit(speed_param_base, NTS_xcheck.velocities[i]);
  }
}

static en_active_inactive Learn_NTS_CheckParamSync(learn_usr_t type,
    uint32_t relative_dist)
{
  en_active_inactive status = ACTIVE;
  for(uint32_t i = 0; i < NTS_entry->count; i++)
  {
    uint32_t uiPosition_05mm;
    en_car_param_24bit pos_param_base;
    en_car_param_16bit speed_param_base;

    if(type == LEARN_UNTS_UETS)
    {
      uiPosition_05mm = (uint32_t)(relative_dist -
          (NTS_xcheck.positions[i] * ONE_INCH__05MM));

      pos_param_base   = CPARAM24__Traction_UNTS_Prof_1_Pos_1   + i;
      speed_param_base = CPARAM16__Traction_UNTS_Prof_1_Speed_1 + i;
    }
    else if(type == LEARN_DNTS_DETS)
    {
      uiPosition_05mm = (uint32_t)(relative_dist +
          (NTS_xcheck.positions[i] * ONE_INCH__05MM));

      pos_param_base   = CPARAM24__Traction_DNTS_Prof_1_Pos_1   + i;
      speed_param_base = CPARAM16__Traction_DNTS_Prof_1_Speed_1 + i;
    }

    uint32_t uiPosParam_05mm = Param_ReadValue_24Bit(pos_param_base);
    int16_t iSpeedParam_fpm  = (int16_t)Param_ReadValue_16Bit(speed_param_base);
    if( (uiPosParam_05mm != uiPosition_05mm) ||
        (iSpeedParam_fpm != NTS_xcheck.velocities[i]) )
    {
      status = INACTIVE;
      break;
    }
  }

  return status;
}

