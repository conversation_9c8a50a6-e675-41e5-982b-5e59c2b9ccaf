/****************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file sr_parameters.c
 * @version 1.0.0
* @brief Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
* @details Subroutine updating processor parameter CRCs, and services requests between nodes for parameters
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include "app.h"
#include "sr_parameters.h"
#include "sr_FRAM.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (0)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (10)
/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
#ifdef ENABLE_PARAMETER_MASTER
static en_param_blocks eBlockToUpdate_OEM = NUM_PARAM_BLOCKS;
static en_param_blocks eBlockToUpdate_User = NUM_PARAM_BLOCKS;
#endif
#ifdef ENABLE_EFLASH_NODE
typedef enum
{
   EFLASH_UPDATE_STATE__INACTIVE,
   EFLASH_UPDATE_STATE__UPDATE_USER_COPY,

   NUM_EFLASH_UPDATE_STATES
}en_eflash_update_state;
/* Used to split the update of the recovery sector and the write from recovery to user copy */
static en_eflash_update_state eEFlashUpdateState;
#endif
/******************************************************************************
* Function Definitions
*******************************************************************************/
#ifdef ENABLE_EFLASH_NODE
/* @brief Regularly checks for differences between the eflash and running copy of parameters and if detected, updates the eflash
 * Adapted from LceFs_Commit  */
static void UpdateParametersEFlash(void)
{
   static uint8_t ucBlockToUpdate;
   uint32_t uiCRC_EFlash;
   uint32_t uiCRC_Local;
   uint8_t ucBlockIndex;
   en_pass_fail eError = PASS;
   en_active_inactive bWriteToEFlash;
#if defined(ENABLE_PARAMETER_MASTER)
   /* Suppress update of eflash when default is in progress */
   if( ( eBlockToUpdate_OEM >= NUM_PARAM_BLOCKS )
    && ( eBlockToUpdate_User >= NUM_PARAM_BLOCKS )
    && ( Param_GetState() == PARAM_STATE__IDLE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE ) )
    {
#elif defined(ENABLE_EFLASH_NODE) /* CT_A7 */
   /* Suppress this process when out of sync with master to prevent repeated writes to the EFLASH */
   if( ( Param_GetBlockToUpdate() == NUM_PARAM_BLOCKS )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
    && ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE ) )
   {
#endif
      switch(eEFlashUpdateState)
      {
         case EFLASH_UPDATE_STATE__INACTIVE:
         {
               bWriteToEFlash = INACTIVE;
               for(ucBlockIndex = 0; ucBlockIndex < NUM_PARAM_BLOCKS; ucBlockIndex++)
               {
                  uiCRC_EFlash = Param_GetBlockCRC_EFlash(ucBlockIndex);
                  uiCRC_Local = Param_GetBlockCRC_Local(ucBlockIndex);
                  if( uiCRC_Local != uiCRC_EFlash )
                  {
                     bWriteToEFlash = ACTIVE;
                     ucBlockToUpdate = ucBlockIndex;
                     break;
                  }
               }
               if( bWriteToEFlash == ACTIVE )
               {
                   eEFlashUpdateState = EFLASH_UPDATE_STATE__UPDATE_USER_COPY;
               }
         }
            break;
         case EFLASH_UPDATE_STATE__UPDATE_USER_COPY:
         {
//            int32_t iStartTime_1us = Timer_GetCount_1us();
            st_param_block *pstParamBlocks = Param_GetBlockStructure(ucBlockToUpdate);
            uint32_t uiAddressToWrite = FRAM_START_ADDRESS__PARAMETERS_MAIN + (ucBlockToUpdate * EFLASH_PARAM_BLOCK_SIZE__BYTES);
            for(uint8_t i = 0; i < FRAM_WRITES_TYPE__TRIPLET; i++)
            {
                eError |= FRAM_WriteSingleBlock(uiAddressToWrite, pstParamBlocks, EFLASH_PARAM_BLOCK_SIZE__BYTES);
                uiAddressToWrite += FRAM_PARAMETERS_MAIN_SIZE__BYTES;
            }
//            int32_t iDiffTime_1us = Timer_GetCount_1us() - iStartTime_1us; // 9,809 at 80mhz
            if( eError == PASS )
            {
               Param_SetBlockCRC_EFlash(ucBlockToUpdate, Param_GetBlockCRC_Local(ucBlockToUpdate));
               eEFlashUpdateState = EFLASH_UPDATE_STATE__INACTIVE;
            }
         }
            break;
         default:
            eEFlashUpdateState = EFLASH_UPDATE_STATE__INACTIVE;
            break;
      }
   }
   if( eError != PASS )
   {
      en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CALM__DEFAULT_BACKUP_FAILED_MR : CALM__DEFAULT_BACKUP_FAILED_CT;
      Alarms_SetAlarm(eAlarm);
   }
   else if( eEFlashUpdateState != EFLASH_UPDATE_STATE__INACTIVE )
   {
      en_car_alarm eAlarm = ( System_GetNodeID() == SYS_NODE__MR_A7 ) ? CALM__EFLASH_UPDATE_MR : CALM__EFLASH_UPDATE_CT;
      Alarms_SetAlarm(eAlarm);
   }
}
#endif

/* @fn void vParameters_Task(void *pvParameters)
 * @brief Execute the vParameters_Task to update the  parameters according to UI inputs
 * and also update the parameters to EFlash
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vParameters_Task(void *pvParameters)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		Param_Run();
		if( Param_GetState() != PARAM_STATE__STARTUP )
		{
			Floor_UpdateFloorPositions();
		}
		if( ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__RUNNING) == INACTIVE )
				&& ( Motion_GetMotionRunFlag(MOTION_RUN_FLAG__SAFETY_TOGGLE) == INACTIVE )
				&& ( ( Param_GetState() == PARAM_STATE__STARTUP )
						|| ( Param_GetBlockToUpdate() != NUM_PARAM_BLOCKS ) ) )
		{
			Faults_SetFault(CFLT__PARAMETER_SYNC_MR_A4+System_GetNodeID());
		}

#ifdef ENABLE_EFLASH_NODE
		UpdateParametersEFlash();
#endif

		vTaskDelay(PARAMETERS_TASK_DELAY);
	}
}

