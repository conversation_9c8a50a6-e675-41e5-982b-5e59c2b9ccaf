/*******************************************************************************
* @Copyright (C) 2024 by Vantage Elevation
* @file           : sr_fpga.c
* @version        : 1.0.0
* @brief INTENDED FOR TEMPORARY USE ONLY. CAN BE REMOVED AFTER ALL CONTOLLERS UPDATED TO REV4 MR/CT BOARDS.
* @details This subroutine updates on board FPGAs
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include <string.h>
#include <stdio.h>
#include "fpga_def.h"
#include "fpga_def_old.h"
#include "app_spi.h"
#include "app_gpio.h"
#include "dg_car_mr_a7.h"
#include "dg_car_ct_a7.h"
#include "dg_car_ct_b4.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_RUN_INTERVAL_1MS                (20)
#define TRANSMIT_TIMEOUT__1MS                      (10)

#define FPGA_MESSAGE_RESEND_RATE_1MS               (100)
#define FPGA_OFFLINE_TIMEOUT_1MS                   (3000)

#define FPGA_TOGGLE_TIMEOUT_1MS                    (10000)
#define FPGA_TOGGLE_MIN_SAMPLE_TIME_20MS           (5) /* Minimum amount of time each input must be seen in the pass state during the inactive snapshot phase */

#define FPGA_CROSSCHECK_TIMEOUT_20MS               (100) /* How long the FPGA and MCU input status must be in disagreement before a fault occurs */

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/

/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef enum
{
   SPI_PKT_RET__PASS,
   SPI_PKT_RET__FAIL,
   SPI_PKT_RET__ERR,
} en_spi_pkt_ret;

/* State machine control */
typedef struct
{
   uint8_t aucRxSPIBuf[FPGA_SPI_BUFFER_SIZE__BYTES_OLD_FPGA];
   uint8_t aucTxSPIBuf[FPGA_SPI_BUFFER_SIZE__BYTES_OLD_FPGA];

   uint32_t uiInputBitmap;                                                 /* Most recent copy of FPGA inputs received */

   uint8_t aucXCheckDebounce_20ms[NUM_FPGA_OLD_CT_INPUTS];                     /* Array of counters for debouncing input crosscheck violations */

   en_fpga_toggle_states eToggleState_MCU;                                 /* Tracks the MCU side toggle state */
   en_fpga_toggle_states eToggleState_FPGA;                                /* Tracks the FPGA side toggle state */

   en_car_fault eToggleFault;                                              /* Tracks the fault marking the first toggled input encountered that failed the toggle check  */

   uint16_t uwOfflineTimer_1ms;                                            /* Checks if no valid packet ahs been received within a certain period */
   uint16_t uwResendTimer_1ms;                                             /* Forces resend of messages to the fpga */

   uint8_t ucRxErrorCounter_MCU;                                           /* Tracks number of number of packet validation errors seen by the MCU */
   uint8_t ucRxErrorCounter_FPGA;                                          /* Tracks number of number of packet validation errors seen by the FPGA */
   uint8_t ucVersion;                                                      /* Reported FPGA version */
   en_fpga_ct_errors eError;
} st_ct_fpga_control_FPGA_OLD;
/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/
static st_ct_fpga_control_FPGA_OLD stControlFPGA_FPGA_OLD;
/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @brief Loads the FPGA status datagram which will be written to the datagram scheduler for transmission to other diagnostics nodes */
void FPGA_OLD_LoadDatagram_FPGA_Status_CTB(un_datagram *punDatagram)
{
   punDatagram->auc8[0] = stControlFPGA_FPGA_OLD.eToggleState_FPGA & 0x0F;
   punDatagram->auc8[0] |= ( stControlFPGA_FPGA_OLD.eToggleState_MCU & 0x0F ) << 4;
   punDatagram->auc8[1] = stControlFPGA_FPGA_OLD.eError;
   punDatagram->auc8[2] = stControlFPGA_FPGA_OLD.ucRxErrorCounter_FPGA;
   punDatagram->auc8[3] = stControlFPGA_FPGA_OLD.ucRxErrorCounter_MCU;

   punDatagram->aui32[1] = FPGA_GetInputBitmap_CTB();

   punDatagram->auc8[12] = stControlFPGA_FPGA_OLD.ucVersion & 0x7F;
   if( stControlFPGA_FPGA_OLD.uwOfflineTimer_1ms < FPGA_OFFLINE_TIMEOUT_1MS )
   {
      punDatagram->auc8[12] |= 0x80;
   }
}

/* @brief Transmit and read back byte arrays to/from the FPGA chip.
 * @return eError, returns 1 if the request has failed
 */
static en_pass_fail SPITransfer(void)
{
   en_pass_fail eError;
   // RK TODO: determine how best to control NSS signal. For now, only working is to enable/disable SPI3.
   //HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_RESET);
   HAL_GPIO_WritePin(GPIOA,GPIO_PIN_15,GPIO_PIN_RESET);//HAL_SPI_MspInit(&hspi3); // Drive NSS low.
   //__disable_irq();
   HAL_StatusTypeDef eStatus = HAL_SPI_TransmitReceive(&hspi3, stControlFPGA_FPGA_OLD.aucTxSPIBuf, stControlFPGA_FPGA_OLD.aucRxSPIBuf, FPGA_SPI_BUFFER_SIZE__BYTES_OLD_FPGA, TRANSMIT_TIMEOUT__1MS);
   //__enable_irq();
   //HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_SET);
   HAL_GPIO_WritePin(GPIOA,GPIO_PIN_15,GPIO_PIN_SET);//HAL_SPI_MspDeInit(&hspi3); // Drive NSS high.
   if( eStatus == HAL_OK )
   {
      eError = PASS;
   }
   else
   {
      printf("RD: %u\n", eStatus);
      eError = FAIL;
   }
   return eError;
}

/* @brief Populate the SPI packet to be sent to FPGA.
 * @return None
 */
static void SetTxPacket(void)
{
   // Set header section
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[0] = 0xFF;
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[1] = 0x00;
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[2] = 0x00;
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[3] = 0x00;

   // Set data section
   // byte4 init
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] = 0;
   // byte4 bit6
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__IC_STOP_SWITCH)) << 6;
   // byte4 bit5
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__FIRE_STOP_SWITCH)) << 5;
   // byte4 bit4
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__HA_INSPECTION)) << 4;
   // byte4 bit3
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__IC_INSPECTION)) << 3;
   // byte4 bit2
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__ESC_HATCH)) << 2;
   // byte4 bit1
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__CT_STOP_SWITCH)) << 1;
   // byte4 bit0
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[4] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__CT_INSPECTION));
   // byte5 init
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] = 0;
   // byte5 bit7
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__GATESWITCH_FRONT)) << 7;
   // byte5 bit6
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__GATESWITCH_REAR)) << 6;
   // byte5 bit5
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DC_IN_06)) << 5;
   // byte5 bit4
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DC_IN_07)) << 4;
   // byte5 bit3
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DC_IN_08)) << 3;
   // byte5 bit2
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DIP_01)) << 2;
   // byte5 bit1
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DIP_02)) << 1;
   // byte5 bit0
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[5] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DIP_03));
   // byte6 init
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[6] = 0;
   // byte6 bit7
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DIP_04)) << 7;
   // byte6 bit6
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DIP_05)) << 6;
   // byte6 bit5
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__REAR_DOOR_ENABLE)) << 5;
   // byte6 bit4
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__DIP_07)) << 4;
   // byte6 bit3
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[6] |= CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(FPGA_OLD_CT_INPUT__TRACTION_ENABLE)) << 3;
   // byte7 init
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[7] = 0;
   // duplicate data for validation
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[8] = stControlFPGA_FPGA_OLD.aucTxSPIBuf[4];
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[9] = stControlFPGA_FPGA_OLD.aucTxSPIBuf[5];
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[10] = stControlFPGA_FPGA_OLD.aucTxSPIBuf[6];
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[11] = stControlFPGA_FPGA_OLD.aucTxSPIBuf[7];

   // Set footer section
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[12] = 0x80;
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[13] = 0x00;
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[14] = 0x00;
   stControlFPGA_FPGA_OLD.aucTxSPIBuf[15] = 0x00;
}

/* @brief Validate SPI packet received from FPGA, and extract relevant data.
 * @return en_spi_pkt_ret value depending on pass or fail when parsing data.
 */
static en_spi_pkt_ret ProcessRxPacket(void)
{
   if( !(stControlFPGA_FPGA_OLD.aucRxSPIBuf[0] == 0xFF && stControlFPGA_FPGA_OLD.aucRxSPIBuf[1] == 0x00
         && stControlFPGA_FPGA_OLD.aucRxSPIBuf[2] == 0x00 && stControlFPGA_FPGA_OLD.aucRxSPIBuf[3] == 0x00) )
   {
      fpga_printf("Error: invalid packet received: header! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return SPI_PKT_RET__FAIL;
   }
   if( !(stControlFPGA_FPGA_OLD.aucRxSPIBuf[20] == 0x80 && stControlFPGA_FPGA_OLD.aucRxSPIBuf[21] == 0x00
         && stControlFPGA_FPGA_OLD.aucRxSPIBuf[22] == 0x00 && stControlFPGA_FPGA_OLD.aucRxSPIBuf[23] == 0x00) )
   {
      fpga_printf("Error: invalid packet received: footer! %s fileLn%d\n", __FUNCTION__, __LINE__);
      return SPI_PKT_RET__ERR;
   }
   for( uint8_t ucI = 4; ucI < 12; ucI++)
   {
      if( stControlFPGA_FPGA_OLD.aucRxSPIBuf[ucI] != stControlFPGA_FPGA_OLD.aucRxSPIBuf[ucI+8] )
      {
         fpga_printf("Error: invalid packet received: data corruption! %s fileLn%d\n", __FUNCTION__, __LINE__);
         return SPI_PKT_RET__ERR;
      }
   }

   // Parse data section1
   if( stControlFPGA_FPGA_OLD.aucRxSPIBuf[4] & 0x80 ) // byte4 bit8
   {
      fpga_printf("Error: invalid packet received: data1 1st cleared bit! %s fileLn%d\n", __FUNCTION__, __LINE__); // pkt bit index 32
      return SPI_PKT_RET__ERR;
   }
   // pkt bit index 40 thru 46
   stControlFPGA_FPGA_OLD.eError = stControlFPGA_FPGA_OLD.aucRxSPIBuf[5] >> 1;
   // byte6 bit4, pkt bit index 51
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__ESC_HATCH, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[6] & 0x10) > 0);
   // byte6 bit3, pkt bit index 52
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__CT_STOP_SWITCH, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[6] & 0x08) > 0);
   // byte6 bit2, pkt bit index 53
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__CT_INSPECTION, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[6] & 0x04) > 0);
   // byte6 bit1, pkt bit index 54
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__GATESWITCH_FRONT, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[6] & 0x02) > 0);
   // byte6 bit0, pkt bit index 55
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__GATESWITCH_REAR, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[6] & 0x01) > 0);
   // byte7 bit7, pkt bit index 56
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DC_IN_06, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x80) > 0);
   // byte7 bit6, pkt bit index 57
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DC_IN_07, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x40) > 0);
   // byte7 bit5, pkt bit index 58
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DC_IN_08, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x20) > 0);
   // byte7 bit4, pkt bit index 59
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DIP_01, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x10) > 0);
   // byte7 bit3, pkt bit index 60
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DIP_02, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x08) > 0);
   // byte7 bit2, pkt bit index 61
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DIP_03, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x04) > 0);
   // byte7 bit1, pkt bit index 62
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DIP_04, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x02) > 0);
   // byte7 bit0, pkt bit index 63
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DIP_05, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[7] & 0x01) > 0);

   // Parse data section2
   if( stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x80 ) // byte8 bit7
   {
      fpga_printf("Error: invalid packet received: data2 1st cleared bit! %s fileLn%d\n", __FUNCTION__, __LINE__); // pkt bit index 64
      return SPI_PKT_RET__ERR;
   }
   // byte8 bit6, pkt bit index 65
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__REAR_DOOR_ENABLE, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x40) > 0);
   // byte8 bit5, pkt bit index 66
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DIP_07, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x20) > 0);
   // byte8 bit4, pkt bit index 67
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__TRACTION_ENABLE, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x10) > 0);
   // byte8 bit3, pkt bit index 68
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__NTS_ACTIVE, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x08) > 0);
   // byte8 bit2, pkt bit index 69
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__DOOR_ZONE, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x04) > 0);
   // byte8 bit1, pkt bit index 70
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__SAFETY_TOGGLE, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x02) > 0);
   // byte8 bit0, pkt bit index 71
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__IC_STOP_BYPASS, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[8] & 0x01) > 0);
   // byte9 bit7, pkt bit index 72
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__IC_STOP_SWITCH, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[9] & 0x80) > 0);
   // byte9 bit6, pkt bit index 73
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__FIRE_STOP_SWITCH, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[9] & 0x40) > 0);
   // byte9 bit5, pkt bit index 74
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__HA_INSPECTION, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[9] & 0x20) > 0);
   // byte9 bit4, pkt bit index 75
   System_SetBitByIndex(&stControlFPGA_FPGA_OLD.uiInputBitmap, FPGA_OLD_CT_INPUT__IC_INSPECTION, (stControlFPGA_FPGA_OLD.aucRxSPIBuf[9] & 0x10) > 0);

   FPGA_SetInputBitmap_CTB(stControlFPGA_FPGA_OLD.uiInputBitmap);
   return SPI_PKT_RET__PASS;
}

/* @brief Handle FPGA errors as car system faults. */
static void HandleError(void)
{
   if( FPGA_GetOnlineFlag_CTB() )
   {
#if 0 // CT PLD sends all its errors to MR at this time, so no need to set them here.
      en_car_fault eFault = FPGA_GetCarFaultFromFPGAError_CTB( FPGA_GetError_CTB() );
      if( eFault > CFLT__NONE && eFault < NUM_CAR_FAULT )
      {
         Faults_SetFault_PLD(eFault);
      }
#endif
   }
   else
   {
      Faults_SetFault(CFLT__FPGA_OFFLINE_CT);
   }
}

/* @brief Check if MCU input states match that of those recived from FPGA. */
static void MCUXcheck(void)
{
   /* Checks input states and sets fault if they don't match after a debounce period. */
   for(en_fpga_old_ct_inputs eInput = 0; eInput <= NUM_FPGA_OLD_CT_INPUTS; eInput++)
   {
      en_car_fault eFault = FPGA_OLD_GetXcheckFaultFromInput_CTB(eInput);
      if( eFault != NUM_CAR_FAULT )
      {
         uint8_t bMCUActive = CarInput_GetInputValue(FPGA_OLD_GetCarInputFromFPGAInput_CTB(eInput));
         uint8_t bFPGAActive = FPGA_OLD_GetInputBit_CTB(eInput);
         if(bMCUActive != bFPGAActive)
         {
            if(stControlFPGA_FPGA_OLD.aucXCheckDebounce_20ms[eInput] < FPGA_CROSSCHECK_TIMEOUT_20MS)
            {
               stControlFPGA_FPGA_OLD.aucXCheckDebounce_20ms[eInput] += 1;
            }
            else
            {
               Faults_SetFault(eFault);
            }
         }
         else
         {
            stControlFPGA_FPGA_OLD.aucXCheckDebounce_20ms[eInput] = 0;
         }
      }
   }
}

//#define fpga_ct_stat_dbg
#ifdef fpga_ct_stat_dbg
static void debugFPGAStatus(void)
{
   static uint32_t uiFPGADebugTimer_1ms;
   if( uiFPGADebugTimer_1ms >= 1000 )
   {
      /*for(int i = 0; i < 6; i++){
         for(int j = 0; j < 4; j++){
            printf("%02x,", stControlFPGA_FPGA_OLD.aucRxSPIBuf[(i*4)+j]);
         }
         printf("\n");
      }*/
      printf("FPGA_GetVersion_CTB():%d\n", FPGA_GetVersion_CTB());
      printf("FPGA_GetOnlineFlag_CTB():%d\n", FPGA_GetOnlineFlag_CTB());
      printf("FPGA_GetToggleState_FPGA_CTB():%d\n", FPGA_GetToggleState_FPGA_CTB());
      printf("FPGA_GetToggleState_MCU_CTB():%d\n", FPGA_GetToggleState_MCU_CTB());
      printf("FPGA_GetError_CTB():%s\n", FPGA_GetErrorString_CTB(FPGA_GetError_CTB()));
      printf("FPGA_GetRXErrorCounter_FPGA_CTB():%d\n", FPGA_GetRXErrorCounter_FPGA_CTB());
      printf("FPGA_GetRXErrorCounter_MCU_CTB():%d\n", FPGA_GetRXErrorCounter_MCU_CTB());
      printf("FPGA Inputs CTB:\n");
      for(en_fpga_old_ct_inputs eI = 0; eI < NUM_FPGA_OLD_CT_INPUTS; eI++)
      {
         en_car_inputs eMCURef = FPGA_OLD_GetCarInputFromFPGAInput_CTB(eI);
         if( eMCURef < NUM_CAR_INPUTS )
         {
            printf(" %s:%d (ref:%d)\n", FPGA_GetInputString_CTB(eI), FPGA_OLD_GetInputBit_CTB(eI), CarInput_GetInputValue(eMCURef));
         }
         else
         {
            printf(" %s:%d (ref:NA)\n", FPGA_GetInputString_CTB(eI), FPGA_OLD_GetInputBit_CTB(eI));
         }
      }
      printf("----------------\n");
      uiFPGADebugTimer_1ms = 0;
   }
   else
   {
      uiFPGADebugTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
   }
}
#endif

/* @fn static void Run(void)
 * @brief Run function for the subroutine that is executed every uwRunInterval_1ms
 * @param None
 * @return None
 */
void FPGA_OLD_Run(void)
{
   static uint16_t uwFPGAMessageResendTimer_1ms;

   GPIO_WriteOutput(enLOCAL_OUT__PIO_02, Position_GetDZ_CT_A7());
   GPIO_WriteOutput(enLOCAL_OUT__PIO_04, Safety_CheckIfICStopBypassed_MRA());

   // Make sure MCU rear door enable matches PLD rear door enable
   if( (en_active_inactive)Door_GetRearDoorEnabled() != CarInput_GetInputValue(CIN__CT_DIP_06) )
   {
      Faults_SetFault(CFLT__CT_FPGA_REAR_DOOR_EN);
   }

   if( uwFPGAMessageResendTimer_1ms >= FPGA_MESSAGE_RESEND_RATE_1MS )
   {
      en_pass_fail eTransRet = FAIL;
      en_spi_pkt_ret eProcRet = SPI_PKT_RET__FAIL;
      SetTxPacket();
      memset(stControlFPGA_FPGA_OLD.aucRxSPIBuf, 0, 4); // clear the packet header
      eTransRet = SPITransfer();
      eProcRet = ProcessRxPacket();
      if( eTransRet == FAIL || eProcRet == SPI_PKT_RET__FAIL || eProcRet == SPI_PKT_RET__ERR )
      {
         stControlFPGA_FPGA_OLD.uwOfflineTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
      }
      else
      {
         stControlFPGA_FPGA_OLD.uwOfflineTimer_1ms = 0;
      }
      if( stControlFPGA_FPGA_OLD.uwOfflineTimer_1ms >= FPGA_OFFLINE_TIMEOUT_1MS )
      {
         stControlFPGA_FPGA_OLD.uwOfflineTimer_1ms = FPGA_OFFLINE_TIMEOUT_1MS;
      }
      if( eProcRet == SPI_PKT_RET__ERR && stControlFPGA_FPGA_OLD.ucRxErrorCounter_MCU < 255 )
      {
         stControlFPGA_FPGA_OLD.ucRxErrorCounter_MCU++;
      }
      uwFPGAMessageResendTimer_1ms = 0;
   }
   else
   {
      uwFPGAMessageResendTimer_1ms += SUBROUTINE_RUN_INTERVAL_1MS;
   }
   HandleError();
   MCUXcheck();
#ifdef fpga_ct_stat_dbg
   debugFPGAStatus();
#endif
}
