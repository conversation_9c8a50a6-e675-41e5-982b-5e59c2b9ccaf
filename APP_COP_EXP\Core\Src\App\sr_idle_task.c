
/*******************************************************************************
* @Copyright (C) 2025 by Vantage Elevation
* @file           : idle_task.c
* @version 		  : 1.0.0
* @details This file defines the `IdleTaskModule_t` structure and functions to
*          handle periodic execution of different tasks during the idle hook.
*          It includes:
*          - vIdleTaskModulesInit() - Initializes the periodic tasks and their
*            initial ticks.
*          - vApplicationIdleHook() - Checks the tasks' periodic delays and
*            executes the respective functions when their periods elapse.
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "app.h"
#include "sr_LED.h"
#include "sr_RTC.h"
#include "app_watchdog.h"

/******************************************************************************
* Module Typedefs
*******************************************************************************/
typedef void(*IdleTaskFunction_t)(void);

/*****************************************************************************
 * @brief  Structure to hold information about an idle task.
 *
 * This structure contains the task function, the current tick count when the task
 * was last executed, and the task's periodic delay (in ticks) at which it should
 * be executed again.
 *****************************************************************************/
typedef struct
{
	IdleTaskFunction_t function;
	TickType_t ticks;
	const TickType_t ticks_period;
}IdleTaskModule_t;

IdleTaskModule_t IdleTaskModules[] =
{
		{vLED_Update_Task,     0, LED_TASK_PERIODIC_DELAY_MS},
		{vRTC_Update,          0, RTC_TASK_PERIODIC_DELAY_MS},
		{vWatchdog_update,		 0, WATCHDOG_TASK_PERIODIC_DELAY_MS}
};// 3 Tasks
const uint32_t IdleTaskModuleCount = sizeof(IdleTaskModules)/sizeof(IdleTaskModule_t);

/* @fn void vIdleTaskModulesInit(void)
 * @brief Initializes the idle task modules by setting the initial tick count for each task.
 * @param None
 * @return None
 */
void vIdleTaskModulesInit(void)
{
	for(uint32_t i = 0; i < IdleTaskModuleCount; i++)
	{
		IdleTaskModule_t *module = &IdleTaskModules[i];
		module->ticks = xTaskGetTickCount();
	}
}

/* @fn void vApplicationIdleHook(void)
 * @brief Application idle hook function that checks and executes idle tasks based on their time periods.
 *        This function is called during the idle time of the RTOS scheduler. It checks the elapsed
 *        time for each idle task and executes the corresponding function when the task’s time period has elapsed.
 * @param None
 * @return None
 */
void vApplicationIdleHook(void)
{
	for(uint32_t i = 0; i < IdleTaskModuleCount; i++)
	{
		IdleTaskModule_t *module = &IdleTaskModules[i];
		if((xTaskGetTickCount() - module->ticks) > module->ticks_period)
		{
			module->ticks = xTaskGetTickCount();
			module->function();
		}
	}
}
