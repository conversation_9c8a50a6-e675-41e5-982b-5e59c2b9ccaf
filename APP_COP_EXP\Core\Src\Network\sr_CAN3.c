/*******************************************************************************
 * @Copyright (C) 2024 by Vantage Elevation
 * @file           : sr_CAN3.c
 * @version        : 1.0.0
 * @brief Subroutine handling CAN3 network
 * @details Subroutine handling CAN3 network
 *****************************************************************************/

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "sr_fault_alarm.h"
#include <string.h>
#include "sr_CAN3.h"
#include "main.h"
#include "hall_call_def.h"
#include "app_can.h"
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/

/******************************************************************************
 * Module Preprocessor Constants
 *******************************************************************************/

// How long the bus can go without receiving a message before an offline flag is set
#define CAN_OFFLINE_TIMEOUT_1MS                    (15000)
#define CAN_OFFLINE_TIMEOUT_INVALID_1MS            (0xFFFF)
/******************************************************************************
 * Module Preprocessor Macros
 *******************************************************************************/

/******************************************************************************
 * Module Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variable Definitions
 *******************************************************************************/
uint16_t gCAN3_Task_Delay = 0;
/******************************************************************************
 * Module Variable Definitions
 *******************************************************************************/
static uint16_t uwOfflineTimer_1ms = CAN_OFFLINE_TIMEOUT_INVALID_1MS;
/* Accounting array to keep track of which types of hall boards are connected to bus. */
static uint8_t aucHBFuncsOnCBus3[MAX_NUM_HB_FUNC_SW_SETTINGS];
/******************************************************************************
 * Function Definitions
 *******************************************************************************/
/* @fn en_pass_fail CAN3_TransmitDatagram(st_CAN_msg *pstMsg)
 * @brief Returns 0 if the packet was successfully loaded for transmit
 * @param pstMsg, pointer to CAN message to transmit
 * @return Returns 0 if the packet was successfully loaded for transmit
 */
en_pass_fail CAN3_TransmitDatagram(st_CAN_msg *pstMsg)
{
	en_pass_fail eError = FAIL;
	if( pstMsg->bExtendedID == INACTIVE )
	{
		if( pstMsg->uiID & NETWORK_PACKET_ID_EXTENDED_BITMAP ) pstMsg->bExtendedID = ACTIVE;
		else pstMsg->bExtendedID = INACTIVE;
	}
	pstMsg->uiID &= NETWORK_PACKET_ID_EXTENDED_MASK;
	if( CAN3_LoadToRB(pstMsg) == PASS )
	{
		eError = PASS;
	}
	return eError;
}
/* @fn en_active_inactive CAN3_HallBoardFuncOnBus(uint8_t ucFunc)
 * @brief Determine if a hall board with given function arg is connected to the CAN bus.
 * @param ucFunc Function setting value to check
 * @return ACTIVE if on bus, INACTIVE if not.
 */
en_active_inactive CAN3_HallBoardFuncOnBus(uint8_t ucFunc)
{
	if( ucFunc >= MAX_NUM_HB_FUNC_SW_SETTINGS ) return INACTIVE;
	return aucHBFuncsOnCBus3[ucFunc];
}
/* @fn static void UnloadDatagrams(void)
 * @brief Unloads messages from the CAN ring buffer
 * @param None
 * @return None
 */
static void UnloadDatagrams(void)
{
	st_CAN_msg stRxMsg;
	for(uint8_t i = 0; i < CAN_RX_RING_BUFFER_SIZE; i++)
	{
		if( CAN3_UnloadFromRB(&stRxMsg) == PASS )
		{
			// Check if dg sent from hall board
			if(stRxMsg.uiID < MAX_NUM_HB_TX_DATAGRAM_IDS)
			{
				uint16_t uwId = Hall_Board_ProcessCallDatagram(&stRxMsg);
				// If it's a normal hall board dg, record that we've found it's function on this bus
				if( uwId )
				{
					st_hb_settings stHBId = Hall_Board_ParseHBTxMsgId((uint32_t)stRxMsg.uiID);
					aucHBFuncsOnCBus3[stHBId.ucFunc] = ACTIVE;
				}
			}
			else
			{
				Error_SetActiveErrorRiser(RIS_ERROR__INVALID_DEVICE_CAN3);
			}
			CAN3_IncrementStatusCounter(CAN_COUNTER__RX_PACKET);
			uwOfflineTimer_1ms = 0;
		}
		else
		{
			break;
		}
	}
}
/* @fn uint8_t CAN3_CheckIfCommLoss(void)
 * @brief Returns 1 if no communication has been received in the timeout period
 * @param None
 * @return Returns 1 if no communication has been received in the timeout period
 */
uint8_t CAN3_CheckIfCommLoss(void)
{
	return ( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS );
}
/* @fn static void UpdateOfflineTimer(void)
 * @brief Checks if no messages have been received in defined the timeout period.
 * @param None
 * @return None
 */
static void UpdateOfflineTimer(void)
{
	if( uwOfflineTimer_1ms != CAN_OFFLINE_TIMEOUT_INVALID_1MS )
	{
		if( uwOfflineTimer_1ms >= CAN_OFFLINE_TIMEOUT_1MS )
		{
			switch(Main_GetLocalBoardType())
			{
				case BOARD_COP:
					break;
				case BOARD_EXP_MASTER:
				case BOARD_EXP_SLAVE:
					//Error_SetActiveError(EXP_ERROR__COMM_CAN3);
					break;
				case BOARD_RISER:
					Error_SetActiveErrorRiser(RIS_ERROR__COMM_CAN3);
					break;
				case BOARD_INVALID:
				default:
				break;
			}
		}
		else
		{
			uwOfflineTimer_1ms += CAN3_TASK_PERIODIC_DELAY_MS;
		}
	}
}

/* @fn void CAN3_Task_Init(void)
 * @brief Initializes the CAN3 task based on the local board type. It sets the task delay
 *        for the CAN3 task, enabling periodic execution for the RISER board and inactive
 *        execution for all other board types.
 * @param None
 * @return None
 */
void CAN3_Task_Init(void)
{
	switch(Main_GetLocalBoardType())
	{
		case BOARD_RISER:
			gCAN3_Task_Delay = CAN3_TASK_PERIODIC_DELAY_MS;
			break;
		default:
			gCAN3_Task_Delay = TASK_RUN_INTERVAL_INACTIVE_MS;
			break;
	}
}

/* @fn void vCAN3_Task(void *pvParams)
 * @brief Handles CAN3 task operations, including monitoring the bus error counter,
 *        checking for offline status, and updating offline timers. It also manages
 *        specific actions for each board type in case of bus errors.
 * @param pvParams Pointer to parameters passed to the task
 * @return None
 */
void vCAN3_Task(void *pvParams)
{
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
    while(1) {
	__disable_irq();
        // Unload Datagrams
        UnloadDatagrams();
	__enable_irq();
        // Update offline timer
        UpdateOfflineTimer();

        // Update CAN3 bus error counter
        CAN3_UpdateBusErrorCounter();

        // Check if CAN3 bus is offline
        if(CAN3_CheckForBusOffline())
        {
            switch(Main_GetLocalBoardType())
            {
                case BOARD_COP:
                    // Handle alarm for COP board (disabled)
                    // Alarms_SetAlarm(CALM__BUS_RESET_COP_N3);
                    break;
                case BOARD_EXP_MASTER:
                case BOARD_EXP_SLAVE:
                    // Handle error for EXP_MASTER and EXP_SLAVE boards (disabled)
                    // Error_SetActiveError(EXP_ERROR__BUS_OFFLINE_CAN3);
                    break;
                case BOARD_RISER:
                    // Handle error for RISER board
                    Error_SetActiveErrorRiser(RIS_ERROR__BUS_OFFLINE_CAN3);
                    break;
                case BOARD_INVALID:
                default:
                    // Handle other cases (no action)
                    break;
            }
        }
            vTaskDelay(gCAN3_Task_Delay); // Delay using the configured CAN3 task period
    }
}

