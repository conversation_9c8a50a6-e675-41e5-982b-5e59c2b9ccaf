/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2023 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"

#include "sr_LED.h"
#include "sr_fault_alarm.h"
#include "sr_Watchdog.h"
#include "sr_CAN1.h"
#include "sr_CAN2.h"
#include "sr_local_inputs.h"
#include "sr_local_outputs.h"
#include "sr_SPI_AB.h"
#include "sr_SPI_AU.h"
#include "sr_FRAM.h"
#include "sr_car_net.h"
#include "sr_parameters.h"
#include "sr_safety.h"
#include "sr_RTC.h"
#include "sr_operation.h"
#include "sr_position.h"
#include "sr_learn.h"
#include "sr_ETS.h"
#include "sr_learn_v2.h"
#include "sr_system_check.h"

#include "app_spi.h"
#include "app_error.h"
#include "temp_eflash_loader.h"
#include "app_gpio.h"
#include "app_timers.h"
#include "app_watchdog.h"
#include "app_crc.h"
#include "app_swv.h"
#include "app_can.h"
#include "app_uart.h"
#include "evt_timer.h"
#include "ulog.h"

#include <stdio.h>

#include "bootloader_def.h"
/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

#ifndef HSEM_ID_0
#define HSEM_ID_0 (0U) /* HW semaphore 0*/
#endif
/* USER CODE END PD */

/* USER CODE BEGIN 0 */
#define __APP_HEADER_SECTION     __attribute__((section(".app_header")))
__APP_HEADER_SECTION const uint32_t __app_header[] =
{
 0x37415443,                                                // APP ID
 SOFTWARE_VERSION_MAJOR | (SOFTWARE_VERSION_MINOR  << 16),  // VERSION 0
 SOFTWARE_VERSION_PATCH | (SOFTWARE_VERSION_CUSTOM << 16),  // VERSION 1
 0,                                                         // ALIGN 0
 0,                                                         // ALIGN 1
 0xEEEEEEEE,                                                // DUMMY SIZE
 0xCCCCCCCC,                                                // DUMMY CRC_STORED
 0xBBBBBBBB,                                                // DUMMY FILE_OFFSET
};

int __io_putchar(int ch)
{
  UART7_write((const uint8_t * )&ch, 1);
  return ch;
}

void car_system_logger(ulog_level_t severity, const char *msg)
{
  printf("%i: [%s]= %s\r\n",
      (int)Timer_GetCount_1ms(),
      ulog_level_name(severity),
      msg);
}

/* USER CODE END 0 */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
#define DEFINE_STATIC_TASK(name, stack_size)          \
    StackType_t name##_Stack[stack_size];            \
    StaticTask_t name##_TCB;

/* Define Static Buffers for All Tasks */
DEFINE_STATIC_TASK(Init_Task, FAULT_ALARM_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(FaultAlarm_Task, FAULT_ALARM_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Learning_Task, LEARNING_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(LocalInput_Task, LOCAL_INPUT_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(LocalOutput_Task, LOCAL_OUTPUT_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Safety_Task, SAFETY_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Operation_Task, OPERATION_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Parameters_Task, PARAMETERS_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(FRAM_Task, FRAM_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CAN1_Task, CAN1_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CAN2_Task, CAN2_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(CarNet_Task, CAR_NET_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(SPIAU_Task, SPIAU_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(SPIAB_Task, SPIAU_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Position_Task, POSITION_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(ETS_Task, ETS_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(Learning_V2_Task, LEARNING_TASK_STACK_SIZE)
DEFINE_STATIC_TASK(SystemCheck_Task, SYSTEM_CHECK_TASK_STACK_SIZE)

/* USER CODE BEGIN PV */
TaskParams_t TaskParams[] =
{
	{vFaultAlarm_Task,  "FaultAlarm_Task",  FAULT_ALARM_TASK_STACK_SIZE,  NULL, FAULT_ALARM_TASK_PRIORITY,	FaultAlarm_Task_Stack,  &FaultAlarm_Task_TCB},  //1
	{vCAN1_Task, 			  "CAN1_Task", 			  CAN1_TASK_STACK_SIZE, 				NULL, CAN1_TASK_PRIORITY,			    CAN1_Task_Stack,			  &CAN1_Task_TCB	},				//11
	{vCAN2_Task, 			  "CAN2_Task", 			  CAN2_TASK_STACK_SIZE, 				NULL, CAN2_TASK_PRIORITY,			    CAN2_Task_Stack,			  &CAN2_Task_TCB},				//12
	{vLocalInput_Task,  "LocalInput_Task",  LOCAL_INPUT_TASK_STACK_SIZE,  NULL, LOCAL_INPUT_TASK_PRIORITY,	LocalInput_Task_Stack,  &LocalInput_Task_TCB},    // 2
	{vLocalOutput_Task, "LocalOutput_Task", LOCAL_OUTPUT_TASK_STACK_SIZE, NULL, LOCAL_OUTPUT_TASK_PRIORITY,	LocalOutput_Task_Stack, &LocalOutput_Task_TCB},    // 2
	{vSPIAB_Task, 			"SPIAB_Task", 			SPIAB_TASK_STACK_SIZE, 				NULL, SPIAB_TASK_PRIORITY,			  SPIAB_Task_Stack,			  &SPIAB_Task_TCB	},				//13
	{vSPIAU_Task, 			"SPIAU_Task", 			SPIAU_TASK_STACK_SIZE, 				NULL, SPIAU_TASK_PRIORITY,			  SPIAU_Task_Stack,			  &SPIAU_Task_TCB},				//14
	{vFRAM_Task, 			  "FRAM_Task", 			  FRAM_TASK_STACK_SIZE, 				NULL, FRAM_TASK_PRIORITY,			    FRAM_Task_Stack,			  &FRAM_Task_TCB},			//
	{vCarNet_Task, 			"CarNet_Task", 			CAR_NET_TASK_STACK_SIZE, 			NULL, CAR_NET_TASK_PRIORITY,	 	  CarNet_Task_Stack,			&CarNet_Task_TCB},			//15
	{vParameters_Task, 	"Parameters_Task", 	PARAMETERS_TASK_STACK_SIZE,   NULL, PARAMETERS_TASK_PRIORITY,	  Parameters_Task_Stack,	&Parameters_Task_TCB	},		//10
	{vSafety_Task, 			"Safety_Task", 			SAFETY_TASK_STACK_SIZE, 			NULL, SAFETY_TASK_PRIORITY,		    Safety_Task_Stack,			&Safety_Task_TCB},      //3
	{vPosition_Task, 		"Position_Task", 		POSITION_TASK_STACK_SIZE, 		NULL, POSITION_TASK_PRIORITY,		  Position_Task_Stack,		&Position_Task_TCB}, //8
	{vOperation_Task, 	"Operation_Task", 	OPERATION_TASK_STACK_SIZE, 		NULL, OPERATION_TASK_PRIORITY,		Operation_Task_Stack,		&Operation_Task_TCB},   //5
	{vLearning_Task, 		"Learning_Task", 		LEARNING_TASK_STACK_SIZE, 		NULL, LEARNING_TASK_PRIORITY,		  Learning_Task_Stack,		&Learning_Task_TCB},    //6
	{vLearning_V2_Task, "Learning_V2_Task", LEARNING_V2_TASK_STACK_SIZE,  NULL, LEARNING_V2_TASK_PRIORITY,  Learning_V2_Task_Stack, &Learning_V2_Task_TCB},
	{vETS_Task,         "ETS_Task",         ETS_TASK_STACK_SIZE,          NULL, ETS_TASK_PRIORITY,          ETS_Task_Stack,         &ETS_Task_TCB},    //6
	{vSystemCheckTask,  "System_Check_Task",SYSTEM_CHECK_TASK_STACK_SIZE, NULL, SYSTEM_CHECK_TASK_PRIORITY, SystemCheck_Task_Stack,  &SystemCheck_Task_TCB }
};
const uint32_t TaskParamsCount = sizeof(TaskParams) / sizeof(TaskParams_t);

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void PeriphCommonClock_Config(void);
/* USER CODE BEGIN PFP */
static void SystemClock_ConfigUser(void);
static void PeriphCommonClock_ConfigUser(void);
/* USER CODE END PFP */
static void vInitTask(void *pvParameters);
static void Hardware_Init(void);
static void Modules_Init(void);


/* Private user code ---------------------------------------------------------*/
static void Hardware_Init(void)
{
  /* Initialize all configured peripherals */
  /* USER CODE BEGIN 2 */
   MX_SPI1_Init();
   MX_SPI2_Init();
   MX_SPI4_Init();
   MX_SPI5_Init();
   MX_SPI6_Init();

#if !TEMP_EFLASH_LOADER_PARAM_INFO
   Watchdog_Init();
#endif

   GPIO_Init();
   Timer2_Init();
   Timer3_Init();
   Timer5_Init();

   UART7_Init();
   ULOG_INIT();
   ULOG_SUBSCRIBE((ulog_function_t)&car_system_logger, ULOG_TRACE_LEVEL);

   CRC_Init();

   CAN1_Init();
   CAN2_Init();

   TimerEvt_TimerInit();
}

static void Modules_Init(void)
{
	FaultAlarm_Init();
	CarNet_Init();
	RTC_Init();
}
static void vInitTask(void *pvParameters)
{
	//mr_error_code_t errCode;

#if configUSE_TRACE_FACILITY == 1
	CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk;
	DWT->LAR = 0xC5ACCE55;
	DWT->CYCCNT = 0;
	DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk;

	SEGGER_SYSVIEW_Conf();
	SEGGER_SYSVIEW_Start();
#endif

	Hardware_Init();
	System_SetNodeID(SYS_NODE__CT_A7);
	//errCode = FS_Setup();

#if TEMP_EFLASH_LOADER_PARAM_INFO
   while(1);
#endif

// TODO ks review with Alfonso if this can be the default
//#ifndef RELEASE_CONFIGURATION
   /* AS todo for now CTA will stall here if missing user configuration
    * User will need to manually add CTA params to prevent stall on new boards.
    * This is for cases when a MR board is broken and needs to be replaced.
    * This is a temporary solution to avoid a blank MRA board from overwriting a CTA board with good parameters.
    * When the mechanism to transfer CTA parameters to MRA is established, if CTA has a good copy and MRA has an empty copy,
    * CTA will transmit to MRA user parameters
    */
//   APP_ERROR_CHECK(errCode);
//#else
// Latching fault rather than terminate wherever possible in production app to minimize debugging headaches
//   if(errCode == MR_SUCCESS) {
//      PARAMETER_SetUserParamSuccessFlag_CTA(ACTIVE);
//   }
//#endif

   Param_Init();


	Modules_Init();
	/* Call the init function of all modules  */
	vIdleTaskModulesInit();

	/* Initialize tasks */
	/* TODO: Change for static task allocation*/

	for(uint32_t i = 0; i < TaskParamsCount; i++)
	{
		TaskParams_t *task = &TaskParams[i];
		xTaskCreateStatic(task->function,
				task->name,
				task->stack_size,
				task->parameters,
				task->priority,
				task->stack_uffer,
				task->task_buffer);
	}

	/*Delete this task */
	vTaskDelete(NULL);
}

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* MCU Configuration--------------------------------------------------------*/
  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */
  SystemClock_ConfigUser();
  PeriphCommonClock_ConfigUser();
  /* USER CODE END Init */


  /* USER CODE END Boot_Mode_Sequence_2 */

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

   /* Initialize all subroutines */

   xTaskCreateStatic(vInitTask,
   			"InitTask",
			configMINIMAL_STACK_SIZE,
   			NULL,
   			tskIDLE_PRIORITY,
			Init_Task_Stack,          // Array to use as the task's stack.
   			&Init_Task_TCB);

   vTaskStartScheduler();
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
   while(1) {

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
   }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Supply configuration update enable
  */
  HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}

  __HAL_RCC_SYSCFG_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE0);

  while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {}

  /** Macro to configure the PLL clock source
  */
  __HAL_RCC_PLL_PLLSOURCE_CONFIG(RCC_PLLSOURCE_HSE);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI|RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_BYPASS;
  RCC_OscInitStruct.LSIState = RCC_LSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 5;
  RCC_OscInitStruct.PLL.PLLN = 192;
  RCC_OscInitStruct.PLL.PLLP = 2;
  RCC_OscInitStruct.PLL.PLLQ = 20;
  RCC_OscInitStruct.PLL.PLLR = 2;
  RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_2;
  RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
  RCC_OscInitStruct.PLL.PLLFRACN = 0;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2
                              |RCC_CLOCKTYPE_D3PCLK1|RCC_CLOCKTYPE_D1PCLK1;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;
  RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief Peripherals Common Clock Configuration
  * @retval None
  */
void PeriphCommonClock_Config(void)
{
  RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = {0};

  /** Initializes the peripherals clock
  */
  PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_SPI6|RCC_PERIPHCLK_SPI2
                              |RCC_PERIPHCLK_SPI1|RCC_PERIPHCLK_SPI4
                              |RCC_PERIPHCLK_SPI5;
  PeriphClkInitStruct.PLL3.PLL3M = 5;
  PeriphClkInitStruct.PLL3.PLL3N = 48;
  PeriphClkInitStruct.PLL3.PLL3P = 3;
  PeriphClkInitStruct.PLL3.PLL3Q = 3;
  PeriphClkInitStruct.PLL3.PLL3R = 2;
  PeriphClkInitStruct.PLL3.PLL3RGE = RCC_PLL3VCIRANGE_2;
  PeriphClkInitStruct.PLL3.PLL3VCOSEL = RCC_PLL3VCOWIDE;
  PeriphClkInitStruct.PLL3.PLL3FRACN = 0;
  PeriphClkInitStruct.Spi123ClockSelection = RCC_SPI123CLKSOURCE_PLL3;
  PeriphClkInitStruct.Spi45ClockSelection = RCC_SPI45CLKSOURCE_PLL3;
  PeriphClkInitStruct.Spi6ClockSelection = RCC_SPI6CLKSOURCE_PLL3;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */
/**
 * @brief System Clock Configuration
 * @retval None
 */
static void SystemClock_ConfigUser(void) {
   RCC_OscInitTypeDef RCC_OscInitStruct = { 0 };
   RCC_ClkInitTypeDef RCC_ClkInitStruct = { 0 };

   /** Supply configuration update enable
    */
   HAL_PWREx_ConfigSupply(PWR_LDO_SUPPLY);

   /** Configure the main internal regulator output voltage
    */
   __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

   while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
   }

   __HAL_RCC_SYSCFG_CLK_ENABLE();
   __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE0);

   while(!__HAL_PWR_GET_FLAG(PWR_FLAG_VOSRDY)) {
   }

   /** Configure LSE Drive Capability
    */
   HAL_PWR_EnableBkUpAccess();
   __HAL_RCC_LSEDRIVE_CONFIG(RCC_LSEDRIVE_HIGH);

   /** Macro to configure the PLL clock source
    */
   __HAL_RCC_PLL_PLLSOURCE_CONFIG(RCC_PLLSOURCE_HSE);

   /** Initializes the RCC Oscillators according to the specified parameters
    * in the RCC_OscInitTypeDef structure.
    */
   RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_HSE;
   RCC_OscInitStruct.HSEState = RCC_HSE_BYPASS;
   RCC_OscInitStruct.LSIState = RCC_LSI_ON;
   RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
   RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;// 25 MHZ
   RCC_OscInitStruct.PLL.PLLM = 5;// PLLM_CLK = HSE_CLK / 5 = 9.6 MHZ -> PPLN multiplier
   RCC_OscInitStruct.PLL.PLLN = 192;// PLLN_CLK = 960 MHZ -> PQR dividers
   RCC_OscInitStruct.PLL.PLLP = 2;// PLLN_CLK/2 = 480 MHZ -> SYSCLK
   RCC_OscInitStruct.PLL.PLLQ = 20;// PLLN_CLK/20 = 48 MHZ -> CAN / USB
   RCC_OscInitStruct.PLL.PLLR = 2;// N/A
   RCC_OscInitStruct.PLL.PLLRGE = RCC_PLL1VCIRANGE_3;
   RCC_OscInitStruct.PLL.PLLVCOSEL = RCC_PLL1VCOWIDE;
   RCC_OscInitStruct.PLL.PLLFRACN = 0;
   if(HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK) {
      Error_Handler();
   }

   /** Initializes the CPU, AHB and APB buses clocks
    */
   RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK
                                 | RCC_CLOCKTYPE_SYSCLK
                                 | RCC_CLOCKTYPE_PCLK1
                                 | RCC_CLOCKTYPE_PCLK2
                                 | RCC_CLOCKTYPE_D3PCLK1
                                 | RCC_CLOCKTYPE_D1PCLK1;
   RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
   RCC_ClkInitStruct.SYSCLKDivider = RCC_SYSCLK_DIV1;// CPU_CLK = 480 MHZ -> CPU_CLK, SYS_TICK_CLK
   RCC_ClkInitStruct.AHBCLKDivider = RCC_HCLK_DIV2;// HCLK = SYS_CLK/2 = 240 MHZ -> CPU2_CLK, SYS_TICK_CLK2, APB/AHB dividers
   RCC_ClkInitStruct.APB3CLKDivider = RCC_APB3_DIV2;// APB3_CLK = HCLK/2 = 120 MHZ
   RCC_ClkInitStruct.APB1CLKDivider = RCC_APB1_DIV2;// APB1_CLK = HCLK/2 = 120 MHZ, -> 240 MHz TIM1_CLK
   RCC_ClkInitStruct.APB2CLKDivider = RCC_APB2_DIV2;// APB2_CLK = HCLK/2 = 120 MHZ, -> 240 MHz TIM2_CLK
   RCC_ClkInitStruct.APB4CLKDivider = RCC_APB4_DIV2;// APB4_CLK = HCLK/2 = 120 MHZ, -> 240 MHz peripheral bus

   if(HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_4) != HAL_OK) {
      Error_Handler();
   }
}

/**
 * @brief Peripherals Common Clock Configuration
 * @retval None
 */
static void PeriphCommonClock_ConfigUser(void) {
   RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = { 0 };

   /** Initializes the peripherals clock
    */
   PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_SPI1
                                              | RCC_PERIPHCLK_SPI2
                                              | RCC_PERIPHCLK_SPI4
                                              | RCC_PERIPHCLK_SPI5
                                              | RCC_PERIPHCLK_SPI6
                                              | RCC_PERIPHCLK_UART7
                                              | RCC_PERIPHCLK_FDCAN;
   PeriphClkInitStruct.PLL2.PLL2M = 5;
   PeriphClkInitStruct.PLL2.PLL2N = 160;// PLL2N_CLK = 800 MHZ -> PQR divisors
   PeriphClkInitStruct.PLL2.PLL2P = 4;// PLL2P_CLK = PLL2N_CLK/4 = 200 MHz -> ?
   PeriphClkInitStruct.PLL2.PLL2Q = 4;// PLL2Q_CLK = PLL2N_CLK/4 = 200 MHz -> ?
   PeriphClkInitStruct.PLL2.PLL2R = 4;// PLL2R_CLK = PLL2N_CLK/2 = 200 MHz -> QSPI / FMC
   PeriphClkInitStruct.PLL2.PLL2RGE = RCC_PLL2VCIRANGE_3;
   PeriphClkInitStruct.PLL2.PLL2VCOSEL = RCC_PLL2VCOWIDE;
   PeriphClkInitStruct.PLL2.PLL2FRACN = 0;

   PeriphClkInitStruct.PLL3.PLL3M = 5;
   PeriphClkInitStruct.PLL3.PLL3N = 48;// PLL3N_CLK = 240 MHZ -> PQR divisors
   PeriphClkInitStruct.PLL3.PLL3P = 3;// PLL3P_CLK = PLL3N_CLK/3 = 80 MHz -> SPI123
   PeriphClkInitStruct.PLL3.PLL3Q = 3;// PLL3Q_CLK = PLL3N_CLK/3 = 80 MHz -> SPI456
   PeriphClkInitStruct.PLL3.PLL3R = 7;// PLL3R_CLK = PLL3N_CLK/7 = 34.285714 MHz -> LTDC (Try higher so it can share a node with I2C?)
   PeriphClkInitStruct.PLL3.PLL3RGE = RCC_PLL3VCIRANGE_3;
   PeriphClkInitStruct.PLL3.PLL3VCOSEL = RCC_PLL3VCOWIDE;
   PeriphClkInitStruct.PLL3.PLL3FRACN = 0;

   PeriphClkInitStruct.Spi123ClockSelection = RCC_SPI123CLKSOURCE_PLL3;// 80 MHz PLL3P (PLL1Q, PLL2P, PLL3P, PER_CK/HSI) -> AB / PLD
   PeriphClkInitStruct.Spi45ClockSelection = RCC_SPI45CLKSOURCE_PLL3;// 80 MHz PLL3Q (PCLK2, PLL2Q, PLL3Q, HSI) -> FRAM/UI
   PeriphClkInitStruct.Spi6ClockSelection = RCC_SPI6CLKSOURCE_PLL3;// 80 MHz PLL3Q (PCLK4, PLL2Q, PLL3Q, HSI) -> UI
   PeriphClkInitStruct.FdcanClockSelection = RCC_FDCANCLKSOURCE_PLL;// PLL1Q = 48 MHZ (HSE, PLL1Q, PLL2Q)
   PeriphClkInitStruct.Usart234578ClockSelection = RCC_USART234578CLKSOURCE_PLL3;
   // USB = 48 MHZ PLL1Q (PLL1Q, PLL3Q, RC48)
// TIMERS - PCLK1,4,4
// I2C1/2/3 = 100(PCLK1, PLL3R, HSI) -> UI GT911 (touchscreen?) -> make HSI 64MHZ
// I2C4 = PCLK4 / PLL3R / HSI) -> N/A
// LTDC = DIVR3 (33.482)
// QSPI = 200(HCLK3, PLL1Q, PLL2R, PER_CK/HSI)
// ADC = (PLL2P, PLL3R, PER_CK)
// USART 2/3/4/5/7/8 (PCLK1, PLL2Q, PLL3Q, HSI, CSI, LSE)
// FMC = 200 (HCLK3, PLL1Q, PLL2R, PER_CK/HSI)
   if(HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK) {
      Error_Handler();
   }
}
/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
   /* User can add his own implementation to report the HAL error return state */
   __disable_irq();
   while(1) {
   }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */

void vApplicationStackOverflowHook(TaskHandle_t xTask, char *pcTaskName)
{
	HALT_IF_DEBUGGING();
}

