/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32g4xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "app.h"
#include "riser.h"
#include "expansion.h"
/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */
#define DIP_ADDRESS__COP								(0x00)
#define DIP_ADDRESS__EXP_M1								(0x01)
#define DIP_ADDRESS__EXP_M2								(0x05)
#define DIP_ADDRESS__EXP_M3								(0x09)
#define DIP_ADDRESS__EXP_M4								(0x0D)
#define DIP_ADDRESS__EXP_M5								(0x11)
#define DIP_ADDRESS__RISER1								(0x41)
#define DIP_ADDRESS__RISER2								(0x42)
#define DIP_ADDRESS__RISER3								(0x43)
#define DIP_ADDRESS__RISER4								(0x44)
#define DIP_ADDRESS__TEST								(0x7F)
/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */
typedef enum
{
	BOARD_INVALID,
	BOARD_COP,
	BOARD_EXP_MASTER,
	BOARD_EXP_SLAVE,
	BOARD_RISER,
	BOARD_TEST,

	NUM_LOCAL_BOARD_TYPES
}en_local_board_type;
/* Exported functions prototypes ---------------------------------------------*/
en_expansion_id Main_GetLocalExpansionID(void);
en_riser_id Main_GetLocalRiserID(void);
en_local_board_type Main_GetLocalBoardType(void);
uint8_t Main_GetStartupDipAddress(void);
void Error_Handler(void);

/* USER CODE BEGIN EFP */

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
