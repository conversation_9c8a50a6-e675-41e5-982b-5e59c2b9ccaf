/*******************************************************************************
 * @Copyright (C) 2023 by Vantage Elevation
 * @file           : sr_CAN1.h
 * @version 		  : 1.0.0
 * @brief          : CAN1 subroutine
 * @details		  : CAN1 subroutine
 ********************************************************************************/
#ifndef _SR_CAN1_H_
#define _SR_CAN1_H_

/******************************************************************************
 * Includes
 *******************************************************************************/
#include "network.h"
/******************************************************************************
 * Preprocessor Constants
 *******************************************************************************/

/******************************************************************************
 * Configuration Constants
 *******************************************************************************/

/******************************************************************************
 * Macros
 *******************************************************************************/

/******************************************************************************
 * Typedefs
 *******************************************************************************/

/******************************************************************************
 * Global Variables
 *******************************************************************************/
/******************************************************************************
 * Function Prototypes
 *******************************************************************************/
en_pass_fail CAN1_TransmitDatagram(st_CAN_msg *pstMsg);
uint8_t CAN1_CheckIfCommLoss(void);
//en_pass_fail CAN_ExpMessageInRange(uint16_t uwDatagramID);
void vCAN1_Task(void *pvParams);
#endif /* _SR_CAN1_H_ */
