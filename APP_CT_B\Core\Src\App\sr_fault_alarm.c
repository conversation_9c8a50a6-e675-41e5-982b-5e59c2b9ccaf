/****************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file sr_Fault.c
* @version 1.0.0
* @brief Subroutine updating processor's fault status
* @details Subroutine updating processor's fault status
*****************************************************************************/

/******************************************************************************
* Includes
*******************************************************************************/
#include "sr_fault_alarm.h"
#include "sr_position.h"
#include "car_input_def.h"
#include "car_faults_def.h"
#include "car_alarms_def.h"
#include "stm32g4xx_hal.h"
/******************************************************************************
* Function Prototypes
*******************************************************************************/

/******************************************************************************
* Module Preprocessor Constants
*******************************************************************************/
#define SUBROUTINE_FIRST_RUN_DELAY_1MS                            (1000)
#define SUBROUTINE_RUN_INTERVAL_1MS                               (50)

/******************************************************************************
* Module Preprocessor Macros
*******************************************************************************/


/******************************************************************************
* Module Typedefs
*******************************************************************************/

/******************************************************************************
* Global Variable Definitions
*******************************************************************************/

/******************************************************************************
* Module Variable Definitions
*******************************************************************************/

/******************************************************************************
* Function Definitions
*******************************************************************************/
/* @fn static void CheckForResetFault(void)
 * @brief Checks startup register to diagnose what caused the device to power off and asserts the corresponding fault
 * @param None
 * @return None
 */
static void CheckForResetFault(void)
{
	en_sys_node eNode = System_GetNodeID();
	/* 9.4.4 RCC reset status register (RCC_RSR) */
	uint32_t uiRSR = RCC->CSR;
	if( ( uiRSR & RCC_CSR_IWDGRSTF ) != 0 ) // If watchdog reset
	{
	   switch(eNode)
	   {
	      case SYS_NODE__MR_A7: Faults_SetFault(CFLT__MR_A7_WDT_RESET); break;
	      case SYS_NODE__MR_B4: Faults_SetFault(CFLT__MR_B4_WDT_RESET); break;
	      case SYS_NODE__CT_A7: Faults_SetFault(CFLT__CT_A7_WDT_RESET); break;
	      case SYS_NODE__CT_B4: Faults_SetFault(CFLT__CT_B4_WDT_RESET); break;
	      case SYS_NODE__COP_A4: Faults_SetFault(CFLT__COP_A4_WDT_RESET); break;
	      default: break;
	   }
	}
	else if( ( uiRSR & RCC_CSR_BORRSTF ) != 0 ) // If low power (brown out) reset
	{
	   switch(eNode)
	   {
	      case SYS_NODE__MR_A7: Faults_SetFault(CFLT__MR_A7_BOD_RESET); break;
	      case SYS_NODE__MR_B4: Faults_SetFault(CFLT__MR_B4_BOD_RESET); break;
	      case SYS_NODE__CT_A7: Faults_SetFault(CFLT__CT_A7_BOD_RESET); break;
	      case SYS_NODE__CT_B4: Faults_SetFault(CFLT__CT_B4_BOD_RESET); break;
	      case SYS_NODE__COP_A4: Faults_SetFault(CFLT__COP_A4_BOD_RESET); break;
	      default: break;
	   }
	}
	else // Assume all other sources are regular power off events
	{
	   switch(eNode)
	   {
	      case SYS_NODE__MR_A7: Faults_SetFault(CFLT__MR_A7_POWER_RESET); break;
	      case SYS_NODE__MR_B4: Faults_SetFault(CFLT__MR_B4_POWER_RESET); break;
	      case SYS_NODE__CT_A7: Faults_SetFault(CFLT__CT_A7_POWER_RESET); break;
	      case SYS_NODE__CT_B4: Faults_SetFault(CFLT__CT_B4_POWER_RESET); break;
	      case SYS_NODE__COP_A4: Faults_SetFault(CFLT__COP_A4_POWER_RESET); break;
	      default: break;
	   }
	}

	/* Reset status register by writing to the RMVF bit */
	RCC->CSR = uiRSR | RCC_CSR_RMVF;
}

/* @fn  void Init(void)
 * @brief Initialization function for the subroutine's run interval, first run delay, and state machine variables
 * @param None
 * @return None
 */
void Fault_Init(void)
{
    CheckForResetFault();
}

void vFaultAlarm_Task(void *pvParams)
{
	static uint8_t bLastResetBtn;
	uint8_t bResetBtn;
	// KW suppress INFINITE_LOOP.LOCAL: This loop is required for an embedded system task
	while(1)
	{
		bResetBtn = CarInput_GetInputValue(CIN__FAULT_RST);

		Faults_UpdateLatchedFault(SUBROUTINE_RUN_INTERVAL_1MS);
		Alarms_UpdateLatchedAlarm(SUBROUTINE_RUN_INTERVAL_1MS);
		/* Initiates reset command for LES02 channel based through the Fault Reset Button.*/
		en_car_fault eFault = Position_GetLandingSystemFault();
		en_car_alarm eAlarm = Position_GetLandingSystemAlarm();
		if( ( eFault != CFLT__NONE )
		 || ( eAlarm != CALM__NONE ) )
		{
			if( !bLastResetBtn && bResetBtn )
			{
				Position_SetSelectorReset(ACTIVE);
			}
		}
		bLastResetBtn = bResetBtn;

		vTaskDelay(FAULT_TASK_DELAY);
	}

}
